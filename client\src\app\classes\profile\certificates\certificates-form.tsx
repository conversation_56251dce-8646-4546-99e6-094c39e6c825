"use client";
import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { axiosInstance } from "@/lib/axios";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";
import { fetchClassDetails } from "@/store/thunks/classThunks";
import { useRouter } from "next/navigation";
import { Trash2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";

const certificateSchema = z.object({
  title: z.string().min(2, "Certificate title is required"),
  file: z.custom<any>(
    (files) => files instanceof FileList && files.length > 0,
    {
      message: "Certificate file is required",
    }
  ),
});

const schema = z.object({
  noCertificates: z.boolean().optional(),
  certificates: z.array(certificateSchema).optional(),
});

type FormValues = z.infer<typeof schema>;
const user = JSON.parse(localStorage.getItem('user') || '{}')

export function CertificateForm() {
  const [noCertificates, setNoCertificates] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      noCertificates: false,
      certificates: [
        {
          title: "",
          file: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "certificates",
  });

  const onSubmit = async (data: FormValues) => {
    // If noCertificates is checked, use the dedicated handler
    if (data.noCertificates) {
      handleNoCertificateSubmit();
      return;
    }

    // If noCertificates is false, validate that certificate data is provided
    if (!data.certificates || data.certificates.length === 0) {
      toast.error("Please add at least one certificate record");
      return;
    }

    const formData = new FormData();
    formData.append("noCertificates", "false");

    formData.append("certificates", JSON.stringify(data.certificates));

    data.certificates.forEach((cert) => {
      if (cert.file instanceof FileList) {
        formData.append("files", cert.file[0]);
      }
    });

    try {
      await axiosInstance.post(
        `/classes-profile/certificates`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("Certificates uploaded successfully");
      dispatch(completeForm(FormId.CERTIFICATES));
      router.push("/classes/profile/tution-class");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  const classData = useSelector((state: RootState) => state.class.classData);

  // Initialize form with existing data
  React.useEffect(() => {
    if (classData && !isInitialized) {
      const hasCertificateSetToFalse = classData.certificates?.some((cert: any) => cert.isCertificate === false);

      if (hasCertificateSetToFalse) {
        // User previously selected "I don't have any certificates"
        setNoCertificates(true);
        form.setValue('noCertificates', true);

        // Clear any certificate data that might be in the form
        form.setValue('certificates', []);

        // Show a message to the user
        toast.info("You have selected 'I don't have any certificates'. You cannot add certificate data unless you uncheck this option.");
      }

      setIsInitialized(true);
    }
  }, [classData, form, isInitialized]);

  // Handle submission when user checks "I don't have any certificates"
  const handleNoCertificateSubmit = async () => {
    const formData = new FormData();
    formData.append("noCertificates", "true");
    // We don't include any certificate data when noCertificates is true

    try {
      await axiosInstance.post(
        `/classes-profile/certificates`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("No certificates status saved");
      dispatch(completeForm(FormId.CERTIFICATES));
      router.push("/classes/profile/tution-class");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  // Handle when user unchecks "I don't have any certificates"
  const handleNoCertificateUncheck = async () => {
    const formData = new FormData();
    formData.append("noCertificates", "false");
    // We don't include any certificate data, just clearing the no-certificate status

    try {
      await axiosInstance.post(
        `/classes-profile/certificates`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("You can now add your certificate details");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  const handleDeleteCertificate = async (certId: string, classId: string) => {
    try {
      await axiosInstance.delete(`/classes-profile/certificate/${certId}`, {
        data: { classId }
      });
      toast.success("Certificate deleted successfully");
      await dispatch(fetchClassDetails(classId));

      // Reset form to initial state after deletion
      form.reset({
        noCertificates: false,
        certificates: [
          {
            title: "",
            file: undefined,
          },
        ],
      });
    } catch (error) {
      toast.error("Failed to delete certificate");
      console.log(error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* No Certificates Checkbox */}
        <FormField
          control={form.control}
          name="noCertificates"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    setNoCertificates(!!checked);

                    // If checked, proceed to next form
                    if (checked) {
                      // Submit the form automatically when checkbox is checked
                      handleNoCertificateSubmit();
                    } else {
                      // If unchecked, clear the no-certificate status in the database
                      handleNoCertificateUncheck();
                    }
                  }}
                />
              </FormControl>
              <FormLabel className="font-medium">
                I dont have any certificates
              </FormLabel>
            </FormItem>
          )}
        />

        {classData?.certificates?.length > 0 && !noCertificates && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Previous Certificates</h3>
            {classData.certificates.map((cert: any, idx: any) => (
              <div
                key={idx}
                className="rounded-2xl border bg-muted/20 p-4 shadow-sm space-y-1"
              >
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <p className="font-medium">{cert.title}</p>
                    {cert.certificateUrl && (
                      <a
                        href={`${process.env.NEXT_PUBLIC_API_BASE_URL}uploads/classes/${classData.id}/certificates/${cert.certificateUrl}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 underline"
                      >
                        View Uploaded Certificate
                      </a>
                    )}
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>Delete Certificate</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this certificate? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter className="gap-2">
                        <Button
                          variant="outline"
                          onClick={() => (document.querySelector('button[data-state="open"]') as any).click()}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => {
                            handleDeleteCertificate(cert.id, classData.id);
                            (document.querySelector('button[data-state="open"]') as any).click();
                          }}
                        >
                          Delete
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            ))}
          </div>
        )}

        {!noCertificates &&
          fields.map((item, index) => (
            <div
              key={item.id}
              className="space-y-4 rounded-2xl border bg-muted/30 p-4 shadow-sm"
            >
              <FormField
                control={form.control}
                name={`certificates.${index}.title`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Certificate Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g. Teaching Excellence"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`certificates.${index}.file`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Upload Certificate</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => {
                          const files = e.target.files;
                          if (files && files.length > 0) {
                            const file = files[0];
                            const allowedTypes = ["application/pdf", "image/jpeg", "image/jpg", "image/png"];
                            if (!allowedTypes.includes(file.type)) {
                              toast.error("Only PDF and image files (.pdf, .jpg, .jpeg, .png) are allowed");
                              e.target.value = ""; // Clear the input
                              return;
                            }
                            field.onChange(files);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => remove(index)}
                >
                  Remove
                </Button>
              )}
            </div>
          ))}

        {/* Add new certificate */}
        {!noCertificates && (
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              append({
                title: "",
                file: undefined,
              })
            }
            className="flex items-center gap-2"
          >
            Add New Certificate
          </Button>
        )}

        <Button type="submit">
          Save Certificates
        </Button>
      </form>
    </Form>
  );
}
