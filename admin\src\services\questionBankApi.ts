import axiosInstance from "../lib/axios";
import { QuestionBankInput } from "../lib/types";

//Get all Question Bank Detail
export const getQuestionBank = async (
  page: number = 1,
  limit: number = 10,
  filters: {
    medium?: string;
    standard?: string;
    level?: string;
    subject?: string;
  } = {}
): Promise<any> => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(filters.medium && { medium: filters.medium }),
      ...(filters.standard && { standard: filters.standard }),
      ...(filters.level && { level: filters.level }),
      ...(filters.subject && { subject: filters.subject }),
    }).toString();

    const response = await axiosInstance.get(`/questionBank?${queryParams}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    console.log("Get Question Bank Response:", response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//Get all Subject From Constant Table
export const getSubject = async (): Promise<any> => {
  try {
    const response = await axiosInstance.get("/constant/Subject");
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch subject data: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//Get all Standard From Constant Table
export const getClassroom = async (): Promise<any> => {
  try {
    const response = await axiosInstance.get("/constant/classroom");
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch classroom Data: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const createQuestionBank = async (
  data: QuestionBankInput
): Promise<any> => {
  try {
    const response = await axiosInstance.post("/questionBank", data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const updateQuestionBank = async (
  id: string,
  data: QuestionBankInput
): Promise<any> => {
  try {
    const response = await axiosInstance.put(`/questionBank/${id}`, data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const deleteQuestionBank = async (id: string): Promise<any> => {
  try {
    const response = await axiosInstance.delete(`/questionBank/${id}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const downloadQuestionBankExcel = async (
  filters: {
    medium?: string;
    standard?: string;
    level?: string;
    subject?: string;
  } = {}
) => {
  try {
    const queryParams = new URLSearchParams({
      ...(filters.medium && { medium: filters.medium }),
      ...(filters.standard && { standard: filters.standard }),
      ...(filters.level && { level: filters.level }),
      ...(filters.subject && { subject: filters.subject }),
    }).toString();

    const url = `/export/question-bank${queryParams ? `?${queryParams}` : ''}`;

    const response = await axiosInstance.get(url, {
      headers: {
        "Server-Select": "uwhizServer",
      },
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', 'question-bank.xlsx');
    document.body.appendChild(link);
    link.click();

    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return true;
  } catch (error: any) {
    console.error('Error downloading Excel file:', error);
    throw new Error(`Failed to download Excel file: ${error.message}`);
  }
};

export const uploadQuestionBankExcel = async (file: File): Promise<any> => {
  try {
    const formData = new FormData();
    formData.append('excelFile', file);

    const response = await axiosInstance.post('/questionBank/upload-excel', formData, {
      headers: {
        "Server-Select": "uwhizServer",
      },
      timeout: 30000,
    });

    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to upload Excel file: ${
        error.response?.data?.message || error.response?.data?.error || error.message
      }`,
    };
  }
};

export const deleteManyQuestions = async (ids: string[]): Promise<any> => {
  try {
    const response = await axiosInstance.delete('/questionBank/bulk/delete', {
      data: { ids },
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete questions: ${error.response?.data?.message || error.message}`,
    };
  }
};