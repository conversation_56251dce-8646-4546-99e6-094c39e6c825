"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Blog } from '@/lib/types';
import { CalendarDays, User, BookOpen } from 'lucide-react';

interface BlogCardProps {
  blog: Blog;
}

const BlogCard = ({ blog }: BlogCardProps) => {
  const baseUrl = (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005').replace(/\/$/, '');
  const imagePath = blog.blogImage ? (blog.blogImage.startsWith('/') ? blog.blogImage : `/${blog.blogImage}`) : '';
  const imageUrl = blog.blogImage ? `${baseUrl}${imagePath}` : '';

  const truncateDescription = (description: string) => {
    const plainText = description.replace(/<[^>]*>/g, '');
    return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText;
  };

  const formattedDate = new Date(blog.createdAt).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  return (
    <div className="h-full overflow-hidden flex flex-col group rounded-xl bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
      <div className="relative h-52 w-full overflow-hidden rounded-t-xl">
        {blog.blogImage ? (
          <div className="relative w-full h-full">
            <Image
              src={imageUrl}
              alt={blog.blogTitle}
              className="object-cover transition-transform duration-500 group-hover:scale-110"
              fill
              sizes="(max-width: 768px) 100vw, 33vw"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        ) : (
          <div className="w-full h-full bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-800 dark:to-gray-700 flex items-center justify-center">
            <span className="text-gray-400 dark:text-gray-500 text-sm">No image available</span>
          </div>
        )}

        <div className="absolute top-3 right-3 bg-white/90 dark:bg-gray-800/90 text-xs font-medium py-1.5 px-3 rounded-full shadow-md backdrop-blur-sm flex items-center gap-1.5">
          <CalendarDays className="w-3.5 h-3.5 text-[#FD904B]" />
          {formattedDate}
        </div>
      </div>

      <div className="p-6 flex flex-col flex-grow">
        <h3 className="text-xl font-bold mb-3 line-clamp-2 group-hover:text-[#FD904B] transition-colors duration-300">{blog.blogTitle}</h3>

        {blog.class && (
          <div className="flex items-center gap-3 text-xs text-muted-foreground mb-4">
            <div className="flex items-center gap-1.5">
              <User className="w-3.5 h-3.5 text-[#FD904B]" />
              <span>{blog.class.firstName} {blog.class.lastName}</span>
            </div>
            <span className="text-gray-300">•</span>
            <div className="flex items-center gap-1.5">
              <BookOpen className="w-3.5 h-3.5 text-[#FD904B]" />
              <span>{blog.class.className}</span>
            </div>
          </div>
        )}

        <div className="relative mb-6">
          <p className="text-muted-foreground line-clamp-3 text-sm leading-relaxed">
            {truncateDescription(blog.blogDescription)}
          </p>
          <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white dark:from-gray-900 to-transparent"></div>
        </div>

        <div className="mt-auto pt-2">
          <Link href={`/blogs/${blog.id}`} passHref>
            <Button
              variant="outline"
              className="w-full bg-transparent border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B] hover:text-white transition-all duration-300 font-medium"
            >
              Read More
            </Button>
          </Link>
        </div>
      </div>

      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-[#FD904B] to-[#FD904B]/60"></div>
    </div>
  );
};

export default BlogCard;
