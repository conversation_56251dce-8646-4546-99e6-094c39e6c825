import { Request, Response } from "express";
import {getUwhizTerminatedStudents}  from "../services/getUwhizTeminatedStudentServices";

export const getUwhizTerminatedStudentsController = async (req: Request, res: Response) => {
  try {
    const { applicantIds } = req.body;

    const data = await getUwhizTerminatedStudents(applicantIds);
    res.json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching user details" });
  }
};
