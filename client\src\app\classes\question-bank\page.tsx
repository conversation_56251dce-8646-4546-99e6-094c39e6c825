'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  ColumnDef,
} from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  <PERSON>ertDialog<PERSON>it<PERSON>,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Pencil, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import {
  getQuestionBank,
  createQuestionBank,
  updateQuestionBank,
  deleteQuestionBank,
  getSubject,
  getClassroom,
} from '@/services/questionBankApi';
import { QuestionBank } from '@/lib/types';

const questionBankSchema = z.object({
  question: z.string().min(1, { message: 'Question is required' }).max(500, { message: 'Question cannot exceed 500 characters' }),
  optionOne: z.string().min(1, { message: 'Option 1 is required' }).max(100, { message: 'Option 1 cannot exceed 100 characters' }),
  optionTwo: z.string().min(1, { message: 'Option 2 is required' }).max(100, { message: 'Option 2 cannot exceed 100 characters' }),
  optionThree: z.string().min(1, { message: 'Option 3 is required' }).max(100, { message: 'Option 3 cannot exceed 100 characters' }),
  optionFour: z.string().min(1, { message: 'Option 4 is required' }).max(100, { message: 'Option 4 cannot exceed 100 characters' }),
  correctAnswer: z.enum(['optionOne', 'optionTwo', 'optionThree', 'optionFour'], {
    errorMap: () => ({ message: 'Please select a correct answer' }),
  }),
});

type QuestionBankFormInput = z.infer<typeof questionBankSchema>;

const defaultFormValues: QuestionBankFormInput = {
  question: '',
  optionOne: '',
  optionTwo: '',
  optionThree: '',
  optionFour: '',
  correctAnswer: 'optionOne',
};

export default function QuestionBankPage() {
  const [questions, setQuestions] = useState<QuestionBank[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<QuestionBank | null>(null);
  const [subjects, setSubjects] = useState<string[]>([]);
  const [classrooms, setClassrooms] = useState<string[]>([]);
  const [isFetchingConstants, setIsFetchingConstants] = useState(true);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [limit, setLimit] = useState(10);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [selectedMedium, setSelectedMedium] = useState<'ENGLISH' | 'GUJARATI'>('ENGLISH');
  const [selectedLevel, setSelectedLevel] = useState<'EASY' | 'MEDIUM' | 'HARD'>('EASY');
  const [selectedStandard, setSelectedStandard] = useState<string>('');
  const [selectedSubject, setSelectedSubject] = useState<string>('');
  const [classId, setClassId] = useState<string | null>(null);
  const [filtersApplied, setFiltersApplied] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<QuestionBankFormInput>({
    resolver: zodResolver(questionBankSchema),
    defaultValues: defaultFormValues,
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const userData = JSON.parse(localStorage.getItem("user") || "{}");
      setClassId(userData?.id || null);
    }
  }, []);

  const fetchQuestions = async (page: number = 1, applyFilters: boolean = false) => {
    setIsLoading(true);
    const filters = applyFilters
      ? {
          medium: selectedMedium,
          standard: selectedStandard || undefined,
          level: selectedLevel,
          subject: selectedSubject || undefined,
        }
      : {};
    const response = await getQuestionBank(page, limit, filters);
    if (response.success && response.data) {
      setQuestions(response.data.data);
      setTotalPages(response.data.pagination.totalPages);
      setTotalQuestions(response.data.pagination.totalQuestions);
      setCurrentPage(page);
    } else {
      setQuestions([]);
      setTotalPages(1);
      setTotalQuestions(0);
      toast.error(response.error || 'Failed to fetch questions');
    }
    setIsLoading(false);
  };

  const fetchConstants = async () => {
    setIsFetchingConstants(true);
    setFetchError(null);
    try {
      const subjectResponse = await getSubject();
      if (subjectResponse.success && subjectResponse.data) {
        const subjectValues = subjectResponse.data.details
          .map((detail: any) => detail.value)
          .filter((value: string) => value && value.trim() !== '');
        setSubjects(subjectValues);
        if (subjectValues.length > 0) {
          setSelectedSubject(subjectValues[0]);
        } else {
          setSelectedSubject('');
        }
      } else {
        setFetchError(subjectResponse.error || 'Failed to fetch subjects');
        setSelectedSubject('');
      }

      const classroomResponse = await getClassroom();
      if (classroomResponse.success && classroomResponse.data) {
        const classroomValues = classroomResponse.data.details
          .map((detail: any) => detail.value)
          .filter((value: string) => value && value.trim() !== '');
        setClassrooms(classroomValues);
        if (classroomValues.length > 0) {
          setSelectedStandard(classroomValues[0]);
        } else {
          setSelectedStandard('');
        }
      } else {
        setFetchError(
          (fetchError ? fetchError + '; ' : '') +
            (classroomResponse.error || 'Failed to fetch classrooms')
        );
        setSelectedStandard('');
      }
    } catch (error) {
      setFetchError('Error fetching constants');
      console.error('Error fetching constants:', error);
      setSelectedStandard('');
      setSelectedSubject('');
    } finally {
      setIsFetchingConstants(false);
    }
  };

  useEffect(() => {
    fetchConstants();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchQuestions(currentPage, filtersApplied);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, limit, filtersApplied]);

  const handleSearch = () => {
    setCurrentPage(1);
    setFiltersApplied(true);
    fetchQuestions(1, true);
  };

  const handleReset = () => {
    setSelectedMedium('ENGLISH');
    setSelectedLevel('EASY');
    setSelectedStandard(classrooms[0] || '');
    setSelectedSubject(subjects[0] || '');
    setCurrentPage(1);
    setFiltersApplied(false);
    fetchQuestions(1, false);
  };

  const onSubmit = async (data: QuestionBankFormInput) => {
    setIsSubmitting(true);
    setApiError(null);

    if (!selectedStandard || !selectedSubject) {
      setApiError('Please select a valid standard and subject.');
      toast.error('Please select a valid standard and subject.');
      setIsSubmitting(false);
      return;
    }

    if (!classId) {
      setApiError('Class ID is not available. Please ensure you are logged in.');
      toast.error('Class ID is not available. Please ensure you are logged in.');
      setIsSubmitting(false);
      return;
    }

    const completeData = {
      ...data,
      medium: selectedMedium,
      level: selectedLevel,
      standard: selectedStandard,
      subject: selectedSubject,
      classID: classId,
    };

    let response;
    if (editingQuestion) {
      response = await updateQuestionBank(editingQuestion.id.toString(), completeData);
    } else {
      response = await createQuestionBank(completeData);
    }
    if (response.success) {
      fetchQuestions(currentPage, filtersApplied);
      setIsDialogOpen(false);
      reset(defaultFormValues);
      setEditingQuestion(null);
      toast.success(editingQuestion ? 'Question updated successfully!' : 'Question created successfully!');
    } else {
      setApiError(response.error || 'Failed to save question');
      toast.error(response.error || 'Failed to save question');
    }
    setIsSubmitting(false);
  };

  const handleEdit = (question: QuestionBank) => {
    setEditingQuestion(question);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { question: q, optionOne, optionTwo, optionThree, optionFour, correctAnswer, ..._ } = question;
    reset({ question: q, optionOne, optionTwo, optionThree, optionFour, correctAnswer });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    const response = await deleteQuestionBank(id.toString());
    if (response.success) {
      fetchQuestions(currentPage, filtersApplied);
      toast.success('Question deleted successfully!');
    } else {
      toast.error(response.error || 'Failed to delete question');
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      reset(defaultFormValues);
      setEditingQuestion(null);
      setApiError(null);
    }
  };

  const handleAddQuestion = () => {
    setEditingQuestion(null);
    reset(defaultFormValues);
    setIsDialogOpen(true);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const columns: ColumnDef<QuestionBank>[] = [
    { accessorKey: 'question', header: 'Question' },
    { accessorKey: 'optionOne', header: 'Option 1' },
    { accessorKey: 'optionTwo', header: 'Option 2' },
    { accessorKey: 'optionThree', header: 'Option 3' },
    { accessorKey: 'optionFour', header: 'Option 4' },
    { accessorKey: 'correctAnswer', header: 'Correct Answer' },
    { accessorKey: 'medium', header: 'Medium' },
    { accessorKey: 'standard', header: 'Standard' },
    { accessorKey: 'subject', header: 'Subject' },
    { accessorKey: 'level', header: 'Level' },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row.original)}
            aria-label="Edit question"
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="ghost" size="sm" aria-label="Delete question">
                <Trash2 className="h-4 w-4 text-red-500" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This action cannot be undone. This will permanently delete the question.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={() => handleDelete(row.original.id)}>
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data: questions,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Question Bank</h1>
        <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
          <DialogTrigger asChild>
            <Button onClick={handleAddQuestion} aria-label="Add new question">
              Add Question
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingQuestion ? 'Update Question' : 'Create Question'}
              </DialogTitle>
            </DialogHeader>
            {fetchError ? (
              <p className="text-red-500 text-sm">
                Cannot load form due to missing data: {fetchError}
              </p>
            ) : (
              <>
                {apiError && <p className="text-red-500 text-sm">{apiError}</p>}
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Question Field */}
                  <div>
                    <label className="block text-sm font-medium">Question</label>
                    <Input
                      {...register('question')}
                      placeholder="Enter question"
                      disabled={isFetchingConstants || isSubmitting}
                      className="w-full"
                      aria-label="Question input"
                    />
                    {errors.question && (
                      <p className="text-red-500 text-sm">{errors.question.message}</p>
                    )}
                  </div>

                  {/* Options 1 and 2 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium">Option 1</label>
                      <Input
                        {...register('optionOne')}
                        placeholder="Enter option 1"
                        disabled={isFetchingConstants || isSubmitting}
                        aria-label="Option 1 input"
                      />
                      {errors.optionOne && (
                        <p className="text-red-500 text-sm">{errors.optionOne.message}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium">Option 2</label>
                      <Input
                        {...register('optionTwo')}
                        placeholder="Enter option 2"
                        disabled={isFetchingConstants || isSubmitting}
                        aria-label="Option 2 input"
                      />
                      {errors.optionTwo && (
                        <p className="text-red-500 text-sm">{errors.optionTwo.message}</p>
                      )}
                    </div>
                  </div>

                  {/* Options 3 and 4 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium">Option 3</label>
                      <Input
                        {...register('optionThree')}
                        placeholder="Enter option 3"
                        disabled={isFetchingConstants || isSubmitting}
                        aria-label="Option 3 input"
                      />
                      {errors.optionThree && (
                        <p className="text-red-500 text-sm">{errors.optionThree.message}</p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium">Option 4</label>
                      <Input
                        {...register('optionFour')}
                        placeholder="Enter option 4"
                        disabled={isFetchingConstants || isSubmitting}
                        aria-label="Option 4 input"
                      />
                      {errors.optionFour && (
                        <p className="text-red-500 text-sm">{errors.optionFour.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium">Correct Answer</label>
                      <Controller
                        name="correctAnswer"
                        control={control}
                        render={({ field }) => (
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                            disabled={isFetchingConstants || isSubmitting}
                          >
                            <SelectTrigger aria-label="Correct answer selector">
                              <SelectValue placeholder="Select correct answer" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="optionOne">Option 1</SelectItem>
                              <SelectItem value="optionTwo">Option 2</SelectItem>
                              <SelectItem value="optionThree">Option 3</SelectItem>
                              <SelectItem value="optionFour">Option 4</SelectItem>
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errors.correctAnswer && (
                        <p className="text-red-500 text-sm">{errors.correctAnswer.message}</p>
                      )}
                    </div>
                  </div>

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => handleDialogOpenChange(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isFetchingConstants || isSubmitting}>
                      {isSubmitting ? 'Saving...' : 'Save'}
                    </Button>
                  </DialogFooter>
                </form>
              </>
            )}
          </DialogContent>
        </Dialog>
      </div>

        <div className="flex flex-col gap-5 mb-4 w-full">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-5 w-full">
                <div className="w-full">
                  <label className="block text-sm font-medium">Medium</label>
                  <Select
                    onValueChange={(value: 'ENGLISH' | 'GUJARATI') => setSelectedMedium(value)}
                    value={selectedMedium}
                    disabled={isFetchingConstants}
                  >
                    <SelectTrigger aria-label="Medium selector" className="w-full">
                      <SelectValue placeholder="Select medium" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ENGLISH">English</SelectItem>
                      <SelectItem value="GUJARATI">Gujarati</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-full">
                  <label className="block text-sm font-medium">Level</label>
                  <Select
                    onValueChange={(value: 'EASY' | 'MEDIUM' | 'HARD') => setSelectedLevel(value)}
                    value={selectedLevel}
                    disabled={isFetchingConstants}
                  >
                    <SelectTrigger aria-label="Level selector" className="w-full">
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EASY">Easy</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HARD">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-full">
                  <label className="block text-sm font-medium">Standard</label>
                  <Select
                    onValueChange={(value: string) => setSelectedStandard(value)}
                    value={selectedStandard}
                    disabled={isFetchingConstants || classrooms.length === 0}
                  >
                    <SelectTrigger aria-label="Standard selector" className="w-full">
                      <SelectValue
                        placeholder={
                          isFetchingConstants
                            ? 'Loading standards...'
                            : classrooms.length === 0
                              ? 'No standards available'
                              : 'Select standard'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {classrooms.length > 0 ? (
                        classrooms.map((classroom) => (
                          <SelectItem key={classroom} value={classroom}>
                            {classroom}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-standards" disabled>
                          No standards available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-full">
                  <label className="block text-sm font-medium">Subject</label>
                  <Select
                    onValueChange={(value: string) => setSelectedSubject(value)}
                    value={selectedSubject}
                    disabled={isFetchingConstants || subjects.length === 0}
                  >
                    <SelectTrigger aria-label="Subject selector" className="w-full">
                      <SelectValue
                        placeholder={
                          isFetchingConstants
                            ? 'Loading subjects...'
                            : subjects.length === 0
                              ? 'No subjects available'
                              : 'Select subject'
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.length > 0 ? (
                        subjects.map((subject) => (
                          <SelectItem key={subject} value={subject}>
                            {subject}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-subjects" disabled>
                          No subjects available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-5 justify-start">
                <Button onClick={handleSearch} disabled={isFetchingConstants || isLoading}>
                  Search
                </Button>
                <Button variant="outline" onClick={handleReset} disabled={isFetchingConstants || isLoading}>
                  Reset
                </Button>
              </div>
            </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center">
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows.length > 0 ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center">
                  No data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex justify-between items-center mt-4">
        <div className="text-sm text-gray-600">
          {totalQuestions} entries
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={limit.toString()}
            onValueChange={(value) => {
              setLimit(Number(value));
              setCurrentPage(1);
            }}
          >
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviousPage}
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextPage}
            disabled={currentPage === totalPages}
            aria-label="Next page"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}