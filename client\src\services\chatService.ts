import { axiosInstance } from '@/lib/axios';

export const fetchingprivateMessages = async (userId1: string, userId2: string) => {
  try {
    const response = await axiosInstance.get(`chat/messages/private?userId1=${userId1}&userId2=${userId2}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching private messages:', error);
  }
}

export const fetchingMessageUsers = async (userId: string, userType: 'student' | 'class') => {
  try {
    const response = await axiosInstance.get(`chat/messages/users?userId=${userId}&userType=${userType}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching message users:', error);
  }
}

export const fetchingUnreadMessageUsers = async (userId: string, userType: 'student' | 'class') => {
  try {
    const response = await axiosInstance.get(`chat/messages/unread-users?userId=${userId}&userType=${userType}`, {
      withCredentials: true
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching unread message users:', error);
  }
}
