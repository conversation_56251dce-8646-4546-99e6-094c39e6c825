import { Request,Response } from "express";
import {getCanClassesApply} from "../services/canClassesApplyServices";

export const fetchCanClassesApply = async (req: Request, res: Response):Promise<any> => {
    const { classId } = req.params;
    try {
        const classes = await getCanClassesApply(classId);
        if (!classes) {
            return res.status(404).json({ message: "Class not found" });
        }
        res.status(200).json(classes);
    } catch (error) {
        res.status(500).json({ message: "Error fetching classes", error });
    }
};