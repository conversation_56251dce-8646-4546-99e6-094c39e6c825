"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { useDispatch, useSelector } from "react-redux";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { axiosInstance } from "@/lib/axios";
import { useEffect } from "react";
import { RootState } from "@/store";
import { fetchClassDetails } from "@/store/thunks/classThunks";

const profileSchema = z.object({
  headline: z
    .string()
    .min(10, { message: "Headline must be at least 10 characters." })
    .max(100, { message: "Headline must be less than 100 characters." }),
  description: z
    .string()
    .min(100, { message: "B<PERSON> must be at least 100 characters." })
    .max(1000, { message: "Bio must be less than 1000 characters." }),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export function DescriptionForm() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      headline: "",
      description: "",
    },
  });

  const dispatch = useDispatch();
  const router = useRouter();

  const { user } : any = useSelector((state: RootState) => state.user);

  const onSubmit = async (data: ProfileFormValues) => {
    try {
      await axiosInstance.post(`/classes-profile/description`, data);
      await dispatch(fetchClassDetails(user.id));

      toast.success("Profile updated successfully!");
      dispatch(completeForm(FormId.DESCRIPTION));
      router.push("/classes/profile/photo-and-logo");
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to update profile");
    }
  };

  const { reset } = form;

  const classData = useSelector((state: RootState) => state.class.classData);
  useEffect(() => {
    if (
      classData?.ClassAbout?.tutorBio ||
      classData?.ClassAbout?.catchyHeadline
    ) {
      reset({
        headline: classData?.ClassAbout?.catchyHeadline || "",
        description: classData?.ClassAbout?.tutorBio || "",
      });
    }
  }, [classData, reset]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="headline"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Catchy Headline</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g. Passionate Math Tutor with 7+ Years of Experience"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tutor Bio</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={`1. Introduce yourself
2. Teaching experience
3. Motivate potential students`}
                  rows={6}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit">Save Profile</Button>
      </form>
    </Form>
  );
}
