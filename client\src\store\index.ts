import { configureStore } from '@reduxjs/toolkit';
import userReducer from './slices/userSlice';
import formProgressReducer from './slices/formProgressSlice';
import classReducer from './slices/classSlice';
import studentProfileReducer from './slices/studentProfileSlice';

export const store = configureStore({
  reducer: {
    user: userReducer,
    formProgress: formProgressReducer,
    class: classReducer,
    studentProfile: studentProfileReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
