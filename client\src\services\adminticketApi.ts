import { axiosInstance } from '@/lib/axios';

export interface Ticket {
  id: string;
  studentId: string;
  ticketCode: string;
  status: string;
  visitDate?: string;
  createdAt: string;
  updatedAt: string;
  generatedAt: string;
  student: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    contact?: string;
  };
}

export interface GetTicketByCodeResponse {
  success: boolean;
  data: Ticket;
  message: string;
}

export interface GetAllTicketsResponse {
  success: boolean;
  data: {
    tickets: Ticket[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message: string;
}

// SM Water Park APIs
export const getSMWaterParkTicketByCode = async (ticketCode: string): Promise<GetTicketByCodeResponse> => {
  const response = await axiosInstance.get(`/sm-water-park/ticket/${ticketCode}`);
  return response.data;
};

export const getAllSMWaterParkTickets = async (page: number = 1, limit: number = 10, search?: string): Promise<GetAllTicketsResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (search && search.trim()) {
    params.append('search', search.trim());
  }

  const response = await axiosInstance.get(`/sm-water-park/admin/all?${params.toString()}`);
  return response.data;
};

// Shiv Water Park APIs
export const getShivWaterParkTicketByCode = async (ticketCode: string): Promise<GetTicketByCodeResponse> => {
  const response = await axiosInstance.get(`/shiv-water-park/ticket/${ticketCode}`);
  return response.data;
};

export const getAllShivWaterParkTickets = async (page: number = 1, limit: number = 10, search?: string): Promise<GetAllTicketsResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (search && search.trim()) {
    params.append('search', search.trim());
  }

  const response = await axiosInstance.get(`/shiv-water-park/admin/all?${params.toString()}`);
  return response.data;
};

export interface AdminEntry {
  id: string;
  ticketCode: string;
  student: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    contact?: string;
  };
  enteredAt: string;
  visitDate?: string;
  generatedAt: string;
}

export interface AddAdminEntryResponse {
  success: boolean;
  data?: AdminEntry;
  message: string;
}

export interface GetAdminEntriesResponse {
  success: boolean;
  data: {
    entries: AdminEntry[];
    total: number;
    page: number;
    totalPages: number;
  };
  message: string;
}

export const addSMWaterParkAdminEntry = async (ticketCode: string): Promise<AddAdminEntryResponse> => {
  const response = await axiosInstance.post('/admin-tickets/sm-water-park/add-entry', { ticketCode });
  return response.data;
};

export const getSMWaterParkAdminEntries = async (page: number = 1, limit: number = 10, search?: string): Promise<GetAdminEntriesResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (search && search.trim()) {
    params.append('search', search.trim());
  }

  const response = await axiosInstance.get(`/admin-tickets/sm-water-park/entries?${params.toString()}`);
  return response.data;
};

export const addShivWaterParkAdminEntry = async (ticketCode: string): Promise<AddAdminEntryResponse> => {
  const response = await axiosInstance.post('/admin-tickets/shiv-water-park/add-entry', { ticketCode });
  return response.data;
};

export const getShivWaterParkAdminEntries = async (page: number = 1, limit: number = 10, search?: string): Promise<GetAdminEntriesResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (search && search.trim()) {
    params.append('search', search.trim());
  }

  const response = await axiosInstance.get(`/admin-tickets/shiv-water-park/entries?${params.toString()}`);
  return response.data;
};
