import { axiosInstance } from '@/lib/axios';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchClassDetails: any = createAsyncThunk(
  'class/fetchClassDetails',
  async (userId: any, { rejectWithValue }) => {
    try {
      const res = await axiosInstance.get(`/classes/details/${userId}`);
      return res.data;
    } catch (err: any) {
      return rejectWithValue(err.response?.data || 'Fetch failed');
    }
  }
);
