import prisma from "@/config/prismaClient";
import { CreateReviewInput } from "@/utils/interface";

export const checkStudentReviewExists = async (studentId: string, classId: string): Promise<boolean> => {
  const existingReview = await prisma.classesReviews.findFirst({
    where: {
      studentId,
      classId
    }
  });

  return !!existingReview;
};

export const createUserReviews = async (data:CreateReviewInput):Promise<any> => {
    const { studentId, classId, studentName, userType, ...testimonialData } = data;

    const uniqueId = `${Math.random().toString(36).substring(2, 15)}`;

    return prisma.classesReviews.create({
      data: {
        ...testimonialData,
        classId,
        modelId: uniqueId,
        modelType: userType || "STUDENT",
        studentId: studentId || null,
        studentName: studentName || null,
      },
    });
};

export const getUserReviewsByClassId = async (classId: string, page: number = 1, limit: number = 5) => {
  const skip = (page - 1) * limit;

  const [reviews, total] = await Promise.all([
    prisma.classesReviews.findMany({
      where: {
        classId
      },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    }),
    prisma.classesReviews.count({
      where: {
        classId
      }
    })
  ]);

  return {
    reviews,
    total,
    currentPage: page,
    totalPages: Math.ceil(total / limit),
    hasMore: page < Math.ceil(total / limit)
  };
};

export const getAverageRatingByClassId = async (classId: string) => {
  const testimonials = await prisma.classesReviews.findMany({
    where: {
      classId
    },
    select: {
      rating: true
    }
  });

  if (testimonials.length === 0) {
    return 0;
  }

  const totalRating = testimonials.reduce((sum, testimonial) => sum + testimonial.rating, 0);
  return totalRating / testimonials.length;
};

export const getReviewCountByClassId = async (classId: string) => {
  return prisma.classesReviews.count({
    where: {
      classId
    }
  });
};

export const deleteReviewsById = async (id: string) => {
  return prisma.classesReviews.delete({
    where: {
      id
    }
  });
};

export const getAllReviews = async (page: number = 1, limit: number = 10) => {
  const skip = (page - 1) * limit;

  const [testimonials, total] = await Promise.all([
    prisma.classesReviews.findMany({
      skip,
      take: limit,
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    }),
    prisma.classesReviews.count()
  ]);

  return {
    testimonials,
    total,
    currentPage: page,
    totalPages: Math.ceil(total / limit)
  };
};


export const getReviewByStudent = async (studentId: string, classId: string) => {
  return prisma.classes.findUnique({
  where: { id: classId },
  select: {
    email: true,
    firstName: true,
    lastName: true,
    className: true
  }});
};

