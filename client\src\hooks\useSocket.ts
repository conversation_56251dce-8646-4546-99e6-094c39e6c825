import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';

interface UseSocketProps {
  username: string;
  userId: string;
  userType: 'student' | 'class';
  isAuthenticated: boolean;
}

interface SocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

export const useSocket = ({ username, userId, userType, isAuthenticated }: UseSocketProps) => {
  const socketRef = useRef<Socket | null>(null);
  const [socketState, setSocketState] = useState<SocketState>({
    connected: false,
    connecting: false,
    error: null,
    reconnectAttempts: 0
  });

  const connect = useCallback(() => {
    if (!isAuthenticated || !username || !userId) {
      return;
    }

    if (socketRef.current?.connected) {
      return socketRef.current;
    }

    setSocketState(prev => ({ ...prev, connecting: true, error: null }));

    // Disconnect existing socket if any
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    // Create new socket connection with production-ready configuration
    socketRef.current = io(process.env.NEXT_PUBLIC_API_BASE_URL!, {
      withCredentials: true,
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      forceNew: true,
      // Connection state recovery
      auth: {
        username,
        userId,
        userType
      }
    });

    // Connection event handlers
    socketRef.current.on('connect', () => {
      console.log('Socket connected successfully');
      setSocketState({
        connected: true,
        connecting: false,
        error: null,
        reconnectAttempts: 0
      });

      // Join the socket with user data
      socketRef.current?.emit('join', { username, userType, userId });
      socketRef.current?.emit('getOnlineUsers');
      socketRef.current?.emit('getUnreadCounts', { userId, userType });
    });

    socketRef.current.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setSocketState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: error.message
      }));
      toast.error(`Connection error: ${error.message}`);
    });

    socketRef.current.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      setSocketState(prev => ({
        ...prev,
        connected: false,
        connecting: false
      }));

      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        socketRef.current?.connect();
      }
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnected after', attemptNumber, 'attempts');
      setSocketState(prev => ({
        ...prev,
        connected: true,
        connecting: false,
        error: null,
        reconnectAttempts: attemptNumber
      }));
      toast.success('Connection restored');
    });

    socketRef.current.on('reconnect_attempt', (attemptNumber) => {
      console.log('Socket reconnection attempt:', attemptNumber);
      setSocketState(prev => ({
        ...prev,
        reconnectAttempts: attemptNumber
      }));
    });

    socketRef.current.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error);
      setSocketState(prev => ({
        ...prev,
        error: error.message
      }));
    });

    socketRef.current.on('reconnect_failed', () => {
      console.error('Socket reconnection failed');
      setSocketState(prev => ({
        ...prev,
        error: 'Reconnection failed'
      }));
      toast.error('Unable to reconnect. Please refresh the page.');
    });

    // Handle server ping for connection monitoring
    socketRef.current.on('ping', () => {
      socketRef.current?.emit('pong');
    });

    // Handle server shutdown notification
    socketRef.current.on('server_shutdown', (data) => {
      toast.info(data.message);
    });

    return socketRef.current;
  }, [username, userId, userType, isAuthenticated]);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocketState({
        connected: false,
        connecting: false,
        error: null,
        reconnectAttempts: 0
      });
    }
  }, []);

  const emit = useCallback((event: string, data?: any) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(event, data);
      return true;
    }
    console.warn('Socket not connected, cannot emit event:', event);
    return false;
  }, []);

  const on = useCallback((event: string, handler: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.on(event, handler);
      return () => socketRef.current?.off(event, handler);
    }
    return () => {};
  }, []);

  const off = useCallback((event: string, handler?: (...args: any[]) => void) => {
    if (socketRef.current) {
      socketRef.current.off(event, handler);
    }
  }, []);

  // Auto-connect when dependencies change
  useEffect(() => {
    if (isAuthenticated && username && userId) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, username, userId, userType, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    socket: socketRef.current,
    socketState,
    connect,
    disconnect,
    emit,
    on,
    off
  };
};
