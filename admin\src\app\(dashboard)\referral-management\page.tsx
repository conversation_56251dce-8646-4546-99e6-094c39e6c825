'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Label } from '@/components/ui/label';
import { Plus, <PERSON>, UserCheck, Trash2, <PERSON><PERSON>, ExternalLink, Eye, Trophy, IndianRupee } from 'lucide-react';
import { toast } from 'sonner';
import { axiosInstance } from '@/lib/axios';
import { format } from 'date-fns';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';
import ReferralFilters, { FilterValues } from './ReferralFilters';

interface ReferralLink {
  id: string;
  userId: string;
  userEmail: string;
  userName: string;
  userType: 'ADMIN' | 'CLASS' | 'STUDENT';
  code: string;
  totalReferrals: number;
  studentsReferred: number;
  classesReferred: number;
  createdAt: string;
  isActive: boolean;
  earnings?: {
    totalEarnings: number;
    paidEarnings: number;
    unpaidEarnings: number;
  };
}

interface StaffLinkForm {
  staffEmail: string;
  staffName: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export default function ReferralManagement() {
  const router = useRouter();
  const [referralLinks, setReferralLinks] = useState<ReferralLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [staffForm, setStaffForm] = useState<StaffLinkForm>({
    staffEmail: '',
    staffName: '',
  });
  const [overallStats, setOverallStats] = useState({
    totalLinks: 0,
    totalReferrals: 0,
    totalStudents: 0,
    totalClasses: 0,
  });
  const [earningsSummary, setEarningsSummary] = useState({
    totalEarnings: 0,
    paidEarnings: 0,
    unpaidEarnings: 0,
    registrationEarnings: 0,
    uwhizEarnings: 0,
  });
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({
    firstName: '',
    lastName: '',
    email: '',
    userType: '',
    paymentStatus: '',
    startDate: '',
    endDate: '',
  });
  const [activeTab, setActiveTab] = useState<string>('all');

  const copyReferralCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Referral code copied to clipboard!');
  };

  const generateReferralUrl = (code: string, type: 'student' | 'class') => {
    const baseUrl = process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000';
    return `${baseUrl}/${type}/login?ref=${code}`;
  };

  const navigateToReferredUsers = (linkId: string) => {
    router.push(`/referral-management/${linkId}/referred-users`);
  };

  const deactivateLink = async (linkId: string) => {
    try {
      const response = await axiosInstance.patch(`/referral/admin/deactivate/${linkId}`);
      if (response.data.success) {
        toast.success('Referral link deactivated successfully!');
        fetchReferralLinks(currentPage);
      }
    } catch (error) {
      console.error('Error deactivating link:', error);
      toast.error('Failed to deactivate referral link');
    }
  };

  // Base columns that are always shown
  const baseColumns: ColumnDef<ReferralLink>[] = [
    {
      accessorKey: 'rank',
      header: 'Rank',
      cell: ({ row }) => {
        const index = row.index;
        const globalRank = ((pagination?.currentPage || 1) - 1) * 10 + index + 1;
        const isTopPerformer = globalRank <= 3;
        const trophyColor = globalRank === 1 ? 'text-yellow-500' : globalRank === 2 ? 'text-gray-400' : 'text-orange-600';

        return (
          <div className="flex items-center gap-2">
            {isTopPerformer && (
              <Trophy className={`h-4 w-4 ${trophyColor}`} />
            )}
            <span className={`font-bold ${isTopPerformer ? 'text-orange-600' : 'text-gray-600'}`}>
              #{globalRank}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'userInfo',
      header: 'User/Staff',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.userName || 'N/A'}</div>
          <div className="text-sm text-gray-500">{row.original.userEmail}</div>
        </div>
      ),
    },
    {
      accessorKey: 'userType',
      header: 'Type',
      cell: ({ row }) => (
        <Badge variant={row.original.userType === 'ADMIN' ? 'default' : 'secondary'}>
          {row.original.userType}
        </Badge>
      ),
    },
    {
      accessorKey: 'code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <code className="bg-gray-100 px-2 py-1 rounded text-sm">
            {row.original.code}
          </code>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => copyReferralCode(row.original.code)}
          >
            <Copy className="h-3 w-3" />
          </Button>
        </div>
      ),
    },
    {
      accessorKey: 'totalReferrals',
      header: 'Total Referrals',
      cell: ({ row }) => (
        <span className="font-medium">{row.original.totalReferrals}</span>
      ),
    },
    {
      accessorKey: 'studentsReferred',
      header: 'Students',
      cell: ({ row }) => (
        <span className="text-blue-600">{row.original.studentsReferred}</span>
      ),
    },
    {
      accessorKey: 'classesReferred',
      header: 'Classes',
      cell: ({ row }) => (
        <span className="text-green-600">{row.original.classesReferred}</span>
      ),
    },
  ];

  // Earnings column (for all user types)
  const earningsColumn: ColumnDef<ReferralLink> = {
    accessorKey: 'earnings',
    header: () => (
      <div className="flex items-center gap-1">
        <IndianRupee className="h-4 w-4" />
        Earnings
      </div>
    ),
    cell: ({ row }) => {
      const earnings = row.original.earnings;
      return earnings && earnings.totalEarnings > 0 ? (
        <div className="space-y-1">
          <div className="text-sm font-semibold text-purple-600">
            ₹{earnings.totalEarnings}
          </div>
          {(earnings.paidEarnings > 0 || earnings.unpaidEarnings > 0) && (
            <div className="text-xs text-gray-500">
              <span className="text-green-600">Paid: ₹{earnings.paidEarnings}</span>
              {' | '}
              <span className="text-orange-600">Pending: ₹{earnings.unpaidEarnings}</span>
            </div>
          )}
        </div>
      ) : (
        <span className="text-gray-400">₹0</span>
      );
    },
  };

  // End columns that are always shown
  const endColumns: ColumnDef<ReferralLink>[] = [
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => format(new Date(row.original.createdAt), 'MMM dd, yyyy'),
    },
    {
      accessorKey: 'isActive',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant={row.original.isActive ? 'default' : 'destructive'}>
          {row.original.isActive ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      accessorKey: 'viewReferred',
      header: 'View Referred',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigateToReferredUsers(row.original.id)}
          className="text-orange-600 hover:text-orange-700"
        >
          <Eye className="h-4 w-4 mr-1" />
          View ({row.original.totalReferrals})
        </Button>
      ),
    },
    {
      accessorKey: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const link = row.original;
        // Only show delete option when both students and classes are 0
        const canDelete = link.studentsReferred === 0 && link.classesReferred === 0;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(generateReferralUrl(link.code, 'student'), '_blank')}
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
            {link.isActive && canDelete && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Deactivate Referral Link</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to deactivate this referral link? This action cannot be undone and the link will no longer work for new registrations.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => deactivateLink(link.id)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Deactivate
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        );
      },
    },
  ];

  // Combine columns based on active tab
  const columns: ColumnDef<ReferralLink>[] = [
    ...baseColumns,
    // Show earnings column for all tabs (students can now earn too)
    earningsColumn,
    ...endColumns,
  ];

  // Filter referral links to hide entries with 0 students AND 0 classes (except for ADMIN/Staff)
  const filteredReferralLinks = referralLinks.filter(link => {
    // Always show ADMIN/Staff entries regardless of referral count
    if (link.userType === 'ADMIN') {
      return true;
    }
    // For other types (STUDENT, CLASS), only show if they have at least 1 student OR 1 class referred
    return link.studentsReferred > 0 || link.classesReferred > 0;
  });


  const fetchReferralLinks = useCallback(async (page: number = 1, filters?: FilterValues, tabFilter?: string) => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
      });

      const filtersToUse = filters || activeFilters;
      const tabToUse = tabFilter || activeTab;

      if (filtersToUse.firstName) params.append('firstName', filtersToUse.firstName);
      if (filtersToUse.lastName) params.append('lastName', filtersToUse.lastName);
      if (filtersToUse.email) params.append('email', filtersToUse.email);
      if (filtersToUse.userType) params.append('userType', filtersToUse.userType);
      if (filtersToUse.paymentStatus) params.append('paymentStatus', filtersToUse.paymentStatus);
      if (filtersToUse.startDate) params.append('startDate', filtersToUse.startDate);
      if (filtersToUse.endDate) params.append('endDate', filtersToUse.endDate);

      // Add tab filter
      if (tabToUse && tabToUse !== 'all') {
        if (tabToUse === 'staff') {
          params.append('userType', 'ADMIN');
        } else if (tabToUse === 'student') {
          params.append('userType', 'STUDENT');
        } else if (tabToUse === 'class') {
          params.append('userType', 'CLASS');
        }
      }

      const response = await axiosInstance.get(`/referral/admin/all-links?${params.toString()}`);
      if (response.data.success) {
        const links = response.data.data.referralLinks;
        const paginationData = response.data.data.pagination;

        setReferralLinks(links);
        setPagination(paginationData);
        setCurrentPage(page);

        // Calculate overall stats from pagination data (use original data, not filtered)
        setOverallStats({
          totalLinks: paginationData.totalCount,
          totalReferrals: links.reduce((acc: number, link: ReferralLink) => acc + link.totalReferrals, 0),
          totalStudents: links.reduce((acc: number, link: ReferralLink) => acc + link.studentsReferred, 0),
          totalClasses: links.reduce((acc: number, link: ReferralLink) => acc + link.classesReferred, 0),
        });
      }
    } catch (error) {
      console.error('Error fetching referral links:', error);
      toast.error('Failed to load referral links');
    } finally {
      setLoading(false);
    }
  }, [activeFilters, activeTab]);

  const handleFilterChange = (filters: FilterValues) => {
    setActiveFilters(filters);
    fetchReferralLinks(1, filters, activeTab); // Reset to page 1 when filters change
  };

  const handleClearFilters = () => {
    const emptyFilters: FilterValues = {
      firstName: '',
      lastName: '',
      email: '',
      userType: '',
      paymentStatus: '',
      startDate: '',
      endDate: '',
    };
    setActiveFilters(emptyFilters);
    fetchReferralLinks(1, emptyFilters, activeTab); // Reset to page 1 with no filters
  };

  const handlePageChange = (page: number) => {
    fetchReferralLinks(page, activeFilters, activeTab);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setCurrentPage(1); // Reset to page 1 when tab changes
    fetchReferralLinks(1, activeFilters, tab);
  };

  const fetchEarningsSummary = async () => {
    try {
      const response = await axiosInstance.get('/referral/admin/earnings-summary');
      if (response.data.success) {
        setEarningsSummary(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching earnings summary:', error);
    }
  };

  const createStaffLink = async () => {
    try {
      if (!staffForm.staffEmail || !staffForm.staffName) {
        toast.error('Please fill in all fields');
        return;
      }

      const response = await axiosInstance.post('/referral/admin/create-staff-link', staffForm);
      if (response.data.success) {
        toast.success('Staff referral link created successfully!');
        setIsCreateDialogOpen(false);
        setStaffForm({ staffEmail: '', staffName: '' });
        fetchReferralLinks(1); // Go to first page after creating new link
      }
    } catch (error: any) {
      console.error('Error creating staff link:', error);
      toast.error(error.response?.data?.message || 'Failed to create staff link');
    }
  };



  useEffect(() => {
    fetchReferralLinks(1);
    fetchEarningsSummary();
  }, [fetchReferralLinks]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Referral Management</h1>
          <p className="text-gray-600">Manage staff referral links and track performance</p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-orange-500 hover:bg-orange-600">
              <Plus className="h-4 w-4 mr-2" />
              Add Staff Link
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Staff Referral Link</DialogTitle>
              <DialogDescription>
                Create a new referral link for a staff member
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="staffName">Staff Name</Label>
                <Input
                  id="staffName"
                  value={staffForm.staffName}
                  onChange={(e) => setStaffForm(prev => ({ ...prev, staffName: e.target.value }))}
                  placeholder="Enter staff name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="staffEmail">Staff Email</Label>
                <Input
                  id="staffEmail"
                  type="email"
                  value={staffForm.staffEmail}
                  onChange={(e) => setStaffForm(prev => ({ ...prev, staffEmail: e.target.value }))}
                  placeholder="Enter staff email"
                />
              </div>
              <Button onClick={createStaffLink} className="w-full">
                Create Referral Link
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <ReferralFilters
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
        isLoading={loading}
      />

      {/* Overall Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Links</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {overallStats.totalLinks}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Referrals</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {overallStats.totalReferrals}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Students Referred</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {overallStats.totalStudents}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Classes Referred</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {overallStats.totalClasses}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Earnings Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Earnings
              {activeFilters.paymentStatus && (
                <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {activeFilters.paymentStatus === 'PAID' ? 'Showing: Paid ' : 'Showing: Pending '}
                </span>
              )}
            </CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              ₹{earningsSummary.totalEarnings}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Registration: ₹{earningsSummary.registrationEarnings} | U-whiz: ₹{earningsSummary.uwhizEarnings}
            </p>
            {activeFilters.paymentStatus && (
              <Button
                variant="outline"
                size="sm"
                className="mt-2 w-full text-gray-600 border-gray-200 hover:bg-gray-50"
                onClick={() => handleFilterChange({ ...activeFilters, paymentStatus: '' })}
              >
                Clear Payment Filter
              </Button>
            )}
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleFilterChange({ ...activeFilters, paymentStatus: 'PAID' })}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Earnings</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ₹{earningsSummary.paidEarnings}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Amount distributed to classes
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-full text-green-600 border-green-200 hover:bg-green-50"
              onClick={(e) => {
                e.stopPropagation();
                handleFilterChange({ ...activeFilters, paymentStatus: 'PAID' });
              }}
            >
              Show Paid Earnings
            </Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => handleFilterChange({ ...activeFilters, paymentStatus: 'UNPAID' })}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Earnings</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              ₹{earningsSummary.unpaidEarnings}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Amount pending for payment
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 w-full text-orange-600 border-orange-200 hover:bg-orange-50"
              onClick={(e) => {
                e.stopPropagation();
                handleFilterChange({ ...activeFilters, paymentStatus: 'UNPAID' });
              }}
            >
              Show Pending Earnings
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Referral Links Table */}
      <Card>
        <CardHeader>
          <CardTitle>Referral Links</CardTitle>
          <CardDescription>
            Manage all referral links and track their performance
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <div className="px-6 pt-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="staff">Staff</TabsTrigger>
                <TabsTrigger value="student">Student</TabsTrigger>
                <TabsTrigger value="class">Class</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="mt-0">
              <DataTable
                columns={columns}
                data={filteredReferralLinks}
                totalItems={pagination?.totalCount || 0}
                totalPages={pagination?.totalPages || 1}
                currentPage={pagination?.currentPage || 1}
                pageSize={pagination?.limit || 10}
                onPageChange={handlePageChange}
                isLoading={loading}
              />
            </TabsContent>

            <TabsContent value="staff" className="mt-0">
              <DataTable
                columns={columns}
                data={filteredReferralLinks}
                totalItems={pagination?.totalCount || 0}
                totalPages={pagination?.totalPages || 1}
                currentPage={pagination?.currentPage || 1}
                pageSize={pagination?.limit || 10}
                onPageChange={handlePageChange}
                isLoading={loading}
              />
            </TabsContent>

            <TabsContent value="student" className="mt-0">
              <DataTable
                columns={columns}
                data={filteredReferralLinks}
                totalItems={pagination?.totalCount || 0}
                totalPages={pagination?.totalPages || 1}
                currentPage={pagination?.currentPage || 1}
                pageSize={pagination?.limit || 10}
                onPageChange={handlePageChange}
                isLoading={loading}
              />
            </TabsContent>

            <TabsContent value="class" className="mt-0">
              <DataTable
                columns={columns}
                data={filteredReferralLinks}
                totalItems={pagination?.totalCount || 0}
                totalPages={pagination?.totalPages || 1}
                currentPage={pagination?.currentPage || 1}
                pageSize={pagination?.limit || 10}
                onPageChange={handlePageChange}
                isLoading={loading}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
