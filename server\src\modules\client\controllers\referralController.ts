import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import {
  createOrGetReferralLink,
  getReferralDashboardData,
  getReferralHistory,
  getAllReferralLinks,
  createStaffReferralLink,
  deactivateReferralLink,
  getReferredUsersByLinkId,
  createUwhizApplicationEarning,
  getAllReferralEarnings,
  updateEarningsPaymentStatus,
  getReferralLinkEarnings,
  getOverallEarningsSummary,
  getStudentReferralCode,
  getReferralDiscount,
} from '../services/referralService';
import { UserType } from '@prisma/client';

// Generate or get existing referral link for user
export const generateReferralLink = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;
    const studentId = req.student?.id;

    if (!classId && !studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const userId = classId || studentId;
    const userType: UserType = classId ? 'CLASS' : 'STUDENT';

    const referralLink = await createOrGetReferralLink(userId!, userType);

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const studentLink = `${baseUrl}/student/login?ref=${referralLink.code}`;
    const classLink = `${baseUrl}/class/login?ref=${referralLink.code}`;

    return sendSuccess(res, {
      referralCode: referralLink.code,
      links: {
        studentLink,
        classLink,
      },
      isNew: !referralLink.createdAt || new Date(referralLink.createdAt).getTime() === new Date(referralLink.updatedAt).getTime(),
    }, 'Referral link retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to generate referral link');
  }
};

// Get referral dashboard data
export const getReferralDashboard = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;
    const studentId = req.student?.id;

    if (!classId && !studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const userId = classId || studentId;
    const userType: UserType = classId ? 'CLASS' : 'STUDENT';

    const dashboardData = await getReferralDashboardData(userId!, userType);

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const links = dashboardData.referralCode ? {
      studentLink: `${baseUrl}/student/login?ref=${dashboardData.referralCode}`,
      classLink: `${baseUrl}/class/login?ref=${dashboardData.referralCode}`,
    } : null;

    return sendSuccess(res, {
      ...dashboardData,
      links,
    }, 'Dashboard data retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get dashboard data');
  }
};

// Get referral history with optional date filter
export const getReferralHistoryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;
    const studentId = req.student?.id;

    if (!classId && !studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const userId = classId || studentId;
    const userType: UserType = classId ? 'CLASS' : 'STUDENT';

    const { startDate, endDate } = req.query;

    let start: Date | undefined;
    let end: Date | undefined;

    if (startDate) {
      start = new Date(startDate as string);
    }
    if (endDate) {
      end = new Date(endDate as string);
    }

    const history = await getReferralHistory(userId!, userType, start, end);

    return sendSuccess(res, { history }, 'Referral history retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get referral history');
  }
};

// Admin: Get all referral links with pagination and filters
export const getAllReferralLinksController = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page = '1',
      limit = '10',
      firstName,
      lastName,
      email,
      userType,
      paymentStatus,
      startDate,
      endDate
    } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);

    // Validate pagination parameters
    if (isNaN(pageNumber) || pageNumber < 1) {
      return sendError(res, 'Invalid page number', 400);
    }

    if (isNaN(limitNumber) || limitNumber < 1 || limitNumber > 100) {
      return sendError(res, 'Invalid limit. Must be between 1 and 100', 400);
    }

    // Build filters object
    const filters: any = {};
    if (firstName) filters.firstName = firstName as string;
    if (lastName) filters.lastName = lastName as string;
    if (email) filters.email = email as string;
    if (userType) filters.userType = userType as string;
    if (paymentStatus) filters.paymentStatus = paymentStatus as string;
    if (startDate) filters.startDate = startDate as string;
    if (endDate) filters.endDate = endDate as string;

    const result = await getAllReferralLinks(pageNumber, limitNumber, Object.keys(filters).length > 0 ? filters : undefined);

    const formattedLinks = result.referralLinks.map(link => ({
      id: link.id,
      userId: link.userId,
      userEmail: link.userEmail,
      userName: link.userName,
      userType: link.userType,
      code: link.code,
      totalReferrals: link.referrals.length,
      studentsReferred: link.referrals.filter(r => r.referredUserType === 'STUDENT').length,
      classesReferred: link.referrals.filter(r => r.referredUserType === 'CLASS').length,
      createdAt: link.createdAt,
      isActive: link.isActive,
      earnings: link.earnings, // Include earnings data
    }));

    return sendSuccess(res, {
      referralLinks: formattedLinks,
      pagination: result.pagination
    }, 'All referral links retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get referral links');
  }
};

// Admin: Create staff referral link
export const createStaffReferralLinkController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { staffEmail, staffName } = req.body;

    if (!staffEmail || !staffName) {
      return sendError(res, 'Staff email and name are required', 400);
    }

    const referralLink = await createStaffReferralLink(staffEmail, staffName);

    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const links = {
      studentLink: `${baseUrl}/student/login?ref=${referralLink.code}`,
      classLink: `${baseUrl}/class/login?ref=${referralLink.code}`,
    };

    return sendSuccess(res, {
      referralLink,
      links,
    }, 'Staff referral link created successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to create staff referral link');
  }
};

// Admin: Deactivate referral link
export const deactivateReferralLinkController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { linkId } = req.params;

    if (!linkId) {
      return sendError(res, 'Link ID is required', 400);
    }

    await deactivateReferralLink(linkId);

    return sendSuccess(res, {}, 'Referral link deactivated successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to deactivate referral link');
  }
};

// Admin: Get referred users for a specific referral link with pagination
export const getReferredUsersController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { linkId } = req.params;
    const { page = '1', limit = '10' } = req.query;

    if (!linkId) {
      return sendError(res, 'Link ID is required', 400);
    }

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);

    // Validate pagination parameters
    if (isNaN(pageNumber) || pageNumber < 1) {
      return sendError(res, 'Invalid page number', 400);
    }

    if (isNaN(limitNumber) || limitNumber < 1 || limitNumber > 100) {
      return sendError(res, 'Invalid limit. Must be between 1 and 100', 400);
    }

    const data = await getReferredUsersByLinkId(linkId, pageNumber, limitNumber);

    return sendSuccess(res, data, 'Referred users retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get referred users');
  }
};

// Create U-whiz application earning
export const createUwhizEarningController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { studentId, examId } = req.body;

    if (!studentId || !examId) {
      return sendError(res, 'Student ID and Exam ID are required', 400);
    }

    const earning = await createUwhizApplicationEarning(studentId, examId);

    if (!earning) {
      return sendSuccess(res, { message: 'No referral found for this student' }, 'No earning created');
    }

    return sendSuccess(res, earning, 'U-whiz earning created successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to create U-whiz earning');
  }
};

// Admin: Get all referral earnings
export const getAllReferralEarningsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page = '1',
      limit = '10',
      paymentStatus,
      earningType,
      referralCode
    } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);

    // Validate pagination parameters
    if (isNaN(pageNumber) || pageNumber < 1) {
      return sendError(res, 'Invalid page number', 400);
    }

    if (isNaN(limitNumber) || limitNumber < 1 || limitNumber > 100) {
      return sendError(res, 'Invalid limit. Must be between 1 and 100', 400);
    }

    const filters: any = {};
    if (paymentStatus) filters.paymentStatus = paymentStatus;
    if (earningType) filters.earningType = earningType;
    if (referralCode) filters.referralCode = referralCode;

    const data = await getAllReferralEarnings(pageNumber, limitNumber, filters);

    return sendSuccess(res, data, 'Referral earnings retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get referral earnings');
  }
};

// Admin: Update payment status for earnings
export const updateEarningsPaymentStatusController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { earningIds, paymentStatus } = req.body;

    if (!earningIds || !Array.isArray(earningIds) || earningIds.length === 0) {
      return sendError(res, 'Earning IDs array is required', 400);
    }

    if (!paymentStatus || !['PAID', 'UNPAID'].includes(paymentStatus)) {
      return sendError(res, 'Valid payment status (PAID/UNPAID) is required', 400);
    }

    const result = await updateEarningsPaymentStatus(earningIds, paymentStatus);

    return sendSuccess(res, result, `Payment status updated to ${paymentStatus} successfully`);
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to update payment status');
  }
};

// Admin: Get earnings for a specific referral link
export const getReferralLinkEarningsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { linkId } = req.params;

    if (!linkId) {
      return sendError(res, 'Link ID is required', 400);
    }

    const data = await getReferralLinkEarnings(linkId);

    return sendSuccess(res, data, 'Referral link earnings retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get referral link earnings');
  }
};

// Admin: Get overall earnings summary
export const getOverallEarningsSummaryController = async (req: Request, res: Response): Promise<any> => {
  try {
    const data = await getOverallEarningsSummary();

    return sendSuccess(res, data, 'Overall earnings summary retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get overall earnings summary');
  }
};

// Get student discount information
export const getStudentDiscountController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;

    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const referralCode = await getStudentReferralCode(studentId);

    if (!referralCode) {
      return sendSuccess(res, {
        hasDiscount: false,
        discountPercentage: 0,
        referralCode: null,
      }, 'No referral code found');
    }

    const discountInfo = await getReferralDiscount(referralCode);

    return sendSuccess(res, {
      ...discountInfo,
      referralCode,
    }, 'Student discount information retrieved successfully');
  } catch (error: any) {
    return sendError(res, error.message || 'Failed to get student discount information');
  }
};
