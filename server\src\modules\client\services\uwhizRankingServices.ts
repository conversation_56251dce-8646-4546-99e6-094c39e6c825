import { PrismaClient } from '@prisma/client';
import NodeCache from 'node-cache';
import { Ranking } from '@/utils/interface';

const cache = new NodeCache({ stdTTL: 28800 });

const prisma = new PrismaClient();

// Saves a single answer to the Question_answer table
export async function saveQuestionAnswer(
  userId: string,
  questionId: number,
  answer: string,
  isCorrect: boolean
) {
  return prisma.question_answer.create({
    data: {
      userId,
      questionId,
      answer,
      isCorrect,
    },
  });
}

export async function getExamRankings(examId: number): Promise<Ranking[]> {
  const cacheKey = `examRankings_${examId}`;

  // Check if data exists in cache
  const cachedRankings = cache.get<Ranking[]>(cacheKey);
  if (cachedRankings) {
    return cachedRankings;
  }

  const exam = await prisma.exam.findUnique({
    where: { id: examId },
    select: { id: true, total_questions: true },
  });

  if (!exam) {
    throw new Error(`Exam with ID ${examId} not found`);
  }

  // Fetch correct answers grouped by userId and questionId
  const correctAnswers = await prisma.question_answer.groupBy({
    by: ['userId', 'questionId'],
    where: {
      isCorrect: true,
      question: {
        examId: examId,
      },
    },
    _count: {
      isCorrect: true,
    },
  });

  // Fetch total attempted questions grouped by userId and questionId
  const attemptedQuestions = await prisma.question_answer.groupBy({
    by: ['userId', 'questionId'],
    where: {
      question: {
        examId: examId,
      },
    },
    _count: {
      id: true,
    },
  });

  // Fetch firstName and lastName from Classes table for users who submitted answers
  const userIds = [
    ...new Set([
      ...correctAnswers.map((entry) => entry.userId),
      ...attemptedQuestions.map((entry) => entry.userId),
    ]),
  ];

  const users = await prisma.classes.findMany({
    where: {
      id: { in: userIds },
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      className: true,
      ClassAbout: {
        select: {
          classesLogo: true,
        },
      },
    },
  });

  // Map correct answers and attempted questions to users
  const rankedUsers = userIds
    .map((userId) => {
      const user = users.find((u) => u.id === userId);
      const name = user ? `${user.firstName} ${user.lastName} ` : 'Unknown';

      // Aggregate correct answers for this user
      const correctCount = correctAnswers.filter((entry) => entry.userId === userId).length;

      // Aggregate attempted questions for this user
      const attemptedCount = attemptedQuestions.filter((entry) => entry.userId === userId).length;

      return {
        userId,
        name,
        className: user?.className || '',
        classesLogo: user?.ClassAbout?.classesLogo || null,
        correctCount,
        attemptedCount,
      };
    })
    .sort((a, b) => {
      // Step 1: Sort by correct answers (descending)
      if (b.correctCount !== a.correctCount) {
        return b.correctCount - a.correctCount;
      }
      // Step 2: If correct answers are same, sort by attempted questions (ascending)
      if (a.attemptedCount !== b.attemptedCount) {
        return a.attemptedCount - b.attemptedCount;
      }
      // Step 3: If both are same, sort alphabetically by name
      return a.name.localeCompare(b.name);
    });

  // Assign ranks, giving same rank to users with same correct count and attempted count
  const rankings: Ranking[] = [];
  let currentRank = 1;
  let prevCorrect = rankedUsers[0]?.correctCount;
  let prevAttempted = rankedUsers[0]?.attemptedCount;

  for (const user of rankedUsers) {
    // If either correct count or attempted count changes, increment rank
    if (user.correctCount !== prevCorrect || user.attemptedCount !== prevAttempted) {
      currentRank++;
      prevCorrect = user.correctCount;
      prevAttempted = user.attemptedCount;
    }
    rankings.push({
      rank: currentRank,
      userId: user.userId,
      name: user.name,
      classesName: user.className,
      classesLogo: user.classesLogo,
      score: user.correctCount,
      attempted: user.attemptedCount,
      totalQuestions: exam.total_questions,
    });
  }

  cache.set(cacheKey, rankings);

  return rankings;
}

export async function getUserExamRank(examId: number, userId: string) {
  const rankings = await getExamRankings(examId);

  const userRanking = rankings.find((ranking) => ranking.userId === userId);

  if (!userRanking) {
    throw new Error(`User with ID ${userId} not found in exam ${examId} rankings`);
  }

  return {
    rank: userRanking.rank,
    userId: userRanking.userId,
    name: userRanking.name,
    score: userRanking.score,
    attempted: userRanking.attempted,
    totalQuestions: userRanking.totalQuestions,
  };
}
