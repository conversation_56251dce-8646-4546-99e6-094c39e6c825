import { Request as ExpressRequest, Response } from "express";
import prisma from "@/config/prismaClient";
import {
  createBlogService,
  getAllBlogsService,
  getBlogById,
  updateBlogService,
  updateBlogStatusService,
  deleteBlogService,
  getApprovedBlogsService,
  getBlogsByClassId,
} from "../../services/blogServices/blogService";
import { Status } from "@prisma/client";

interface Request extends ExpressRequest {
  class?: {
    id: string;
    [key: string]: any;
  };
  admin?: {
    id: string;
    [key: string]: any;
  };
}

const getAuthenticatedClassId = async (req: Request): Promise<string | null> => {
  if (req.class?.id) {
    try {
      const classExists = await prisma.classes.findUnique({
        where: { id: req.class.id }
      });

      if (classExists) {
        return req.class.id;
      } else {
        return null;
      }
    } catch (error:any) {
      console.log("Failed to Authenticated ClassId",error)
      return null;
    }
  }

  return null;
};

export const getMyBlogs = async (req: Request, res: Response): Promise<void> => {
  try {
    const classId = await getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class."
      });
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as Status | undefined;

    const result = await getBlogsByClassId(classId, { page, limit, status });

    res.status(200).json({
      success: true,
      ...result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch class blogs",
    });
  }
};

export const getAllBlogs = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as Status | undefined;

    const result = await getAllBlogsService({ page, limit, status });

    res.status(200).json({
      success: true,
      ...result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch blogs",
    });
  }
};

export const getBlogByIdHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const blog = await getBlogById(id);

    if (!blog) {
      res.status(404).json({
        success: false,
        message: "Blog not found",
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: blog,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch blog",
    });
  }
};

export const createBlog = async (req: Request, res: Response): Promise<void> => {
  try {
    const blogData = req.body;
    const classId = await getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    blogData.classId = classId;

    if (req.file) {
      blogData.blogImage = req.file.path.replace(/\\/g, "/");
    }

    try {
      if (!blogData.classId || typeof blogData.classId !== 'string' || blogData.classId.length < 10) {
        res.status(400).json({
          success: false,
          message: `Invalid class ID format: ${blogData.classId}`,
        });
        return;
      }

      const classExists = await prisma.classes.findUnique({
        where: { id: blogData.classId }
      });

      if (!classExists) {
        res.status(404).json({
          success: false,
          message: `Class with ID ${blogData.classId} not found. Please check the class ID and try again.`,
        });
        return;
      }
    } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to Create class blogs",
    });
  }

    const blog = await createBlogService(blogData);

    res.status(201).json({
      success: true,
      message: "Blog created successfully",
      data: blog,
    });
  } catch (error: any) {
    let errorMessage = "Failed to create blog";
    if (error.message) {
      errorMessage = error.message;
    } else if (error.code === 'P2025') {
      errorMessage = "The class you're trying to associate with this blog doesn't exist.";
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
    });
  }
};

export const updateBlog = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const blogData = req.body;

    const classId = await getAuthenticatedClassId(req);

    if (!classId) {
      res.status(400).json({
        success: false,
        message: "Valid class ID is required. Please login as a class.",
      });
      return;
    }

    const existingBlog = await getBlogById(id);

    if (!existingBlog) {
      res.status(404).json({
        success: false,
        message: "Blog not found",
      });
      return;
    }

    if (existingBlog && existingBlog.classId && existingBlog.classId !== classId) {
      res.status(403).json({
        success: false,
        message: "You are not authorized to update this blog",
      });
      return;
    }

    if (req.file) {
      blogData.blogImage = req.file.path.replace(/\\/g, "/");
    }

    const blog = await updateBlogService(id, blogData);

    res.status(200).json({
      success: true,
      message: "Blog updated successfully",
      data: blog,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to update blog",
    });
  }
};

export const updateBlogStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const blog = await updateBlogStatusService(id, { status });

    res.status(200).json({
      success: true,
      message: `Blog status updated to ${status}`,
      data: blog,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to update blog status",
    });
  }
};

export const deleteBlog = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const existingBlog = await getBlogById(id);

    if (!existingBlog) {
      res.status(404).json({
        success: false,
        message: "Blog not found",
      });
      return;
    }

    if (!req.admin) {
      const classId = await getAuthenticatedClassId(req);

      if (!classId) {
        res.status(400).json({
          success: false,
          message: "Valid class ID is required. Please login as a class.",
        });
        return;
      }

      if (existingBlog && existingBlog.classId && existingBlog.classId !== classId) {
        res.status(403).json({
          success: false,
          message: "You are not authorized to delete this blog",
        });
        return;
      }
    }
    
    await deleteBlogService(id);

    res.status(200).json({
      success: true,
      message: "Blog deleted successfully",
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to delete blog",
    });
  }
};

export const getApprovedBlogs = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await getApprovedBlogsService({ page, limit });

    res.status(200).json({
      success: true,
      ...result,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || "Failed to fetch approved blogs",
    });
  }
};
