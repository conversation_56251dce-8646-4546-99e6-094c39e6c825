"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, Plus, Pencil, Trash2 } from "lucide-react";
import { DynamicTable } from "@/app-components/DynamicTable";
import { ColumnDef } from "@tanstack/react-table";
import { Blog } from "@/lib/types";
import { getMyBlogs, deleteBlog } from "@/services/blogApi";
import { format } from "date-fns";

const BlogsPage = () => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [blogToDelete, setBlogToDelete] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { user } = useSelector((state: any) => state.user);
  const router = useRouter();

  const fetchBlogs = useCallback(
    async (pageIndex: number) => {
      try {
        setIsLoading(true);
        const response = await getMyBlogs(pageIndex + 1, 10);
        setBlogs(response.blogs);
        setTotalItems(response.totalPages * 10);
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch your blogs");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    if (!user) {
      router.push("/");
      return;
    }

    fetchBlogs(0);
  }, [user, router, fetchBlogs]);

  const handleDelete = async () => {
    if (!blogToDelete) return;

    try {
      setIsSubmitting(true);
      await deleteBlog(blogToDelete);
      toast.success("Blog deleted successfully");
      setIsDeleteDialogOpen(false);
      setBlogToDelete(null);
      fetchBlogs(0);
    } catch (error: any) {
      toast.error(error.message || "Failed to delete blog");
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDelete = (id: string) => {
    setBlogToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "blogTitle",
      header: "Title",
      cell: ({ row }) => <span className="font-medium">{row.original.blogTitle}</span>,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            row.original.status === "APPROVED"
              ? "bg-green-100 text-green-800"
              : row.original.status === "REJECTED"
              ? "bg-red-100 text-red-800"
              : "bg-yellow-100 text-yellow-800"
          }`}
        >
          {row.original.status}
        </span>
      ),
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ row }) =>
        format(new Date(row.original.createdAt), "MMM dd, yyyy"),
    },
    {
      id: "actions",
      header: () => <div className="text-right">Actions</div>,
      cell: ({ row, table }) => {
        const { onEdit, onDelete } = table.options.meta as {
          onEdit?: (row: any) => void;
          onDelete?: (id: string) => void;
        };
        return (
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => onEdit?.(row.original)}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="text-red-500"
              onClick={() => onDelete?.(row.original.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        );
      },
    },
  ];

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">My Blogs</h1>
        <Button onClick={() => router.push("/classes/blogs/add")}>
          <Plus className="mr-2 h-4 w-4" />
          Add Blog
        </Button>
      </div>

      <DynamicTable
        columns={columns}
        data={blogs}
        fetchData={fetchBlogs}
        totalItems={totalItems}
        pageSize={10}
        isLoading={isLoading}
        onEdit={(row) => router.push(`/classes/blogs/add?id=${row.id}`)}
        onDelete={confirmDelete}
        deletingId={blogToDelete}
      />

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this blog? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BlogsPage;