'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const SMWaterparkPage = () => {
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('sm_admin_token');
    
    if (token) {
      router.push('/smwaterpark/dashboard');
    } else {
      router.push('/smwaterpark/login');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading...</p>
      </div>
    </div>
  );
};

export default SMWaterparkPage;
