import { axiosInstance } from '@/lib/axios';

export async function createQuizTerminationLog(data: {
  classId: string;
  examId: number;
  reason: string;
  clientTime?: string;
}) {
  try {
    const response = await axiosInstance.post('/violance/print-log', data);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create log on admin side when violance occure: ${error.response?.data?.message || error.message}`,
    };
  }
}
