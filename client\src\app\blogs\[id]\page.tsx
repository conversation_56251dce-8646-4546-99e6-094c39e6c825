"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { getBlogById } from '@/services/blogApi';
import { Blog } from '@/lib/types';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Loader2, ArrowLeft, Calendar, User, BookOpen } from 'lucide-react';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import {
  Card,
  CardContent,
} from '@/components/ui/card';

interface BlogDetailPageProps {
  params: Promise<{ id: string }>;
}

const BlogDetailPage = ({ params }: BlogDetailPageProps) => {
  const router = useRouter();
  const [blog, setBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [blogId, setBlogId] = useState<string>('');

  useEffect(() => {
    const getParamsId = async () => {
      const { id } = await params;
      setBlogId(id);
    };

    getParamsId();
  }, [params]);

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        if (!blogId) return;

        setIsLoading(true);
        const data = await getBlogById(blogId);
        setBlog(data);
      } catch {
        router.push('/blogs');
      } finally {
        setIsLoading(false);
      }
    };

    if (blogId) {
      fetchBlog();
    }
  }, [blogId, router]);

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMMM dd, yyyy');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background text-foreground">
        <Header />
        <div className="flex justify-center items-center h-[70vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <Footer />
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="min-h-screen bg-background text-foreground">
        <Header />
        <div className="flex flex-col justify-center items-center h-[70vh]">
          <h1 className="text-2xl font-bold mb-4">Blog not found</h1>
          <Button onClick={() => router.push('/blogs')}>
            Go back to blogs
          </Button>
        </div>
        <Footer />
      </div>
    );
  }


  const baseUrl = (process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005').replace(/\/$/, '');
  const imagePath = blog.blogImage ? (blog.blogImage.startsWith('/') ? blog.blogImage : `/${blog.blogImage}`) : '';
  const imageUrl = blog.blogImage ? `${baseUrl}${imagePath}` : '';

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      <main className="container mx-auto py-12 px-4 max-w-5xl">
        <div className="flex items-center mb-8">
          <Button
            variant="outline"
            size="icon"
            className="mr-3 rounded-full hover:bg-[#FD904B]/10 hover:text-[#FD904B] transition-colors"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Back to Blogs</h1>
        </div>

        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">{blog.blogTitle}</h1>

          {blog.class && (
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-8">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-[#FD904B]" />
                <span>{blog.class.firstName} {blog.class.lastName}</span>
              </div>
              <div className="w-1.5 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600"></div>
              <div className="flex items-center gap-2">
                <BookOpen className="h-4 w-4 text-[#FD904B]" />
                <span>{blog.class.className}</span>
              </div>
              <div className="w-1.5 h-1.5 rounded-full bg-gray-300 dark:bg-gray-600"></div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-[#FD904B]" />
                <span>{formatDate(blog.createdAt)}</span>
              </div>
            </div>
          )}
        </div>

        <div className="relative w-full h-[300px] md:h-[500px] mb-10 rounded-2xl overflow-hidden shadow-lg">
          {blog.blogImage && (
            <div className="relative w-full h-full">
              <Image
                src={imageUrl}
                alt={blog.blogTitle}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 80vw"
                priority
              />
            </div>
          )}
        </div>

        <Card className="border-0 shadow-none mb-12">
          <CardContent className="p-0 md:p-4">
            <article
              className="blog-content"
              dangerouslySetInnerHTML={{ __html: blog.blogDescription }}
            />
          </CardContent>
        </Card>


      </main>
      <Footer />
    </div>
  );
};

export default BlogDetailPage;
