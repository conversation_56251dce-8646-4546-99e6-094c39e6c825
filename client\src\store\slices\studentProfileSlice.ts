import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchStudentProfile, updateStudentProfile } from '../thunks/studentProfileThunks';

export interface StudentData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  contact?: string;
  isVerified?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProfileData {
  id?: string;
  studentId?: string;
  medium?: string;
  classroom?: string;
  birthday?: string | Date;
  school?: string;
  address?: string;
  photo?: string;
  documentUrl?: string;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt?: string;
  updatedAt?: string;
  student?: StudentData;
}

export interface ClassroomOption {
  id: number;
  value: string;
}

export interface StudentProfile {
  profile?: ProfileData;
  classroomOptions?: ClassroomOption[];
  coins?: number;
}

interface StudentProfileState {
  profileData: StudentProfile | null;
  loading: boolean;
  error: string | null;
}

const initialState: StudentProfileState = {
  profileData: null,
  loading: false,
  error: null,
};

const studentProfileSlice = createSlice({
  name: 'studentProfile',
  initialState,
  reducers: {
    setStudentProfileData(state, action: PayloadAction<StudentProfile>) {
      state.profileData = action.payload;
      // Persist to localStorage for photo data
      if (typeof window !== 'undefined' && action.payload?.profile?.photo) {
        try {
          localStorage.setItem('student_profile_photo', action.payload.profile.photo);
        } catch (error) {
          console.error('Failed to persist photo to localStorage:', error);
        }
      }
    },
    updateProfilePhoto(state, action: PayloadAction<string | undefined>) {
      if (state.profileData?.profile) {
        state.profileData.profile.photo = action.payload;
        // Persist to localStorage
        if (typeof window !== 'undefined') {
          try {
            if (action.payload) {
              localStorage.setItem('student_profile_photo', action.payload);
            } else {
              localStorage.removeItem('student_profile_photo');
            }
          } catch (error) {
            console.error('Failed to persist photo to localStorage:', error);
          }
        }
      }
    },
    clearStudentProfileData(state) {
      state.profileData = null;
      state.loading = false;
      state.error = null;
      // Clear persisted photo
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem('student_profile_photo');
        } catch (error) {
          console.error('Failed to clear photo from localStorage:', error);
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch student profile
      .addCase(fetchStudentProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudentProfile.fulfilled, (state, action) => {
        state.loading = false;
        // Ensure we're handling the payload correctly
        if (action.payload) {
          state.profileData = action.payload;
        }
      })
      .addCase(fetchStudentProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Update student profile
      .addCase(updateStudentProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateStudentProfile.fulfilled, (state, action) => {
        state.loading = false;
        // Ensure we're handling the payload correctly
        if (action.payload) {
          state.profileData = action.payload;
        }
      })
      .addCase(updateStudentProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setStudentProfileData, updateProfilePhoto, clearStudentProfileData } = studentProfileSlice.actions;
export default studentProfileSlice.reducer;
