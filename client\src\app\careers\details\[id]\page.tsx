import Link from "next/link";
import { Map<PERSON><PERSON>, Chevron<PERSON><PERSON><PERSON>, Clock } from "lucide-react";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { fetchJobById } from "@/services/careerService";

interface CareerDetailsPageProps {
  params: Promise<{ id: string }>;
}

export default async function CareerDetailsPage({ params }: CareerDetailsPageProps) {
  const { id } = await params;
  const job = await fetchJobById(id);

  return (
    <>
      <Header />
      <div className="min-h-screen bg-white text-black dark:bg-black dark:text-gray-100">
        {/* Hero Section */}
        <section className="py-16 bg-black dark:bg-black text-white border-b border-zinc-700">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <Link
              href="/careers"
              className="inline-flex items-center text-gray-400 hover:text-[#FD904B] font-medium mb-6 transition-colors"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Careers
            </Link>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{job.job_title}</h1>
            <div className="flex flex-wrap items-center gap-4 text-gray-300">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-[#FD904B]" />
                Morbi
              </div>
              <span className="bg-[#FD904B] text-white px-3 py-1 rounded-full text-sm font-medium">
                Full-time
              </span>
            </div>
          </div>
        </section>

        {/* Job Description Section */}
        <section className="py-20">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white dark:bg-zinc-900 border border-gray-200 dark:border-zinc-800 rounded-2xl p-8 shadow-lg transition hover:shadow-xl">
              <div className="flex flex-col md:flex-row md:justify-between items-start md:items-center mb-6">
                <h2 className="text-3xl font-bold text-black dark:text-white">
                  About the Role
                </h2>
                <div className="flex items-center text-sm text-gray-400 mt-4 md:mt-0">
                  <Clock className="h-4 w-4 mr-2 text-[#FD904B]" />
                  Posted: {new Date().toLocaleDateString()}
                </div>
              </div>

              <div
                className="prose dark:prose-invert max-w-none leading-relaxed"
                dangerouslySetInnerHTML={{ __html: job.description }}
              />

              {/* CTA */}
              <div className="mt-10">
                <Link
                  href={`/careers/apply/${job.id}`}
                  className="inline-flex items-center px-6 py-3 bg-[#FD904B] text-white font-semibold rounded-full hover:bg-[#e67e22] transition transform hover:scale-105 shadow-lg"
                >
                  Apply Now
                </Link>
              </div>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
}