import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const getUwhizTerminatedStudents = async (applicantIds: any) => {
    let users: any = [];

    users = await prisma.student.findMany({
        where: { id: { in: applicantIds } },
        select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            createdAt: true,
        },
    });

    return users;
}