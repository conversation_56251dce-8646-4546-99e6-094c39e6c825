import express from "express";
import {
    getClasses,
    getStudents,
    getConversationBetween,
    getStudentsForClass
} from "../controllers/chatController";
import { authMiddleware } from "@/middlewares/adminAuth";

const router = express.Router();

router.get("/classes", authMiddleware, getClasses);

router.get("/students", authMiddleware, getStudents);

router.get("/students/:classId", authMiddleware, getStudentsForClass);

router.get("/conversation/:classId/:studentId", authMiddleware, getConversationBetween);

export default router;
