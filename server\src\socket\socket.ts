import express from "express";
import dotenv from "dotenv";
import { createChatRoutes } from "../modules/client/routes/chatRoutes";
import { initializeSocket } from "../modules/client/controllers/socketController";
import { createSocketServer } from "../config/socketConfig";

dotenv.config();

const app = express();

// Create Socket.IO server with production-ready configuration
// Note: The actual HTTP server will be passed from index.ts
let io: any = null;

const onlineUsers = new Map<string, { socketId: string; userType: string; userId: string }>();
const router = createChatRoutes(onlineUsers);

// Initialize socket when HTTP server is available
export const initializeSocketServer = (httpServer: any) => {
  io = createSocketServer(httpServer);
  initializeSocket(io, onlineUsers);
  return io;
};

export { io, app, router, onlineUsers };