import express from "express";
import { Server } from "socket.io";
import dotenv from "dotenv";
import { createChatRoutes } from "../modules/client/routes/chatRoutes";
import { initializeSocket } from "../modules/client/controllers/socketController";

dotenv.config();

const app = express();
const allowedOrigins = [process.env.FRONTEND_URL, process.env.ADMIN_URL];

const io = new Server(
  {
    cors: {
      origin: (origin, callback) => {
        if (!origin || allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error("Not allowed by CORS"));
        }
      },
      methods: ["GET", "POST"],
      credentials: true,
    },
    transports: ['websocket', 'polling'],
    allowEIO3: true,
    pingTimeout: 60000,
    pingInterval: 25000,
  }
);

const onlineUsers = new Map<string, { socketId: string; userType: string; userId: string }>();
const router = createChatRoutes(onlineUsers);

initializeSocket(io, onlineUsers);

export { io, app,router, onlineUsers };