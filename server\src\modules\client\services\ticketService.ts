import prisma from '@/config/prismaClient';

export const generateUniqueTicketCode = async (): Promise<string> => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let ticketCode: string;
  let isUnique = false;

  do {
    ticketCode = '';
    for (let i = 0; i < 6; i++) {
      ticketCode += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    const [smTicket, shivTicket] = await Promise.all([
      prisma.sMWaterParkTicket.findUnique({ where: { ticketCode } }),
      prisma.shivWaterParkTicket.findUnique({ where: { ticketCode } })
    ]);

    isUnique = !smTicket && !shivTicket;
  } while (!isUnique);

  return ticketCode;
};

export const createSMWaterParkTicket = async (studentId: string) => {
  const studentSelect = {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    contact: true
  };

  const existingTicket = await prisma.sMWaterParkTicket.findFirst({
    where: { studentId, status: 'Active' },
    include: { student: { select: studentSelect } }
  });

  if (existingTicket) {
    return existingTicket;
  }

  const ticketCode = await generateUniqueTicketCode();

  return await prisma.sMWaterParkTicket.create({
    data: { studentId, ticketCode, status: 'Active' },
    include: { student: { select: studentSelect } }
  });
};

export const createShivWaterParkTicket = async (studentId: string) => {
  const studentSelect = {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    contact: true
  };

  const existingTicket = await prisma.shivWaterParkTicket.findFirst({
    where: { studentId, status: 'Active' },
    include: { student: { select: studentSelect } }
  });

  if (existingTicket) {
    return existingTicket;
  }

  const ticketCode = await generateUniqueTicketCode();

  return await prisma.shivWaterParkTicket.create({
    data: { studentId, ticketCode, status: 'Active' },
    include: { student: { select: studentSelect } }
  });
};

export const getSMWaterParkTicketByCode = async (ticketCode: string) => {
  const studentSelect = {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    contact: true
  };

  return await prisma.sMWaterParkTicket.findUnique({
    where: { ticketCode },
    include: { student: { select: studentSelect } }
  });
};

export const getShivWaterParkTicketByCode = async (ticketCode: string) => {
  const studentSelect = {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    contact: true
  };

  return await prisma.shivWaterParkTicket.findUnique({
    where: { ticketCode },
    include: { student: { select: studentSelect } }
  });
};

export const getAllSMWaterParkTickets = async (page: number = 1, limit: number = 10, search?: string) => {
  const skip = (page - 1) * limit;
  const studentSelect = {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    contact: true
  };

  // Build where clause for search
  const whereClause: any = {};
  if (search) {
    whereClause.OR = [
      { ticketCode: { contains: search, mode: 'insensitive' } },
      { student: { firstName: { contains: search, mode: 'insensitive' } } },
      { student: { lastName: { contains: search, mode: 'insensitive' } } },
      { student: { email: { contains: search, mode: 'insensitive' } } },
      { student: { contact: { contains: search, mode: 'insensitive' } } }
    ];
  }

  const [tickets, total] = await Promise.all([
    prisma.sMWaterParkTicket.findMany({
      where: whereClause,
      skip,
      take: limit,
      include: { student: { select: studentSelect } },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.sMWaterParkTicket.count({ where: whereClause })
  ]);

  return {
    tickets,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  };
};

export const getAllShivWaterParkTickets = async (page: number = 1, limit: number = 10, search?: string) => {
  const skip = (page - 1) * limit;
  const studentSelect = {
    id: true,
    firstName: true,
    lastName: true,
    email: true,
    contact: true
  };

  // Build where clause for search
  const whereClause: any = {};
  if (search) {
    whereClause.OR = [
      { ticketCode: { contains: search, mode: 'insensitive' } },
      { student: { firstName: { contains: search, mode: 'insensitive' } } },
      { student: { lastName: { contains: search, mode: 'insensitive' } } },
      { student: { email: { contains: search, mode: 'insensitive' } } },
      { student: { contact: { contains: search, mode: 'insensitive' } } }
    ];
  }

  const [tickets, total] = await Promise.all([
    prisma.shivWaterParkTicket.findMany({
      where: whereClause,
      skip,
      take: limit,
      include: { student: { select: studentSelect } },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.shivWaterParkTicket.count({ where: whereClause })
  ]);

  return {
    tickets,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit)
  };
};