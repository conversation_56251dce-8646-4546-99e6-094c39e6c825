import express from "express";
import { getMessageUsersController, getOnlineUsersController, getPrivateMessagesController, getUnreadMessageUsersController } from "../controllers/chatController";


const router = express.Router();

export const createChatRoutes = (onlineUsers: Map<string, { socketId: string; userType: string; userId: string }>) => {
  router.get("/messages/private", getPrivateMessagesController);
  router.get("/messages/users", getMessageUsersController);
  router.get("/messages/unread-users", getUnreadMessageUsersController);
  router.get("/users/online", getOnlineUsersController(onlineUsers));

  return router;
};

export default createChatRoutes;
