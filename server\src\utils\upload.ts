import multer from 'multer';
import path from 'path';
import fs from 'fs';

type MulterStorageOptions = {
  folder: string;
  classIdKey?: string;
  studentIdKey?: string;
};

export function dynamicStorage(options: MulterStorageOptions | string) {
  const opts: MulterStorageOptions = typeof options === 'string'
    ? { folder: options }
    : options;

  return {
    storage: multer.diskStorage({
      destination: (req, file, cb) => {
        let entityId: string | undefined;
        let entityType: string = 'general';

        if (opts.classIdKey && req.class?.id) {
          entityId = req.class.id;
          entityType = 'classes';
        }
        else if (opts.classIdKey && req.params?.[opts.classIdKey]) {
          entityId = req.params[opts.classIdKey];
          entityType = 'classes';
        }
        else if (opts.studentIdKey && req.student?.id) {
          entityId = req.student.id;
          entityType = 'student';
        }
        else if (opts.studentIdKey && req.params?.studentId) {
          entityId = req.params.studentId;
          entityType = 'student';
        }
        else if (typeof options === 'string' && req.student?.id) {
          entityId = req.student.id;
          entityType = 'student';
        }

        if (!entityId) {
          return cb(new Error('Missing entity ID (class or student)'), '');
        }

        let dir: string;

        if (entityType === 'student') {
          dir = path.join('uploads', entityType, entityId);
        } else {
          dir = path.join('uploads', entityType, entityId, opts.folder);
        }

        fs.mkdirSync(dir, { recursive: true });
        cb(null, dir);
      },
      filename: (req, file, cb) => {
        const ext = path.extname(file.originalname);

        if ((opts.studentIdKey && (req.student?.id || req.params?.studentId)) ||
            (typeof options === 'string' && req.student?.id)) {
          if (file.fieldname === 'photo') {
            cb(null, `studentPhoto${ext}`);
          } else if (file.fieldname === 'document') {
            cb(null, `studentDocument${ext}`);
          } else {
            const name = path.basename(file.originalname, ext).replace(/\s+/g, '-');
            const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
            cb(null, `${name}-${uniqueSuffix}${ext}`);
          }
        } else {
          const name = path.basename(file.originalname, ext).replace(/\s+/g, '-');
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          cb(null, `${name}-${uniqueSuffix}${ext}`);
        }
      },
    }),
  };
}
