import { axiosInstance } from '@/lib/axios';
import { Student, StudentProfile } from '@/lib/types';

export interface StudentPaginationResponse {
  students: Student[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export const getStudents = async (page: number = 1, limit: number = 10, filters: { name?: string; email?: string; contact?: string; status?: string } = {}): Promise<StudentPaginationResponse> => {
  try {
    const response = await axiosInstance.get('/student', {
      params: { page, limit, includeProfile: true, ...filters },
    });

    return response.data.data || response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch students: ${error.message}`);
  }
};

export const getStudentProfile = async (studentId: string): Promise<StudentProfile> => {
  try {
    const response = await axiosInstance.get(`/student-profile/admin/${studentId}/all-data`);
    return response.data.data?.profile || response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch student profile: ${error.message}`);
  }
};



export const updateStudentByAdmin = async (studentId: string, jsonData: any): Promise<any> => {
  try {
    const response = await axiosInstance.put(`/student-profile/admin/${studentId}/combined`, jsonData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.data.data || response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to update student: ${error.message}`);
  }
};

export const updateStudentProfileStatus = async (
  studentId: string,
  status: 'PENDING' | 'APPROVED' | 'REJECTED'
): Promise<StudentProfile> => {
  try {
    const response = await axiosInstance.patch(`/student-profile/admin/${studentId}/status`, { status });
    return response.data.data || response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to update student status: ${error.message}`);
  }
};


export const getAllStudentCounts = async () => {
  try {
    const response = await axiosInstance.get('/student/counts');
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get total student count: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const downloadStudentsExcel = async (filters?: {
  name?: string;
  email?: string;
  contact?: string;
  status?: string;
}) => {
  try {
    let url = '/export/students/excel';
    const params = new URLSearchParams();

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all') {
          params.append(key, value);
        }
      });
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await axiosInstance.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', 'students.xlsx');
    document.body.appendChild(link);
    link.click();

    link.parentNode?.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return true;
  } catch (error: any) {
    console.error('Error downloading students Excel file:', error);
    throw new Error(`Failed to download students Excel file: ${error.message}`);
  }
};
