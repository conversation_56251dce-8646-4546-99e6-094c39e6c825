import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export enum FormId {
  PROFILE = 'about',
  DESCRIPTION = 'description',
  PHOTO_LOGO = 'photo_logo',
  EDUCATION = 'education',
  EXPERIENCE = 'experience',
  CERTIFICATES = 'certificates',
  TUTIONCLASS = 'tution_class',
}

interface FormProgressState {
  completedSteps: number;
  totalSteps: number;
  currentStep: number;
  completedForms: Record<string, boolean>;
}

const initialState: FormProgressState = {
  completedSteps: 0,
  totalSteps: 7,
  currentStep: 1,
  completedForms: {
    [FormId.PROFILE]: false,
    [FormId.DESCRIPTION]: false,
    [FormId.PHOTO_LOGO]: false,
    [FormId.EDUCATION]: false,
    [FormId.CERTIFICATES]: false,
    [FormId.EXPERIENCE]: false,
    [FormId.TUTIONCLASS]: false,
  },
};

const formProgressSlice = createSlice({
  name: 'formProgress',
  initialState,
  reducers: {
    completeForm: (state, action: PayloadAction<FormId>) => {
      const formId = action.payload;
      if (!state.completedForms[formId]) {
        state.completedForms[formId] = true;
        state.completedSteps = Math.min(state.completedSteps + 1, state.totalSteps);
      }
    },
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },
  },
});

export const { completeForm, setCurrentStep } = formProgressSlice.actions;
export default formProgressSlice.reducer;
