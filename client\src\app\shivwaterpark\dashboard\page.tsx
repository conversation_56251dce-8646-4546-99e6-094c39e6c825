'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Calendar, Clock, User, Mail, Plus, Loader2, Search, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import {
  addShivWaterParkAdminEntry,
  getShivWaterParkAdminEntries,
  type AdminEntry
} from '@/services/adminticketApi';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const ShivWaterparkDashboard = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [otpValues, setOtpValues] = useState(['', '', '', '', '', '']);
  const [tickets, setTickets] = useState<AdminEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isTableLoading, setIsTableLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const PAGE_SIZE = 10;

  const fetchTickets = useCallback(async (page: number = 1, search: string = '') => {
    setIsTableLoading(true);
    try {
      const response = await getShivWaterParkAdminEntries(page, PAGE_SIZE, search);
      if (response.success) {
        setTickets(response.data.entries);
        setTotalPages(response.data.totalPages);
        setTotalItems(response.data.total);
        setCurrentPage(response.data.page);
      }
    } catch (error: any) {
      console.error('Error fetching admin entries:', error);
      toast.error('Failed to fetch admin entries');
    } finally {
      setIsTableLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchTickets(1, '');
  }, [fetchTickets]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchTickets(1, searchQuery);
      } else {
        setCurrentPage(1);
        fetchTickets(1, searchQuery);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, fetchTickets]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleAddUser = async () => {
    const otpCode = otpValues.join('');
    if (otpCode.length !== 6) {
      toast.error('Please enter all 6 digits');
      return;
    }

    setIsLoading(true);
    try {
      const response = await addShivWaterParkAdminEntry(otpCode);
      if (response.success) {
        toast.success(`Record added: ${response.data!.student.firstName} ${response.data!.student.lastName}`);
        // Refresh the table to show the new record
        fetchTickets(currentPage, searchQuery);
      } else {
        toast.error(response.message || 'Failed to add record');
      }
    } catch (error: any) {
      console.error('Error fetching ticket:', error);
      toast.error(error.response?.data?.message || 'Failed to add record');
    } finally {
      setIsLoading(false);
    }

    setOtpValues(['', '', '', '', '', '']);
    setIsDialogOpen(false);
  };

  const resetForm = () => {
    setOtpValues(['', '', '', '', '', '']);
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Shiv Water Park Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage your water park operations</p>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Record
            </Button>
          </DialogTrigger>
            <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Add User Record
              </DialogTitle>
              <DialogDescription>
                Enter a 6-digit ticket code to add the users record to the table.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="otp">6-Digit Ticket Code</Label>
                <div className="flex gap-2 justify-center">
                  {otpValues.map((value, index) => (
                    <Input
                      key={index}
                      ref={(el) => {
                        inputRefs.current[index] = el;
                      }}
                      type="text"
                      maxLength={1}
                      value={value}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                      className="w-12 h-12 text-center text-lg font-semibold border-2 focus:border-blue-500"
                      placeholder="0"
                    />
                  ))}
                </div>
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                variant="outline"
                onClick={resetForm}
                className="flex-1"
              >
                Reset
              </Button>
              <Button
                onClick={handleAddUser}
                className="flex-1"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Adding...
                  </>
                ) : (
                  'Add Record'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h2 className="text-xl font-semibold text-gray-900">
              Shiv Water Park Records {totalItems > 0 && `(${totalItems})`}
            </h2>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by ticket code, name, email, or contact..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full sm:w-80"
              />
            </div>
          </div>
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Ticket Code</TableHead>
              <TableHead>Student Name</TableHead>
              <TableHead>Contact Info</TableHead>
              <TableHead>Visit Date & Time</TableHead>
              <TableHead className="text-right">Generated At</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isTableLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500 py-8">
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    Loading tickets...
                  </div>
                </TableCell>
              </TableRow>
            ) : tickets.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center text-gray-500 py-8">
                  <div className="text-center">
                    <User className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Records Found</h3>
                    <p className="text-gray-500">
                      {searchQuery ? 'No tickets match your search criteria.' : 'Click "Add Record" button above and enter a 6-digit ticket code to view visitor details.'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              tickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell className="font-mono font-medium">
                    {ticket.ticketCode}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      {ticket.student.firstName} {ticket.student.lastName}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-3 w-3 text-gray-400" />
                        {ticket.student.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {ticket.visitDate ? new Date(ticket.visitDate).toLocaleString() : 'Not set'}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      {new Date(ticket.generatedAt).toLocaleString()}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>

        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * PAGE_SIZE) + 1} to {Math.min(currentPage * PAGE_SIZE, totalItems)} of {totalItems} results
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  setCurrentPage(1);
                  fetchTickets(1, searchQuery);
                }}
                disabled={currentPage === 1 || isTableLoading}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newPage = Math.max(1, currentPage - 1);
                  setCurrentPage(newPage);
                  fetchTickets(newPage, searchQuery);
                }}
                disabled={currentPage === 1 || isTableLoading}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm px-2">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newPage = Math.min(totalPages, currentPage + 1);
                  setCurrentPage(newPage);
                  fetchTickets(newPage, searchQuery);
                }}
                disabled={currentPage === totalPages || isTableLoading}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  setCurrentPage(totalPages);
                  fetchTickets(totalPages, searchQuery);
                }}
                disabled={currentPage === totalPages || isTableLoading}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShivWaterparkDashboard;