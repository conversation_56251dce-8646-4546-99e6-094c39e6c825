import jsPDF from 'jspdf';

const getStudentData = () => {
  try {
    const data = localStorage.getItem("student_data");
    return data ? JSON.parse(data) : null;
  } catch {
    return null;
  }
};

export const generateTicketPDF = async (providedTicketCode?: string, parkType: 'sm' | 'shiv' = 'sm') => {
  try {
    const htmlFile = parkType === 'shiv' ? 'shiv-water-park-ticket.html' : 'sm-water-park-ticket.html';
    const ticketUrl = providedTicketCode
      ? `/${htmlFile}?ticketCode=${encodeURIComponent(providedTicketCode)}`
      : `/${htmlFile}`;
    const ticketWindow = window.open(ticketUrl, '_blank', 'width=1200,height=800');

    if (!ticketWindow) {
      throw new Error('Unable to open ticket window. Please allow popups for this site.');
    }

    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        ticketWindow.close();
        reject(new Error('Timeout loading ticket content'));
      }, 1000);

      ticketWindow.onload = () => {
        clearTimeout(timeout);
        resolve(true);
      };
    });

    await new Promise(resolve => setTimeout(resolve, 2000));

    const ticketContainer = ticketWindow.document.querySelector('.ticket-container') as HTMLElement;

    if (!ticketContainer) {
      ticketWindow.close();
      throw new Error('Ticket container not found');
    }

    const html2canvas = (await import('html2canvas')).default;

    const canvas = await html2canvas(ticketContainer, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 1100,
      height: 450
    });

    ticketWindow.close();

    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    const imgData = canvas.toDataURL('image/png');

    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    const imgWidth = pdfWidth - 20; 
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    const x = 10;
    const y = (pdfHeight - imgHeight) / 2;

    pdf.addImage(imgData, 'PNG', x, y, imgWidth, imgHeight);

    pdf.setFontSize(16);
    pdf.setTextColor(253, 144, 75);
    pdf.text('SM Water Park Ticket', 10, 10);

    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    const timestamp = new Date().toLocaleString();
    pdf.text(`Generated on: ${timestamp}`, 10, pdfHeight - 10);

    const studentData = getStudentData();
    const firstName = studentData?.firstName || 'Student';
    const lastName = studentData?.lastName || 'Name';
    const guestName = `${firstName} ${lastName}`;
    const filename = `SM-Water-Park-Ticket-${guestName.replace(/\s+/g, '-')}.pdf`;
    pdf.save(filename);

    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

export const generateTicketPDFDirect = async (providedTicketCode?: string, parkType: 'sm' | 'shiv' = 'sm') => {
  try {
    const studentData = getStudentData();

    console.log('Student Data from localStorage:', studentData);

    const firstName = studentData?.firstName || 'Student';
    const lastName = studentData?.lastName || 'Name';
    const guestName = `${firstName} ${lastName}`;
    const contact = studentData?.contact || '+91 XXXXXXXXXX';
    const email = studentData?.email || '<EMAIL>';

    const ticketCode = providedTicketCode || 'XXXXXX';

    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Background
    pdf.setFillColor(255, 255, 255);
    pdf.rect(0, 0, pageWidth, pageHeight, 'F');

    // Header section background
    pdf.setFillColor(0, 0, 0);
    pdf.rect(10, 10, 70, pageHeight - 20, 'F');

    // Header text
    pdf.setTextColor(253, 144, 75);
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    const parkName = parkType === 'shiv' ? 'SHIV WATER PARK' : 'SM WATER PARK';
    pdf.text(parkName, 45, 50, { align: 'center' });

    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.text('Dive into Adventure • Make a Splash • Create Memories', 45, 60, { align: 'center' });

    // Visitor Details Section
    pdf.setDrawColor(253, 144, 75);
    pdf.setLineWidth(0.5);
    pdf.rect(90, 20, 90, 50);

    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('🎫 Visitor Details', 95, 30);

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(100, 100, 100);
    pdf.text('Guest Name:', 95, 40);
    pdf.setTextColor(0, 0, 0);
    pdf.text(guestName, 140, 40);

    pdf.setTextColor(100, 100, 100);
    pdf.text('Contact:', 95, 47);
    pdf.setTextColor(0, 0, 0);
    pdf.text(contact, 140, 47);

    pdf.setTextColor(100, 100, 100);
    pdf.text('Email:', 95, 54);
    pdf.setTextColor(0, 0, 0);
    pdf.text(email, 140, 54);

    pdf.setTextColor(100, 100, 100);
    pdf.text('Ticket Type:', 95, 61);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Adult - Full Day', 140, 61);

    // Visit Information Section
    pdf.rect(90, 80, 90, 40);

    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('🎫 Visit Information', 95, 90);

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(100, 100, 100);
    pdf.text('Visit Date:', 95, 100);
    pdf.setTextColor(0, 0, 0);
    pdf.text('March 15, 2025', 140, 100);

    pdf.setTextColor(100, 100, 100);
    pdf.text('Valid Time:', 95, 107);
    pdf.setTextColor(0, 0, 0);
    pdf.text('9:00 AM - 6:00 PM', 140, 107);

    pdf.setTextColor(100, 100, 100);
    pdf.text('Gate Entry:', 95, 114);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Main Entrance', 140, 114);

    // Ticket Code Section
    pdf.setFillColor(253, 144, 75);
    pdf.rect(90, 130, 90, 20, 'F');

    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(8);
    pdf.setFont('helvetica', 'bold');
    pdf.text('TICKET CODE', 135, 138, { align: 'center' });

    pdf.setFontSize(14);
    pdf.text(ticketCode, 135, 146, { align: 'center' });

    pdf.rect(190, 20, 100, 80);

    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('📋 Terms & Conditions', 195, 30);

    pdf.setFontSize(8);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(100, 100, 100);

    const terms = [
      '• All participants must come with their parents',
      '• Entry permitted only once for Uwhiz participants',
      '• Valid only for the date mentioned above',
      '• Entry subject to park capacity and availability',
      '• Children below 3 years allowed free entry',
      '• Outside food and beverages prohibited',
      '• Swimming costumes mandatory for water rides',
      '• Management reserves right to refuse entry'
    ];

    terms.forEach((term, index) => {
      pdf.text(term, 195, 40 + (index * 6));
    });

    // QR Code Section
    pdf.rect(190, 110, 100, 40);

    pdf.setFillColor(253, 144, 75);
    pdf.rect(230, 120, 20, 20, 'F');

    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(8);
    pdf.setFont('helvetica', 'bold');
    pdf.text('QR CODE', 240, 132, { align: 'center' });

    pdf.setFontSize(7);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(100, 100, 100);
    pdf.text('Show this ticket at entrance for quick entry', 240, 145, { align: 'center' });

    pdf.setFontSize(8);
    pdf.setTextColor(100, 100, 100);
    const timestamp = new Date().toLocaleString();
    pdf.text(`Generated on: ${timestamp}`, 10, pageHeight - 5);

    const parkPrefix = parkType === 'shiv' ? 'Shiv' : 'SM';
    const filename = `${parkPrefix}-Water-Park-Ticket-${guestName.replace(/\s+/g, '-')}.pdf`;
    pdf.save(filename);

    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

// Shiv Water Park specific functions
export const generateShivWaterParkTicketPDF = async (providedTicketCode?: string) => {
  return generateTicketPDF(providedTicketCode, 'shiv');
};

export const generateShivWaterParkTicketPDFDirect = async (providedTicketCode?: string) => {
  return generateTicketPDFDirect(providedTicketCode, 'shiv');
};