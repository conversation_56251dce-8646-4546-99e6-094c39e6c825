
require('dotenv').config();

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createMissingEarnings() {
  try {
    console.log('🔄 Starting to create missing earnings for existing referrals...');

    const referralsWithoutEarnings = await prisma.referral.findMany({
      where: {
        referredUserType: 'STUDENT',
        earnings: {
          none: {}
        }
      },
      include: {
        earnings: true,
        referralLink: true
      }
    });

    console.log(`📊 Found ${referralsWithoutEarnings.length} referrals without earnings`);

    let createdCount = 0;
    let skippedCount = 0;

    for (const referral of referralsWithoutEarnings) {
      try {

        // Create earnings for all referrer types (CLASS, ADMIN, and STUDENT)
        await prisma.referralEarning.create({
          data: {
            studentId: referral.referredUserId,
            examId: null,
            referralId: referral.id,
            earningType: 'REGISTRATION',
            amount: 10,
            paymentStatus: 'UNPAID',
          }
        });

        createdCount++;
        console.log(`✅ Created earning for referral: ${referral.id} (Student: ${referral.referredUserEmail}) - Referrer: ${referral.referralLink.userType}`);
      } catch (error) {
        console.error(`❌ Failed to create earning for referral ${referral.id}:`, error);
      }
    }

    console.log(`🎉 Successfully created ${createdCount} missing earnings!`);

    const totalEarnings = await prisma.referralEarning.count();
    const totalAmount = await prisma.referralEarning.aggregate({
      _sum: {
        amount: true
      }
    });

    console.log(`📈 Total earnings records: ${totalEarnings}`);
    console.log(`💰 Total earnings amount: ₹${totalAmount._sum.amount || 0}`);

    const registrationEarnings = await prisma.referralEarning.count({
      where: { earningType: 'REGISTRATION' }
    });
    const uwhizEarnings = await prisma.referralEarning.count({
      where: { earningType: 'UWHIZ_APPLICATION' }
    });

    console.log(`📊 Registration earnings: ${registrationEarnings}`);
    console.log(`📊 U-whiz earnings: ${uwhizEarnings}`);

  } catch (error) {
    console.error('❌ Error creating missing earnings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createMissingEarnings();
