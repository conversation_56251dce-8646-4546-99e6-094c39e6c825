import fs from 'fs';
import path from 'path';

export interface Base64FileData {
  base64Data: string;
  mimeType: string;
  fileName?: string;
}

export interface SavedFileInfo {
  filePath: string;
  fileName: string;
}

export const saveBase64ToFile = async (
  base64Data: string,
  mimeType: string,
  studentId: string,
  fileType: 'photo' | 'document',
  fileName?: string
): Promise<SavedFileInfo> => {
  try {
    const uploadDir = path.join('uploads', 'student', studentId);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Delete existing files of the same type before saving new one
    if (fileType === 'photo') {
      // Delete any existing photo files
      const photoPatterns = ['studentPhoto.jpg', 'studentPhoto.jpeg', 'studentPhoto.png'];
      photoPatterns.forEach(pattern => {
        const existingPhotoPath = path.join(uploadDir, pattern);
        deleteFileIfExists(existingPhotoPath);
      });
    } else if (fileType === 'document') {
      // Delete any existing document files
      const documentPatterns = ['studentDocument.pdf', 'studentDocument.jpg', 'studentDocument.jpeg', 'studentDocument.png'];
      documentPatterns.forEach(pattern => {
        const existingDocPath = path.join(uploadDir, pattern);
        deleteFileIfExists(existingDocPath);
      });
    }

    let extension = '';
    switch (mimeType) {
      case 'image/jpeg':
      case 'image/jpg':
        extension = '.jpg';
        break;
      case 'image/png':
        extension = '.png';
        break;
      case 'application/pdf':
        extension = '.pdf';
        break;
      default:
        if (fileName) {
          extension = path.extname(fileName);
        } else {
          extension = '.bin';
        }
    }

    // Generate file name
    let finalFileName: string;
    if (fileType === 'photo') {
      finalFileName = `studentPhoto${extension}`;
    } else if (fileType === 'document') {
      finalFileName = `studentDocument${extension}`;
    } else {
      const timestamp = Date.now();
      finalFileName = `${fileType}_${timestamp}${extension}`;
    }

    // Full file path
    const filePath = path.join(uploadDir, finalFileName);

    // Convert Base64 to buffer and save
    const buffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(filePath, buffer);

    return {
      filePath: path.join('uploads', 'student', studentId, finalFileName),
      fileName: finalFileName
    };
  } catch (error) {
    console.error('Error saving Base64 file:', error);
    throw new Error('Failed to save file');
  }
};


export const validateBase64FileSize = (base64Data: string, maxSizeKB: number = 1024, minSizeKB: number = 0): boolean => {
  try {
    const byteSize = (base64Data.length * 3) / 4; // Approximate size in bytes
    const sizeInKB = byteSize / 1024;
    return sizeInKB >= minSizeKB && sizeInKB <= maxSizeKB;
  } catch {
    return false;
  }
};


export const validateMimeType = (mimeType: string, fileType: 'photo' | 'document'): boolean => {
  if (fileType === 'photo') {
    return ['image/jpeg', 'image/jpg', 'image/png'].includes(mimeType);
  } else if (fileType === 'document') {
    return ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'].includes(mimeType);
  }
  return false;
};


export const deleteFileIfExists = (filePath: string): void => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);    }
  } catch (error) {
    console.error('Error deleting file:', error);
    // Don't throw error, just log it
  }
};

export const cleanupOldFiles = (oldPhotoPath?: string, oldDocumentPath?: string): void => {
  try {
    // Clean up old photo if exists
    if (oldPhotoPath) {
      const fullOldPhotoPath = path.isAbsolute(oldPhotoPath) ? oldPhotoPath : path.join(process.cwd(), oldPhotoPath);
      deleteFileIfExists(fullOldPhotoPath);
    }

    // Clean up old document if exists
    if (oldDocumentPath) {
      const fullOldDocPath = path.isAbsolute(oldDocumentPath) ? oldDocumentPath : path.join(process.cwd(), oldDocumentPath);
      deleteFileIfExists(fullOldDocPath);
    }
  } catch (error) {
    console.error('Error cleaning up old files:', error);
    // Don't throw error, just log it
  }
};
