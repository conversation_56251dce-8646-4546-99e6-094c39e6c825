import prisma from '@/config/prismaClient';

export const findUserByEmail = (email: string) => {
  return prisma.classes.findUnique({ where: { email } });
};

export const createUser = (
  firstName: string,
  lastName: string,
  email: string,
  password: string
) => {
  let className = '';
  let contactNo = '';

  let randomNumber = Math.floor(Math.random() * 9000) + 1000;

  let username = firstName + randomNumber;

  return prisma.classes.create({
    data: {
      firstName,
      lastName,
      email,
      password,
      className,
      contactNo,
      username,
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      className: true,
      contactNo: true,
      username: true,
    },
  });
};

export const updateUserResetToken = async (
  email: string,
  resetToken: string | null,
  resetTokenExpiry: Date | null
) => {
  return prisma.classes.update({
    where: { email },
    data: {
      resetToken,
      resetTokenExpiry,
    },
  });
};

export const updateUserPassword = async (email: string, password: string) => {
  return prisma.classes.update({
    where: { email },
    data: { password },
  });
};
