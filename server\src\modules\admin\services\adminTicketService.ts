import prisma from '@/config/prismaClient';

export const addSMWaterParkAdminEntry = async (ticketCode: string) => {
  try {
    const ticket = await prisma.sMWaterParkTicket.findUnique({
      where: { ticketCode },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      }
    });

    if (!ticket) {
      return { success: false, message: 'Ticket not found' };
    }

    const existingEntry = await prisma.sMWaterParkAdminEntry.findUnique({
      where: { ticketCode }
    });

    if (existingEntry) {
      return { success: false, message: 'Ticket already entered' };
    }

    const adminEntry = await prisma.sMWaterParkAdminEntry.create({
      data: {
        ticketCode,
        studentId: ticket.studentId,
        visitDate: new Date(),
        enteredBy: 'admin'
      } as any,
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      }
    });

    return {
      success: true,
      data: {
        id: adminEntry.id,
        ticketCode: adminEntry.ticketCode,
        enteredAt: adminEntry.enteredAt,
        student: adminEntry.student,
        visitDate: (adminEntry as any).visitDate,
        generatedAt: ticket.generatedAt
      }
    };
  } catch (error) {
    console.error('Error adding SM Water Park admin entry:', error);
    return { success: false, message: 'Internal server error' };
  }
};

export const getAllSMWaterParkAdminEntries = async (
  page: number = 1,
  limit: number = 10,
  search: string = ''
) => {
  try {
    const skip = (page - 1) * limit;

    const whereClause = search ? {
      OR: [
        { ticketCode: { contains: search, mode: 'insensitive' as const } },
        { student: { firstName: { contains: search, mode: 'insensitive' as const } } },
        { student: { lastName: { contains: search, mode: 'insensitive' as const } } },
        { student: { email: { contains: search, mode: 'insensitive' as const } } },
        { student: { contact: { contains: search } } }
      ]
    } : {};

    const [entries, total] = await Promise.all([
      prisma.sMWaterParkAdminEntry.findMany({
        where: whereClause,
        include: {
          student: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              contact: true
            }
          }
        },
        orderBy: { enteredAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.sMWaterParkAdminEntry.count({ where: whereClause })
    ]);

    const entriesWithTicketInfo = await Promise.all(
      entries.map(async (entry) => {
        const ticket = await prisma.sMWaterParkTicket.findUnique({
          where: { ticketCode: entry.ticketCode },
          select: { generatedAt: true }
        });

        return {
          id: entry.id,
          ticketCode: entry.ticketCode,
          student: entry.student,
          enteredAt: entry.enteredAt,
          visitDate: (entry as any).visitDate,
          generatedAt: ticket?.generatedAt || entry.enteredAt
        };
      })
    );

    return {
      success: true,
      data: {
        entries: entriesWithTicketInfo,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('Error fetching SM Water Park admin entries:', error);
    return { success: false, message: 'Internal server error' };
  }
};

export const addShivWaterParkAdminEntry = async (ticketCode: string) => {
  try {
    const ticket = await prisma.shivWaterParkTicket.findUnique({
      where: { ticketCode },
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      }
    });

    if (!ticket) {
      return { success: false, message: 'Ticket not found' };
    }

    const existingEntry = await prisma.shivWaterParkAdminEntry.findUnique({
      where: { ticketCode }
    });

    if (existingEntry) {
      return { success: false, message: 'Ticket already entered' };
    }

    const adminEntry = await prisma.shivWaterParkAdminEntry.create({
      data: {
        ticketCode,
        studentId: ticket.studentId,
        visitDate: new Date(), // Set current date/time when admin enters the code
        enteredBy: 'admin'
      } as any,
      include: {
        student: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            contact: true
          }
        }
      }
    });

    return {
      success: true,
      data: {
        id: adminEntry.id,
        ticketCode: adminEntry.ticketCode,
        enteredAt: adminEntry.enteredAt,
        student: adminEntry.student,
        visitDate: (adminEntry as any).visitDate, // Use visitDate from admin entry
        generatedAt: ticket.generatedAt
      }
    };
  } catch (error) {
    console.error('Error adding Shiv Water Park admin entry:', error);
    return { success: false, message: 'Internal server error' };
  }
};

export const getAllShivWaterParkAdminEntries = async (
  page: number = 1,
  limit: number = 10,
  search: string = ''
) => {
  try {
    const skip = (page - 1) * limit;

    const whereClause = search ? {
      OR: [
        { ticketCode: { contains: search, mode: 'insensitive' as const } },
        { student: { firstName: { contains: search, mode: 'insensitive' as const } } },
        { student: { lastName: { contains: search, mode: 'insensitive' as const } } },
        { student: { email: { contains: search, mode: 'insensitive' as const } } },
        { student: { contact: { contains: search } } }
      ]
    } : {};

    const [entries, total] = await Promise.all([
      prisma.shivWaterParkAdminEntry.findMany({
        where: whereClause,
        include: {
          student: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              contact: true
            }
          }
        },
        orderBy: { enteredAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.shivWaterParkAdminEntry.count({ where: whereClause })
    ]);

    const entriesWithTicketInfo = await Promise.all(
      entries.map(async (entry) => {
        const ticket = await prisma.shivWaterParkTicket.findUnique({
          where: { ticketCode: entry.ticketCode },
          select: { generatedAt: true }
        });

        return {
          id: entry.id,
          ticketCode: entry.ticketCode,
          student: entry.student,
          enteredAt: entry.enteredAt,
          visitDate: (entry as any).visitDate,
          generatedAt: ticket?.generatedAt || entry.enteredAt
        };
      })
    );

    return {
      success: true,
      data: {
        entries: entriesWithTicketInfo,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('Error fetching Shiv Water Park admin entries:', error);
    return { success: false, message: 'Internal server error' };
  }
};