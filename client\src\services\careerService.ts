import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_CAREER_URL;
const CAREER_KEY = process.env.NEXT_PUBLIC_CAREER_KEY;

export const fetchJobs = async () => {
  const res = await axios.get(`${API_URL}/jobs`, {
    headers: {
      'x-career-key': CAREER_KEY!,
    },
  });
  return res.data.data;
};

export const fetchJobById = async (id: number | string) => {
  const res = await axios.get(`${API_URL}/jobs/${id}`, {
    headers: {
      'x-career-key': CAREER_KEY!,
    },
  });
  return res.data.data;
};

export const saveCareerDetails = async (data: any) => {
  const res = await axios.post(`${API_URL}/save-career-details/`, data, {
    headers: {
      'x-career-key': CAREER_KEY!,
      'Content-Type': 'multipart/form-data', // if you're sending files
    },
  });
  return res.data;
};