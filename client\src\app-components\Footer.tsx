'use client';
import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaLinkedinIn,
  FaTumblr,
  FaPinterestP,
  FaFilePdf,
  FaEnvelope,
} from 'react-icons/fa';

const Footer = () => {
  return (
    <footer className="bg-black text-gray-300 px-6 py-16">
      <div className="container mx-auto max-w-7xl space-y-16">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          <Link href="/" className="flex items-center gap-2">
            <Image
              src="/logo_black.png"
              alt="Logo"
              width={200}
              height={40}
              className="object-contain"
            />
          </Link>

          <div className="flex flex-wrap justify-center gap-1">
            {[
              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },
              {
                href: 'https://www.uest.in/wp-content/uploads/2025/03/Welcome-A-UEST-Product-1.pdf',
                icon: FaFilePdf,
                label: 'Brochure',
              },
              {
                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',
                icon: FaTwitter,
                label: 'Twitter',
              },
              {
                href: 'https://www.facebook.com/share/1FNYcyqawH/',
                icon: FaFacebookF,
                label: 'Facebook',
              },
              {
                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',
                icon: FaInstagram,
                label: 'Instagram',
              },
              {
                href: 'https://www.linkedin.com/company/uest-edtech/',
                icon: FaLinkedinIn,
                label: 'LinkedIn',
              },
              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },
              {
                href: 'https://www.tumblr.com/uestedtech?source=share',
                icon: FaTumblr,
                label: 'Tumblr',
              },
            ].map(({ href, icon: Icon, label }) => (
              <div key={label} className="flex flex-col items-center">
                <Link
                  href={href}
                  className="flex items-center justify-center w-12 h-12 hover:border-gray-400 transition"
                  title={label}
                >
                  <Icon className="text-xl text-white hover:text-gray-400 transition" />
                </Link>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">About</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/verified-classes" className="hover:text-white transition">
                  Find Tutors
                </Link>
              </li>
              <li>
                <Link href="/support" className="hover:text-white transition">
                  Support
                </Link>
              </li>
              <li>
                <Link href="#" className="hover:text-white transition">
                  Pricing
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-4">For Students</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/verified-classes" className="hover:text-white transition">
                  Find Online Tutor
                </Link>
              </li>
              <li>
                <Link href="/support" className="hover:text-white transition">
                  FAQs
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Contact</h3>
            <address className="not-italic text-sm space-y-1 leading-relaxed">
              <p>Head Office</p>
              <p>86, 87 Capital Market, Morbi - 363641</p>
              <p>Contact: +91 96 877 877 88</p>
              <p>Email: <EMAIL></p>
            </address>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Apps</h3>
           <Link href="https://play.google.com/store/apps/details?id=com.uest" target="_blank">
            <Image
              src="/playstore.png"
              alt="Google Play Store"
              width={180}
              height={50}
              className="object-contain"
            />
          </Link>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4">
          <p>© 2025 uest.in. All rights reserved.</p>
          <div className="flex gap-4">
            <Link href="/terms-and-conditions" className="hover:text-white transition">
              Terms & Conditions
            </Link>
            <Link href="/privacy-policy" className="hover:text-white transition">
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
