export const saveExamAttempt = (studentId: string) => {
  const timestamp = Date.now();
  const examAttempts = localStorage.getItem("examAttempts");
  const attempts: { [key: string]: number } = examAttempts ? JSON.parse(examAttempts) : {};
  attempts[studentId] = timestamp;
  localStorage.setItem("examAttempts", JSON.stringify(attempts));
};

export const isAttemptAllowed = (studentId: string): { allowed: boolean; remainingHours: number | null } => {
  const examAttempts = localStorage.getItem("examAttempts");
  if (!examAttempts) {
    return { allowed: true, remainingHours: null };
  }

  const attempts: { [key: string]: number } = JSON.parse(examAttempts);
  const lastAttempt = attempts[studentId];

  if (!lastAttempt) {
    return { allowed: true, remainingHours: null };
  }

  const lastAttemptTime = lastAttempt;
  const currentTime = Date.now();
  const hours24InMs = 24 * 60 * 60 * 1000;
  const timeSinceLastAttempt = currentTime - lastAttemptTime;

  if (timeSinceLastAttempt >= hours24InMs) {
    return { allowed: true, remainingHours: null };
  }

  const remainingMs = hours24InMs - timeSinceLastAttempt;
  const remainingHours = Math.ceil(remainingMs / (60 * 60 * 1000));
  return { allowed: false, remainingHours };
};