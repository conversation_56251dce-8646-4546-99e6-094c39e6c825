import { Router } from 'express';
import {
  generateReferralLink,
  getReferralDashboard,
  getReferralHistoryController,
  getAllReferralLinksController,
  createStaffReferralLinkController,
  deactivateReferralLinkController,
  getReferredUsersController,
  createUwhizEarningController,
  getAllReferralEarningsController,
  updateEarningsPaymentStatusController,
  getReferralLinkEarningsController,
  getOverallEarningsSummaryController,
  getStudentDiscountController,
} from '../controllers/referralController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const referralRouter = Router();

// Routes for Classes
referralRouter.post('/generate-link', authClientMiddleware, generateReferralLink);
referralRouter.get('/dashboard', authClientMiddleware, getReferralDashboard);
referralRouter.get('/history', authClientMiddleware, getReferralHistoryController);

// Routes for Students
referralRouter.post('/generate-link/student', studentAuthMiddleware, generateReferralLink);
referralRouter.get('/dashboard/student', studentAuthMiddleware, getReferralDashboard);
referralRouter.get('/history/student', studentAuthMiddleware, getReferralHistoryController);
referralRouter.get('/discount/student', studentAuthMiddleware, getStudentDiscountController);

// Admin routes
referralRouter.get('/admin/all-links', authMiddleware, getAllReferralLinksController);
referralRouter.post('/admin/create-staff-link', authMiddleware, createStaffReferralLinkController);
referralRouter.patch('/admin/deactivate/:linkId', authMiddleware, deactivateReferralLinkController);
referralRouter.get('/admin/referred-users/:linkId', authMiddleware, getReferredUsersController);

// Earning management routes
referralRouter.post('/admin/create-uwhiz-earning', authMiddleware, createUwhizEarningController);
referralRouter.get('/admin/earnings', authMiddleware, getAllReferralEarningsController);
referralRouter.get('/admin/earnings-summary', authMiddleware, getOverallEarningsSummaryController);
referralRouter.patch('/admin/earnings/payment-status', authMiddleware, updateEarningsPaymentStatusController);
referralRouter.get('/admin/link-earnings/:linkId', authMiddleware, getReferralLinkEarningsController);

// Public route for U-whiz application earning (called from U-whiz server)
referralRouter.post('/uwhiz-earning', createUwhizEarningController);

export default referralRouter;
