import {
  createPriceRankController,
  deletePriceRankController,
  getPriceRanksController,
  updatePriceRankController,
} from "../controllers/uwhizPriceRankingController";

import { Router } from "express";

const uwhizPriceRank = Router();
uwhizPriceRank.post("/", createPriceRankController);
uwhizPriceRank.get("/:examId", getPriceRanksController);
uwhizPriceRank.put("/:id", updatePriceRankController);
uwhizPriceRank.delete("/:id", deletePriceRankController);

export default uwhizPriceRank;
