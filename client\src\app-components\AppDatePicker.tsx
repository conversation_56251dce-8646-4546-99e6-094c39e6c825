'use client';

import { CalendarIcon } from 'lucide-react';
import { format, parse } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from '@/components/ui/popover';
import {
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface DatePickerFieldProps {
  field: {
    value: string;
    onChange: (value: string) => void;
  };
  label: string;
  disabled?: boolean;
}

export function AppDatePicker({ field, label, disabled = false }: DatePickerFieldProps) {
  return (
    <FormItem className="flex flex-col">
      <FormLabel>{label}</FormLabel>
      <Popover>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              type="button"
              variant="outline"
              disabled={disabled}
              className={cn(
                'w-full pl-3 text-left font-normal bg-white dark:bg-black border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-800',
                !field.value && 'text-muted-foreground'
              )}
            >
              {field.value ? field.value : 'Pick a date'}
              <CalendarIcon className="ml-auto h-4 w-4 opacity-50 dark:text-white" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent
          className="w-auto p-0 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 shadow-lg"
          align="start"
        >
          <Calendar
            mode="single"
            captionLayout="dropdown"
            fromYear={1950}
            toYear={new Date().getFullYear()}
            selected={
              field.value ? parse(field.value, 'yyyy-MM-dd', new Date()) : undefined
            }
            onSelect={(date) => {
              if (date) {
                field.onChange(format(date, 'yyyy-MM-dd'));
              }
            }}
            disabled={(date) => date > new Date()}
            initialFocus
            classNames={{
              caption: 'flex justify-center p-2',
              dropdown:
                'mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white',
              caption_label: 'hidden',
            }}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  );
}