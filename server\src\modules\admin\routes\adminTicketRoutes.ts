import { Router } from 'express';
import {
  addSMWaterParkEntry,
  getSMWaterParkEntries,
  addShivWaterParkEntry,
  getShivWaterParkEntries
} from '../controllers/adminTicketController';

const router = Router();

router.post('/sm-water-park/add-entry', addSMWaterParkEntry);
router.get('/sm-water-park/entries', getSMWaterParkEntries);

router.post('/shiv-water-park/add-entry', addShivWaterParkEntry);
router.get('/shiv-water-park/entries', getShivWaterParkEntries);

export default router;