'use client';

import { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { RiLockPasswordLine } from 'react-icons/ri';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { toast } from 'sonner';
import { resetPassword } from '@/services/AuthService';
import { resetStudentPassword } from '@/services/studentAuthServices';
import { Loader2 } from 'lucide-react';

const resetPasswordSchema = z.object({
  newPassword: z.string().min(6, { message: 'Password must be at least 6 characters' }),
});

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const token = searchParams.get('token');
  const userType = searchParams.get('userType');

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      newPassword: '',
    },
  });

  useEffect(() => {
    if (!email || !token) {
      toast.error('Invalid reset link');
      router.push('/');
    }
  }, [email, token, router]);

  const onSubmit = async (data: ResetPasswordFormValues) => {
    if (!email || !token) return;

    setIsSubmitting(true);
    try {
      let response;

      if (userType === 'student') {
        response = await resetStudentPassword(email, token, data.newPassword);
      } else {
        response = await resetPassword(email, token, data.newPassword);
      }

      if (response.success === false) {
        toast.error(response.message || 'Failed to reset password');
        return;
      }
      toast.success(response.message || 'Password reset successful');
      router.push('/');
    } catch (error: any) {
      toast.error(error?.response?.data?.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="newPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>New Password</FormLabel>
              <FormControl>
                <div className="relative">
                  <RiLockPasswordLine
                    className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                    size={20}
                  />
                  <Input
                    type="password"
                    placeholder="New Password"
                    className="pl-10 rounded-lg border-gray-300 focus:ring-orange-500"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="w-full bg-customOrange hover:bg-orange-600 transition-colors"
          disabled={isSubmitting}
        >
          {isSubmitting ? <Loader2 className="h-5 w-5 animate-spin" /> : 'Reset Password'}
        </Button>
      </form>
    </Form>
  );
}
