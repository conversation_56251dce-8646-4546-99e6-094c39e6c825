import { Request, Response } from 'express';
import * as examService from '../services/examServiece';
import { ExamInputType } from '../interfaces/ExamInputType';

export const createExam = async (req: Request, res: Response): Promise<void> => {
  try {
    const examData = req.body as ExamInputType;

    const startDate =
      examData.start_date instanceof Date ? examData.start_date : new Date(examData.start_date);
    if (isNaN(startDate.getTime())) {
      res.status(400).json({ message: 'Invalid start_date format' });
      return;
    }

    const data = examData as ExamInputType;

    const newExam = await examService.createExam(data);
    res.status(201).json(newExam);
  } catch (error: any) {
    res.status(400).json({ message: 'Error creating exam', error: error.message });
  }
};


export const getAllExams = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const { exams, total, currentPage, totalPages } = await examService.getAllExams(page, limit);

    res.status(200).json({
      success: true,
      exams,
      totalPages,
      currentPage,
      total,
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: error.message || 'Failed to fetch exams',
    });
  }
};

export const getExamById = async (req: Request, res: Response): Promise<void> => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid exam ID' });
      return;
    }

    const exam = await examService.getExamById(id);
    if (!exam) {
      res.status(404).json({ message: 'Exam not found' });
      return;
    }
    res.status(200).json(exam);
  } catch (error: any) {
    res.status(400).json({ message: 'Error fetching exam', error: error.message });
  }
};

export const updateExam = async (req: Request, res: Response): Promise<void> => {
  try {
    const id = parseInt(req.params.id);
    const examData = req.body as Partial<ExamInputType>;

    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid exam ID' });
      return;
    }

    // If start_date is provided, validate and transform it
    if (examData.start_date) {
      const startDate =
        examData.start_date instanceof Date ? examData.start_date : new Date(examData.start_date);
      if (isNaN(startDate.getTime())) {
        res.status(400).json({ message: 'Invalid start_date format' });
        return;
      }
      examData.start_date = startDate;
    }

    const updatedExam = await examService.updateExam(id, examData);
    res.status(200).json(updatedExam);
  } catch (error: any) {
    res.status(400).json({ message: 'Error updating exam', error: error.message });
  }
};

export const deleteExam = async (req: Request, res: Response): Promise<void> => {
  try {
    const id = parseInt(req.params.id);
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid exam ID' });
      return;
    }

    const deletedExam = await examService.deleteExam(id);
    res.status(200).json({ message: 'Exam deleted successfully', deletedExam });
  } catch (error: any) {
    res.status(400).json({ message: 'Error deleting exam', error: error.message });
  }
};
