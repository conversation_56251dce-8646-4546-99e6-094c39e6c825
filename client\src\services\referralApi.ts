import { axiosInstance } from '@/lib/axios';

// Get student discount information
export const getStudentDiscount = async () => {
  try {
    const response = await axiosInstance.get('/referral/discount/student');
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get discount info: ${error.response?.data?.message || error.message}`,
    };
  }
};

// Calculate discounted price
export const calculateDiscountedPrice = (originalPrice: string, discountPercentage: number): number => {
  const discountAmount = Math.round((Number(originalPrice) * discountPercentage) / 100);
  return Number(originalPrice) - discountAmount;
};
