"use client";
import { Separator } from "@/components/ui/separator";
import { SidebarNav } from "./components/sidebar-nav";
import { Progress } from "@/components/ui/progress";
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from "@/store";
import { useAuth } from "@/lib/useAuth";
import { useEffect, useState } from "react";
import { axiosInstance } from "@/lib/axios";
import { setClassData } from "@/store/slices/classSlice";
import { evaluateCompletedForms } from "@/lib/helper";
import { Button } from "@/components/ui/button";

const sidebarNavItems = [
  {
    title: "About",
    href: "/classes/profile",
  },
  {
    title: "Description",
    href: "/classes/profile/description",
  },
  {
    title: "Photo & Logo",
    href: "/classes/profile/photo-and-logo",
  },
  {
    title: "Education",
    href: "/classes/profile/education",
  },
  {
    title: "Experience",
    href: "/classes/profile/experience",
  },
  {
    title: "Certificates",
    href: "/classes/profile/certificates",
  },
  {
    title: "Tution Class",
    href: "/classes/profile/tution-class",
  },
];

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const { completedSteps, totalSteps } = useSelector((state: RootState) => state.formProgress);
  const { user } = useAuth();
  const { user: userDetail } : any = useSelector((state: RootState) => state.user);
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasSentForReview, setHasSentForReview] = useState(false);
  const [status, setStatus] = useState("");

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const res = await axiosInstance.get(`/classes/details/${userDetail.id}`);
        dispatch(setClassData(res.data));
        evaluateCompletedForms(res.data, dispatch);
        if (res.data?.status?.status === "PENDING" || res.data?.status?.status === "APPROVED") {
          setHasSentForReview(true);
          setStatus(res.data?.status?.status);
        }
      } catch (err) {
        console.error("Failed to fetch class:", err);
      }
    };

    if (userDetail?.id) {
      fetchDetails();
    }
  }, [userDetail, dispatch]);

  if (!user) return null;

  const progress = (completedSteps / totalSteps) * 100;
  const isProfileComplete = Math.round(progress) === 100;

  const handleSendForReview = async () => {
    try {
      setIsSubmitting(true);
      await axiosInstance.post(`/classes-profile/send-for-review/${userDetail.id}`);
      setHasSentForReview(true);
    } catch (error) {
      console.error("Error sending for review:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="space-y-6 p-10 pb-16 md:block">
        <div className="space-y-0.5">
          <h2 className="text-2xl font-bold tracking-tight">Edit Profile</h2>
          <p className="text-muted-foreground">
            Start creating your public profile. Your progress will be
            automatically saved as you complete each section. You can return at
            any time to finish your registration.
          </p>
        </div>
        <Progress value={progress} className="h-2" />
        <p className="text-sm text-muted-foreground">
          {Math.round(progress)}% complete
        </p>
        {isProfileComplete && (
          <div className="mt-4">
            {hasSentForReview ? (
              <Button
                className="bg-gray-400 text-white cursor-not-allowed"
                disabled
              >
                {status === "APPROVED"
                  ? "Profile Approved ✅"
                  : "Profile Sent for Review"}
              </Button>
            ) : (
              <Button
                className="bg-green-600 hover:bg-green-700 text-white"
                disabled={isSubmitting}
                onClick={handleSendForReview}
              >
                Send for Review
              </Button>
            )}
          </div>
        )}

        <Separator className="my-6" />
        <div className="flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0">
          <aside className="-mx-4 lg:w-1/6">
            <SidebarNav items={sidebarNavItems} />
          </aside>
          <div className="flex justify-center w-full">
            <div className="flex-1 lg:max-w-2xl">{children}</div>
          </div>
        </div>
      </div>
    </>
  );
}