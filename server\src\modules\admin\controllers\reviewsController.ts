import { Request, Response } from 'express';
import prisma from "@/config/prismaClient";

export const getAdminTestimonials = async (req: Request, res: Response) => {
  try {
    const testimonials = await prisma.classesReviews.findMany({
      include: {
        class: {
          select: {
            id: true,
            className: true,
            firstName: true,
            lastName: true,
            ClassAbout: {
              select: {
                classesLogo: true,
                profilePhoto: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json(testimonials);
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    res.status(500).json({ message: 'Failed to fetch testimonials' });
  }
};

export const deleteAdminTestimonial = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    if (!id) {
      res.status(400).json({ message: 'Testimonial ID is required' });
      return;
    }

    await prisma.classesReviews.delete({
      where: {
        id
      }
    });

    res.status(200).json({ message: 'Testimonial deleted successfully' });
  } catch (error) {
    console.error('Error deleting testimonial:', error);
    res.status(500).json({ message: 'Failed to delete testimonial' });
  }
};
