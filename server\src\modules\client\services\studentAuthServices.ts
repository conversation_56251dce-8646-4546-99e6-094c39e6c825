import prisma from "@/config/prismaClient";
import { Status } from "@prisma/client";

export const findStudentByEmail = (email: string) => {
  return prisma.student.findUnique({ where: { email } });
};

export const createStudent = (
  firstName: string,
  lastName: string,
  email: string,
  contact: string,
  password: string
) => {
  return prisma.student.create({
    data: {
      firstName,
      lastName,
      email,
      contact,
      password,
    },
  });
};

export const updateStudentResetToken = async (
  email: string,
  resetToken: string | null,
  resetTokenExpiry: Date | null
) => {
  return prisma.student.update({
    where: { email },
    data: {
      resetToken,
      resetTokenExpiry,
    },
  });
};

export const updateStudentPassword = async (
  email: string,
  password: string
) => {
  return prisma.student.update({
    where: { email },
    data: { password },
  });
};

export const getAllStudent = async (
  skip: number,
  limit: number,
  includeProfile: boolean = false,
  filters: {
    name?: string;
    email?: string;
    contact?: string;
    status?: Status;
  } = {}
) => {
  const students = await prisma.student.findMany({
    skip,
    take: limit,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      contact: true,
      isVerified: true,
      createdAt: true,
      updatedAt: true,
      profile: includeProfile,
    },
    where: {
      ...(filters.name && {
        OR: [
          { firstName: { contains: filters.name, mode: "insensitive" } },
          { lastName: { contains: filters.name, mode: "insensitive" } },
        ],
      }),
      ...(filters.email && {
        email: { contains: filters.email, mode: "insensitive" },
      }),
      ...(filters.contact && {
        contact: { contains: filters.contact, mode: "insensitive" },
      }),
      ...(filters.status && {
        profile: {
          status: filters.status,
        },
      }),
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  const studentIds = students.map((s) => s.id);

  const coins = await prisma.uestCoins.findMany({
    where: {
      modelType: "STUDENT",
      modelId: { in: studentIds },
    },
  });

  const coinsMap = new Map(coins.map((coin) => [coin.modelId, coin.coins]));

  const enrichedStudents = students.map((student) => ({
    ...student,
    coins: coinsMap.get(student.id) ?? 0,
  }));

  return enrichedStudents;
};

export const getAllStudentCount = () => {
  return prisma.student.count();
};

export const updateStudent = async (
  id: string,
  data: {
    firstName?: string;
    lastName?: string;
    contact?: string;
  }
) => {
  return prisma.student.update({
    where: { id },
    data,
  });
};



export const getAllStudentsCounts = async () => {
  const [totalStudents, pending, approved, rejected, totalCoinsResult] =
    await Promise.all([
      prisma.student.count(),
      prisma.student.count({ where: { profile: { status: "PENDING" } } }),
      prisma.student.count({ where: { profile: { status: "APPROVED" } } }),
      prisma.student.count({ where: { profile: { status: "REJECTED" } } }),
      prisma.uestCoins.aggregate({
        where: {
          modelType: "STUDENT",
        },
        _sum: {
          coins: true,
        },
      }),
    ]);

  return {
    total : totalStudents,
    pending,
    approved,
    rejected,
    totalCoins: totalCoinsResult._sum.coins ?? 0,
  };
};
