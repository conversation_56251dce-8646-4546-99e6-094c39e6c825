import { axiosInstance } from '@/lib/axios';

export interface Ticket {
  id: string;
  studentId: string;
  ticketCode: string;
  status: string;
  parkType: string;
  visitDate?: string;
  createdAt: string;
  updatedAt: string;
  generatedAt: string;
  student: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    contact?: string;
  };
}

export interface GenerateTicketResponse {
  success: boolean;
  data: Ticket;
  message: string;
}

// Water Park specific ticket functions
export const generateSMWaterParkTicket = async (data?: { visitDate?: string }): Promise<GenerateTicketResponse> => {
  const response = await axiosInstance.post('/sm-water-park/generate', data || {});
  return response.data;
};

export const generateShivWaterParkTicket = async (data?: { visitDate?: string }): Promise<GenerateTicketResponse> => {
  const response = await axiosInstance.post('/shiv-water-park/generate', data || {});
  return response.data;
};