import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const seedConstants = async () => {
  try {
    const categories = [
      { name: 'Education', details: [] },
      { name: 'Board Type', details: ['Gujarat', 'CBSE', 'ICSE', 'IB', 'Cambridge'] },
      {
        name: 'Section',
        details: [
          'Pre-school',
          'Pre-primary',
          'Primary',
          'Secondary',
          'Higher Secondary',
          'Undergraduate',
          'Postgraduate',
          'Vocational',
          'Diploma',
        ],
      },
      { name: 'Medium', details: ['Gujarati', 'English', 'Hindi'] },
      {
        name: 'Subject',
        details: [
          'Mathematics',
          'Gujarati',
          'Hindi',
          'English',
          'Science',
          'Social Studies',
          'Physics',
          'Chemistry',
          'Biology',
          'Computer Science',
        ],
      },
      { name: 'Coaching Type', details: ['Personal', 'Group', 'Online', 'Hybrid'] },
      {
        name: 'Drama',
        details: [
          'Theater',
          'Stage Performance',
          'Comedy',
          'Tragedy',
          'Improv',
          'Musical Theater',
          'Method Acting',
          'Scriptwriting',
          'Directing',
        ],
      },
      {
        name: 'Music',
        details: [
          'Piano',
          'Guitar',
          'Violin',
          'Drums',
          'Vocal Training',
          'Flute',
          'Sitar',
          'Music Composition',
        ],
      },
      { name: 'Art & Craft', details: ['Painting', 'Sculpting', 'Pottery', 'Sketching'] },
      { name: 'Sports', details: ['Hockey', 'Football', 'Cricket', 'Basketball', 'Tennis'] },
      {
        name: 'Languages',
        details: [
          'English',
          'Gujarati',
          'Hindi',
          'French',
          'Spanish',
          'German',
          'Mandarin',
          'Japanese',
        ],
      },
      {
        name: 'Technology',
        details: ['Python', 'JavaScript', 'Machine Learning', 'Web Development', 'Data Science'],
      },
      { name: 'Arts', details: ['Painting', 'Sculpting', 'Photography', 'Graphic Design'] },
    ];

    for (const category of categories) {
      const createdCategory = await prisma.constantCategory.upsert({
        where: { name: category.name },
        update: {},
        create: { name: category.name },
      });

      if (category.details.length > 0) {
        const details = category.details.map((value) => ({
          value,
          categoryId: createdCategory.id,
        }));

        await prisma.constantDetail.createMany({
          data: details,
          skipDuplicates: true,
        });
      }
    }

    console.log('Seeding completed!');
  } catch (e) {
    console.error('Seeding failed:', e);
    throw e;
  } finally {
    await prisma.$disconnect();
  }
};

seedConstants();
