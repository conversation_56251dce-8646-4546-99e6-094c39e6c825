'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';

interface SMWaterparkLayoutProps {
  children: React.ReactNode;
}

const SMWaterparkLayout = ({ children }: SMWaterparkLayoutProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('sm_admin_token');
      const isLoggedIn = !!token;
      setIsAuthenticated(isLoggedIn);
      setIsLoading(false);

      if (!isLoggedIn && pathname !== '/smwaterpark/login') {
        router.push('/smwaterpark/login');
      }

      if (isLoggedIn && pathname === '/smwaterpark/login') {
        router.push('/smwaterpark/dashboard');
      }
    };

    checkAuth();
  }, [pathname, router]);

  const handleLogout = () => {
    localStorage.removeItem('sm_admin_token');
    localStorage.removeItem('sm_admin_data');
    toast.success('Logged out successfully');
    router.push('/smwaterpark/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (pathname === '/smwaterpark/login') {
    return <>{children}</>;
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-lg">
                  <Image
                    src="/sm_water_park.png"
                    alt="SM Water Park Logo"
                    width={40}
                    height={40}
                    className="rounded-md object-contain"
                  />
                </span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">SM Water Park</h1>
                <p className="text-sm text-gray-600">Admin Dashboard</p>
              </div>
            </div>

            <div className="flex items-center gap-4">              
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
};

export default SMWaterparkLayout;