'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Exam } from '@/lib/types';
import { toZonedTime } from 'date-fns-tz';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { toast } from 'sonner';

interface ExamStatusButtonProps {
  exam: Exam;
  hasApplied: boolean;
  isMaxLimitReached: boolean;
  hasAttempted: boolean;
  onApplyClick: () => void;
}

const ExamStatusButton = ({
  exam,
  hasApplied,
  isMaxLimitReached,
  hasAttempted,
  onApplyClick,
}: ExamStatusButtonProps) => {
  const router = useRouter();
  const isTutorLoggedIn = useSelector((state: RootState) => state.user.isAuthenticated); 
  const [examStatus, setExamStatus] = useState<'countdown' | 'start' | 'finished'>('countdown');
  const [isStartWindowActive, setIsStartWindowActive] = useState(false);
  const [isRegistrationOpen, setIsRegistrationOpen] = useState(false);

  useEffect(() => {
    const calculateStatus = () => {
      const startTime = new Date(exam.start_date).getTime();
      const startRegistrationDate = exam.start_registration_date
        ? new Date(exam.start_registration_date).getTime()
        : null;

      if (isNaN(startTime)) {
        console.error(`Invalid start_date for exam ${exam.id}: ${exam.start_date}`);
        setExamStatus('finished');
        setIsStartWindowActive(false);
        setIsRegistrationOpen(false);
        return;
      }

      const now = toZonedTime(new Date(), 'Asia/Kolkata').getTime();
      const startWindowEnd = startTime + exam.duration * 60 * 1000;

      if (startRegistrationDate && now < startRegistrationDate) {
        setIsRegistrationOpen(false);
      } else {
        setIsRegistrationOpen(true);
      }

      if (now < startTime) {
        setExamStatus('countdown');
        setIsStartWindowActive(false);
      } else if (now >= startTime && now <= startWindowEnd) {
        setExamStatus('start');
        setIsStartWindowActive(true);
      }  
      else {
        setExamStatus('finished');
        setIsStartWindowActive(false);
      }
    };

    calculateStatus();
    const interval = setInterval(calculateStatus, 1000);

    return () => clearInterval(interval);
  }, [exam.start_date, exam.duration, exam.id, exam.start_registration_date]);

  const handleStartExam = () => {
    router.push(`/uwhiz-exam/${exam.id}`);
  };

  const handleViewResult = () => {
    router.push(`/uwhiz-details/${exam.id}`);
  };

  const handleApplyClick = () => {
    if (isTutorLoggedIn) {
      toast.error('You are currently logged in as a tutor. Please log out and then log in as a student to apply for UWhiz.');
      return;
    }
    onApplyClick();
  };

  if (hasAttempted) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 mb-4 mx-5">
        <Button
          className="w-full bg-gray-400 text-white font-semibold py-2 rounded-lg cursor-not-allowed"
          disabled
        >
          Attempted
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center gap-4 mb-4 mx-5">
      {examStatus === 'countdown' ? (
        <Button
          onClick={handleApplyClick}
          className="w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          disabled={isMaxLimitReached || hasApplied || !isRegistrationOpen}
        >
          {hasApplied
            ? 'Applied'
            : isMaxLimitReached
            ? 'Max Limit Reached'
            : !isRegistrationOpen
            ? 'Registration Will Start Soon'
            : 'Apply Now'}
        </Button>
      ) : examStatus === 'start' ? (
        <Button
          onClick={handleStartExam}
          className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          disabled={!isStartWindowActive || !hasApplied}
        >
          {hasApplied ? 'Start Exam Now' : 'You Have Not Applied'}
        </Button>
      ) : (
        <Button
        disabled
          onClick={handleViewResult}
          className="w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
        >
          Result Will Announce Soon
        </Button>
      )}
    </div>
  );
};

export default ExamStatusButton;