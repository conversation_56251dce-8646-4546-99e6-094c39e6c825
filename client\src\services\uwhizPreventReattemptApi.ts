import { axiosInstance } from '@/lib/axios';

export const uwhizPreventReattempApi = async (studentId: string,examId:number) => {
  try {
    const response = await axiosInstance.get(`check-attempt?studentId=${studentId}&examId=${examId}`,{
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed To Get Student And Exam Detail: ${error.response?.data?.message || error.message}`,
    };
  }
};