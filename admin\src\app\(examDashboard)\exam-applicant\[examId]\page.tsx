'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getExamApplicant, downloadExamApplicantsExcel } from '@/services/uwhizExamApplicantApi';
import { createColumnHelper, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { ApiResponse, Applicants } from '@/lib/types';
import { toast } from 'sonner';
import { Download } from 'lucide-react';

const columnHelper = createColumnHelper<Applicants>();

const columns = [
    columnHelper.accessor(row => `${row.firstName} ${row.lastName}`, {
        header: 'Name',
        cell: info => info.getValue(),
    }),
    columnHelper.accessor('email', {
        header: 'Email',
        cell: info => info.getValue(),
    }),
    columnHelper.accessor('contact', {
        header: 'Contact',
        cell: info => info.getValue(),
    }),

    columnHelper.accessor('createdAt', {
        header: 'Applied At',
        cell: info => new Date(info.getValue()).toLocaleString('en-IN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }),
    }),
];

export default function ExamApplicantsTable() {
    const { examId } = useParams();
    const [data, setData] = useState<Applicants[]>([]);
    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(1);
    const [isDownloading, setIsDownloading] = useState(false);
    const limit = 10;

    useEffect(() => {
        const fetchData = async () => {
            if (typeof examId !== 'string') return;
            const response: ApiResponse = await getExamApplicant(parseInt(examId), page, limit);
            if (response.success === false) {
                return;
            }
            setData(response.data);
            setTotal(response.total);
        };
        fetchData();
    }, [examId, page]);

    const handleDownloadExcel = async () => {
        if (typeof examId !== 'string') return;

        setIsDownloading(true);
        try {
            await downloadExamApplicantsExcel(parseInt(examId));
            toast.success('Excel file downloaded successfully');
        } catch (error) {
            toast.error('Failed to download Excel file');
            console.error('Download error:', error);
        } finally {
            setIsDownloading(false);
        }
    };

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
    });

    const totalPages = Math.ceil(total / limit);
    const isDataEmpty = data.length === 0;

    return (
        <div className="p-4">
            <div className="overflow-x-auto rounded-lg shadow-sm">
                <div className="flex justify-between items-center pb-5">
                    <h1 className="text-2xl font-bold">Exam Applicants</h1>
                    <button
                        onClick={handleDownloadExcel}
                        disabled={isDownloading || isDataEmpty}
                        className="flex items-center gap-2 px-4 py-2 bg-[#ff914d] text-white rounded-lg hover:bg-[#e8823d] disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <Download className="h-4 w-4" />
                        {isDownloading ? 'Downloading...' : 'Download xlsx'}
                    </button>
                </div>
                <table className="min-w-full border border-gray-300 rounded-lg">
                    <thead>
                        {table.getHeaderGroups().map(headerGroup => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <th key={header.id} className="border-b border-gray-300 px-4 py-2 text-left text-sm font-medium bg-gray-50">
                                        {flexRender(header.column.columnDef.header, header.getContext())}
                                    </th>
                                ))}
                            </tr>
                        ))}
                    </thead>
                    <tbody>
                        {isDataEmpty ? (
                            <tr>
                                <td colSpan={columns.length} className="px-4 py-2 text-sm text-center text-gray-600">
                                    No Applicant Data
                                </td>
                            </tr>
                        ) : (
                            table.getRowModel().rows.map(row => (
                                <tr key={row.id} className="border-b border-gray-300">
                                    {row.getVisibleCells().map(cell => (
                                        <td key={cell.id} className="px-4 py-2 text-sm">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </td>
                                    ))}
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
            <div className="flex justify-between items-center mt-4">
                <span className="text-sm text-gray-600">
                    {total} entries
                </span>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => setPage(1)}
                        disabled={page === 1 || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'<<'}
                    </button>
                    <button
                        onClick={() => setPage(page - 1)}
                        disabled={page === 1 || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'<'}
                    </button>
                    <span className="text-sm">
                        Page {page} of {totalPages}
                    </span>
                    <button
                        onClick={() => setPage(page + 1)}
                        disabled={page === totalPages || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'>'}
                    </button>
                    <button
                        onClick={() => setPage(totalPages)}
                        disabled={page === totalPages || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'>>'}
                    </button>
                </div>
            </div>
        </div>
    );
}