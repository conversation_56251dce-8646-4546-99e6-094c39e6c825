import { Request, Response } from 'express';
import * as examService from '../services/uwhizCertificateServices';

export const getExamParticipants = async (req: Request, res: Response): Promise<any> => {
  try {
    const { examId, classId } = req.query;

    // Check if at least one parameter is provided
    if (!examId && !classId) {
      return res.status(400).json({
        success: false,
        error: 'At least one of examId or classId is required',
      });
    }

    const participants = await examService.getExamParticipants(
      examId ? parseInt(examId as string) : undefined,
      classId as string | undefined
    );

    // if (participants.length === 0) {
    //   return res.status(400).json({
    //     success: false,
    //     message: 'No participants found who took the exam',
    //   });
    // }

    res.status(200).json({
      success: true,
      data: participants,
    });
  } catch (error) {
    console.error('Error in getExamParticipants controller:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
    });
  }
};
