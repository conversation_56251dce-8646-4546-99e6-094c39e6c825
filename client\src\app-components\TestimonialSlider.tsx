'use client';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { useState, useEffect, useRef } from 'react';
import { Star } from 'lucide-react';
import { axiosInstance } from '@/lib/axios';

interface Testimonial {
  id: string;
  message: string;
  rating: number;
  status: string;
  createdAt: string;
  class: {
    id: string;
    className: string;
    fullName?: string;
    classesLogo?: string | null;
    profilePhoto?: string | null;
  }
}

const renderStars = (rating: number) => {
  return [...Array(5)].map((_, index) => (
    <Star
      key={index}
      className={`w-4 h-4 ${index < rating ? 'fill-[#FD904B] text-[#FD904B]' : 'text-gray-300'}`}
    />
  ));
};

const TestimonialCard = ({ testimonial }: { testimonial: Testimonial }) => {

  const name = testimonial.class.fullName || testimonial.class.className;
  const role = testimonial.class.className;
  const quote = testimonial.message;
  const rating = testimonial.rating;
  const imageSrc = testimonial.class.classesLogo
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.class.classesLogo}`
    : testimonial.class.profilePhoto
      ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.class.profilePhoto}`
      : '/teacher-profile.jpg';

  return (
    <div className="inline-flex flex-shrink-0 w-[360px] mx-4">
      <motion.div
        className="dark:bg-siderbar rounded-3xl p-8 w-full relative overflow-hidden border-2 border-gray-200 dark:border-gray-700"
        whileHover={{
          scale: 1.02,
          borderColor: "#FD904B",
          zIndex: 1
        }}
      >
        <div className="flex items-center gap-4 mb-6">
          <motion.div
            className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0 border-2 border-gray-200 dark:border-gray-700"
            whileHover={{ scale: 1.1 }}
          >
            <Image
              src={imageSrc}
              alt={name}
              fill
              className="object-cover"
            />
          </motion.div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-base dark:text-white text-gray-800 truncate">
              {name}
            </h3>
            <p className="text-orange-500 text-sm font-medium truncate">
              {role}
            </p>
            <div className="flex items-center gap-1 mt-1">
              {renderStars(rating)}
            </div>
          </div>
        </div>

        <div className="pt-4 border-t border-gray-200">
          <p className="text-gray-700 text-base leading-relaxed break-words line-clamp-3 italic dark:text-white">
            &quot;{quote}&quot;
          </p>
        </div>
      </motion.div>
    </div>
  );
};

const InfiniteSlider = ({ direction = 1, testimonials }: { direction?: number, testimonials: Testimonial[] }) => {
  const [width, setWidth] = useState(0);
  const carousel = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateWidth = () => {
      if (carousel.current) {
        const singleSetWidth = testimonials.length * (360 + 32);
        setWidth(singleSetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, [testimonials.length]);


  const repeatedTestimonials = [];
  for (let i = 0; i < 6; i++) {
    repeatedTestimonials.push(...testimonials);
  }

  return (
    <div className="overflow-hidden">
      <motion.div
        ref={carousel}
        className="flex"
        animate={{
          x: direction > 0 ? [-width, 0] : [0, -width]
        }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: 40,
            ease: "linear",
            times: [0, 1]
          }
        }}
        style={{
          gap: '32px'
        }}
      >
        {repeatedTestimonials.map((testimonial, index) => (
          <TestimonialCard
            key={`${testimonial.id}-${index}`}
            testimonial={testimonial}
          />
        ))}
      </motion.div>
    </div>
  );
};

const TestimonialSlider = () => {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchApprovedTestimonials = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get('/testimonials/approved');
        setTestimonials(response.data);
      } catch (err) {
        console.error('Error fetching testimonials:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchApprovedTestimonials();
  }, []);

  if (!loading && testimonials.length === 0) {
    return null;
  }

  return (
    <section className="py-20 dark:bg-slidebar">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16 px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent dark:text-white"
          >
            What Our Clients Say

          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-gray-600 text-lg"
          >
            Trusted by thousands of satisfied customers
          </motion.p>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#FD904B]"></div>
          </div>
        ) : (
          <div className="space-y-12">

            <div className="relative">
              <InfiniteSlider direction={-1} testimonials={testimonials} />
              <div className="absolute left-0 top-0 bottom-0 w-40  z-10" />
              <div className="absolute right-0 top-0 bottom-0 w-40 z-10" />
            </div>

            {testimonials.length > 5 && (
              <div className="relative">
                <InfiniteSlider direction={1} testimonials={testimonials} />
                <div className="absolute left-0 top-0 bottom-0 w-40 z-10" />
                <div className="absolute right-0 top-0 bottom-0 w-40 z-10" />
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

export default TestimonialSlider;