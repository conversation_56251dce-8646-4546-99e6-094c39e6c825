import { Router } from 'express';
import * as examController from '../controllers/examController';
import { examSchema, examUpdateSchema, paginationSchema } from '../requests/examValidator';
import { validate, validateQuery } from '../middelware/examValidate';
import { authMiddleware } from '@/middlewares/adminAuth';

const examRouter = Router();

examRouter.post('/', authMiddleware, validate(examSchema), examController.createExam);
examRouter.put('/:id', authMiddleware, validate(examUpdateSchema), examController.updateExam);
examRouter.get('/', validateQuery(paginationSchema), examController.getAllExams);
examRouter.get('/:id', examController.getExamById);
examRouter.delete('/:id', authMiddleware, examController.deleteExam);

export default examRouter;
