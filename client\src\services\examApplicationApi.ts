import { axiosInstance } from "@/lib/axios";

// Apply for an exam
export const applyForExam = async (
  examId: number,
  applicantId: string
): Promise<any> => {
  try {
    const response = await axiosInstance.post(
      "/examApplication",
      {
        examId,
        applicantId,
      },
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.error || "Failed to apply for exam");
  }
};

// Get all exam applications (for admin)
export const getAllExamApplications = async (
  page: number = 1,
  limit: number = 10,
  examId?: number
): Promise<any> => {
  try {
    const params: any = { page, limit };
    if (examId !== undefined) {
      params.examId = examId;
    }
    const response = await axiosInstance.get(`/examApplication/${examId}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
      params,
    },);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.error || "Failed to fetch exam applications"
    );
  }
};
