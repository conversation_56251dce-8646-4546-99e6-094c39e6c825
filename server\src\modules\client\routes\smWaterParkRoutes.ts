import { Router } from 'express';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import {
  generateSMWaterParkTicketController,
  getSMWaterParkTicketByCodeController,
  getAllSMWaterParkTicketsController
} from '../controllers/ticketController';

const smWaterParkRouter = Router();



smWaterParkRouter.post('/generate', studentAuthMiddleware, generateSMWaterParkTicketController);

smWaterParkRouter.get('/ticket/:ticketCode', getSMWaterParkTicketByCodeController);

smWaterParkRouter.get('/admin/all', getAllSMWaterParkTicketsController);

export default smWaterParkRouter;