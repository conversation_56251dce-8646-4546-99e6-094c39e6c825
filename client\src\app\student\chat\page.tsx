'use client';

import { Suspense, useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import SharedChat from '@/app-components/SharedChat';
import { isStudentAuthenticated } from '@/lib/utils';

function ChatWithParams() {
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [username, setUsername] = useState('');
    const [userId, setUserId] = useState('');
    const searchParams = useSearchParams();
    const selectedUserId = searchParams.get('userId');
    const selectedUserName = searchParams.get('userName');

    useEffect(() => {
        const studentIsAuthenticated = isStudentAuthenticated();
        setIsAuthenticated(studentIsAuthenticated);

        if (studentIsAuthenticated) {
            const studentData = localStorage.getItem('student_data');
            if (studentData) {
                try {
                    const parsedData = JSON.parse(studentData);
                    const studentName = `${parsedData.firstName} ${parsedData.lastName}` || parsedData.email.split('@')[0];
                    setUsername(studentName);
                    setUserId(parsedData.id);
                } catch (error) {
                    console.error('Error parsing student data:', error);
                }
            }
        } else {
            const userData = localStorage.getItem('user');
            if (userData) {
                try {
                    const parsedData = JSON.parse(userData);
                    const userName = `${parsedData.firstName} ${parsedData.lastName}` || parsedData.email.split('@')[0];
                    setUsername(userName);
                    setUserId(parsedData.id);
                    setIsAuthenticated(true);
                } catch (error) {
                    console.error('Error parsing user data:', error);
                }
            }
        }
    }, []);

    return (
        <SharedChat
            userType="student"
            isAuthenticated={isAuthenticated}
            username={username}
            userId={userId}
            loginPath="/"
            initialSelectedUserId={selectedUserId || undefined}
            initialSelectedUserName={selectedUserName || undefined}
        />
    );
}

export default function ChatUI() {
    return (
        <Suspense>
            <ChatWithParams />
        </Suspense>
    );
}