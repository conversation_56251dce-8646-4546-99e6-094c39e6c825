import type { Metada<PERSON> } from "next";
import "./globals.css";
import { ReduxProvider } from "@/store/provider";
import { Toaster } from "@/components/ui/sonner";
import { Balsamiq_Sans } from "next/font/google";
import Script from "next/script";
import AnalyticsProvider from "@/hooks/AnalyticsProvider";
import { Suspense } from "react";
import { GA_TRACKING_ID } from "@/lib/gtag";

const balsamiq = Balsamiq_Sans({
  subsets: ["latin"],
  weight: ["400", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: 'Uest - Your Gateway to Educational Excellence',
  description: 'Your Gateway to Educational Excellence',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
            strategy="afterInteractive"
          />
          <Script id="gtag-init" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${GA_TRACKING_ID}', {
                page_path: window.location.pathname,
              });
            `}
          </Script>
        </>
      </head>
      <body className={`antialiased ${balsamiq.className}`}>
        <ReduxProvider>
          <Suspense fallback={null}>
            {process.env.NODE_ENV === 'production' && (

              <AnalyticsProvider />
            )}
          </Suspense>
          {children}
          <Toaster />
        </ReduxProvider>
      </body>
    </html>
  );
}