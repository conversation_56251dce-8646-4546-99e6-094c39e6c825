import { Request, Response } from 'express';
import { addShivWaterParkAdminEntry, addSMWaterParkAdminEntry, getAllShivWaterParkAdminEntries, getAllSMWaterParkAdminEntries } from '../services/adminTicketService';


export const addSMWaterParkEntry = async (req: Request, res: Response): Promise<void> => {
  try {
    const { ticketCode } = req.body;

    if (!ticketCode || ticketCode.length !== 6) {
      res.status(400).json({
        success: false,
        message: 'Valid 6-digit ticket code is required'
      });
      return;
    }

    const result = await addSMWaterParkAdminEntry(ticketCode.toUpperCase());

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in addSMWaterParkEntry:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getSMWaterParkEntries = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = (req.query.search as string) || '';

    const result = await getAllSMWaterParkAdminEntries(page, limit, search);

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('Error in getSMWaterParkEntries:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const addShivWaterParkEntry = async (req: Request, res: Response): Promise<void> => {
  try {
    const { ticketCode } = req.body;

    if (!ticketCode || ticketCode.length !== 6) {
      res.status(400).json({
        success: false,
        message: 'Valid 6-digit ticket code is required'
      });
      return;
    }

    const result = await addShivWaterParkAdminEntry(ticketCode.toUpperCase());

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in addShivWaterParkEntry:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

export const getShivWaterParkEntries = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = (req.query.search as string) || '';

    const result = await getAllShivWaterParkAdminEntries(page, limit, search);

    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('Error in getShivWaterParkEntries:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};