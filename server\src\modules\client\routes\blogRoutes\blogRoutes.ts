import express, { Request as ExpressRequest, Response, NextFunction } from "express";
import multer from "multer";
import {
  createBlog,
  getAllBlogs,
  getBlogByIdHandler,
  updateBlog,
  updateBlogStatus,
  deleteBlog,
  getApprovedBlogs,
  getMyBlogs,
} from "../../controllers/blogController/blogController";
import validateRequest from "@/middlewares/validateRequest";
import { createBlogSchema, updateBlogSchema, updateBlogStatusSchema } from "../../requests/blogRequests/blogRequest";
import { authMiddleware } from "@/middlewares/adminAuth";
import { authClientMiddleware } from "@/middlewares/clientAuth";
import { dynamicStorage } from "@/utils/upload";

interface CustomRequest extends ExpressRequest {
  admin?: {
    id: string;
    [key: string]: any;
  };
  class?: {
    id: string;
    [key: string]: any;
  };
}

const asRequestHandler = (handler: any) => handler as express.RequestHandler;

const blogRouter = express.Router();

const uploadBlogImage = multer(
  dynamicStorage({ folder: "blogs", classIdKey: "classId" })
);

blogRouter.get("/my-blogs", authClientMiddleware, asRequestHandler(getMyBlogs));
blogRouter.get("/approved", asRequestHandler(getApprovedBlogs));
blogRouter.get("/", authMiddleware, asRequestHandler(getAllBlogs));
blogRouter.get("/:id", asRequestHandler(getBlogByIdHandler));

blogRouter.post("/",authClientMiddleware,uploadBlogImage.single("blogImage"),validateRequest(createBlogSchema),asRequestHandler(createBlog)
);

blogRouter.put(
  "/:id",
  authClientMiddleware,
  uploadBlogImage.single("blogImage"),
  validateRequest(updateBlogSchema),
  asRequestHandler(updateBlog)
);

blogRouter.patch(
  "/:id/status",
  authMiddleware,
  validateRequest(updateBlogStatusSchema),
  asRequestHandler(updateBlogStatus)
);

const combinedAuthMiddleware = (req: CustomRequest, res: Response, next: NextFunction) => {
  authMiddleware(req as any, res, (err?: any) => {
    if (!err && req.admin) {
      return next();
    }
    authClientMiddleware(req as any, res, next);
  });
};

blogRouter.delete("/:id", asRequestHandler(combinedAuthMiddleware), asRequestHandler(deleteBlog));

export default blogRouter;
