import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const truncateThought = (text: string, wordLimit: number = 5): string => {
  const words = text.trim().split(/\s+/);
  if (words.length <= wordLimit) return text;
  return words.slice(0, wordLimit).join(' ') + '...';
};

export const setStudentAuthToken = (token: string) => {
  localStorage.setItem('studentToken', token);
};

export const getStudentAuthToken = (): string | null => {
  return localStorage.getItem('studentToken');
};

export const clearStudentAuthToken = () => {
  localStorage.removeItem('studentToken');
};

export const isStudentAuthenticated = (): boolean => {
  return !!getStudentAuthToken();
};