"use client";

import { useState, useEffect, useCallback } from 'react';
import { Star } from 'lucide-react';
import { toast } from 'sonner';
import { useSelector } from 'react-redux';
import { axiosInstance } from '@/lib/axios';
import Image from 'next/image';
import { Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/useAuth';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Testimonial } from '@/lib/types';


const Testimonials = () => {
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [isConfirmDelete, setIsConfirmDeleteOpen] = useState(false);
  const [testimonialToDelete, setTestimonialToDelete] = useState<string | null>(null);
  const router = useRouter();
  const { user: isAuthenticated } = useAuth();

  const { user } = useSelector((state: any) => state.user);
  const classId = user?.id;

  const fetchTestimonials = useCallback(async () => {
    try {
      const response = await axiosInstance.get(`/testimonials/class/${classId}`);
      setTestimonials(response.data);
    } catch {
      toast.error('Failed to fetch testimonials');
    }
  }, [classId]);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/?authError=1');
      return;
    }

    if (classId) {
      fetchTestimonials();
    }
  }, [classId, isAuthenticated, router, fetchTestimonials]);

  const handleStarClick = (selectedRating: number) => {
    setRating(selectedRating);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    if (message.trim().length < 10) {
      toast.error('Please write a message with at least 10 characters');
      return;
    }

    setIsSubmitting(true);

    try {
      await axiosInstance.post('/testimonials', {
        classId,
        rating,
        message,
      });

      toast.success('Testimonial submitted successfully!');
      setMessage('');
      setRating(0);
      fetchTestimonials();
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to submit testimonial');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${index < rating ? 'fill-[#FD904B] text-[#FD904B]' : 'text-gray-300'
          }`}
      />
    ));
  };

  const openDeleteConfirmation = (testimonialId: string) => {
    setTestimonialToDelete(testimonialId);
    setIsConfirmDeleteOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!testimonialToDelete) return;

    try {
      await axiosInstance.delete(`/testimonials/${testimonialToDelete}`);
      toast.success('Testimonial deleted successfully!');
      fetchTestimonials();
      setIsConfirmDeleteOpen(false);
      setTestimonialToDelete(null);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to delete testimonial');
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">

      <div className="dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-6">Write a Testimonial</h2>

        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Your Rating</label>
          <div className="flex gap-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => handleStarClick(star)}
                className="focus:outline-none"
              >
                <Star
                  className={`w-8 h-8 ${star <= rating
                    ? 'fill-[#FD904B] text-[#FD904B]'
                    : 'text-gray-300'
                    }`}
                />
              </button>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">
              Your Message
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FD904B] focus:border-transparent"
              rows={4}
              placeholder="Share your experience..."
              required
              minLength={10}
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-[#FD904B] text-white py-2 px-4 rounded-lg hover:bg-[#FD904B]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Testimonial'}
          </button>
        </form>
      </div>

      <div>
        <h2 className="text-2xl font-semibold mb-4">Class Testimonials</h2>
        {testimonials.length > 0 ? (
          <div className="space-y-4">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="dark:bg-slidebar border border-gray-400 rounded-lg shadow-sm p-4">
                <div className="flex items-center gap-3 mb-4">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden border-2 border-[#FD904B]/20">
                    <Image
                      src={testimonial.class.classesLogo
                        ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.class.classesLogo}`
                        : testimonial.class.profilePhoto
                          ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${testimonial.class.profilePhoto}`
                          : ''}
                      alt={testimonial.class.className || 'Class logo'}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 48px"
                    />
                  </div>
                  <div className='flex-1'>
                    <div className='flex justify-between items-center'>
                      <h4 className="font-medium text-gray-600 dark:text-white">
                        {testimonial.class.fullName || testimonial.class.className}
                      </h4>

                      <Trash2
                      onClick={() => openDeleteConfirmation(testimonial.id)}
                      className='h-4 w-4 text-red-500 cursor-pointer hover:text-red-700' />
                    </div>
                    <div className="flex items-center gap-1 mt-1">
                      {renderStars(testimonial.rating)}
                    </div>
                  </div>

                </div>
                <p className="text-gray-700 break-words mb-3">{testimonial.message}</p>
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>Posted on {new Date(testimonial.createdAt).toLocaleDateString()}</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${testimonial.status === 'APPROVED'
                    ? 'bg-green-100 text-green-800'
                    : testimonial.status === 'REJECTED'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                    }`}>
                    {testimonial.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500">No testimonials yet for this class.</p>
        )}
      </div>

      {/* Delete Dialog */}
      <AlertDialog open={isConfirmDelete} onOpenChange={setIsConfirmDeleteOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the testimonial.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsConfirmDeleteOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-500 hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Testimonials;
