'use client';

import { useReactTable, getCoreRowModel, flexRender, ColumnDef } from '@tanstack/react-table';
import { useMemo, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../app/store';
import { useRouter } from 'next/navigation';
import {
  fetchExams,
  deleteExamAsync,
  openDeleteDialog,
  openFormDialog,
  closeDeleteDialog,
  closeFormDialog,
  setCurrentPage,
} from '@/app/examSlice';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  BadgeCheck,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeftIcon,
  ChevronsRightIcon,
  ChevronUpCircleIcon,
  MoreHorizontal,
} from 'lucide-react';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import ExamForm from './ExamForm';
import { FaAt, FaBan, FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';
import { Exam } from '@/lib/types';
import { FaMedal } from 'react-icons/fa6';

export default function ExamTable() {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const {
    exams,
    currentPage,
    totalPages,
    totalRecords,
    limit,
    isDeleteDialogOpen,
    isFormDialogOpen,
    examToDelete,
    loading,
    error,
  } = useSelector((state: RootState) => state.exam);

  useEffect(() => {
    dispatch(fetchExams({ page: currentPage, limit }));
  }, [currentPage, limit, dispatch]);

  const handleEdit = useCallback(
    (type: 'question' | 'form' | 'result', exam: Exam) => {
      if (type === 'question') {
        router.push(`/question/${exam.id}`);
      } else if (type === 'result') {
        router.push(`/exam-detail/result/${exam.id}`);
      } else {
        dispatch(openFormDialog(exam));
      }
    },
    [dispatch, router]
  );

  const handleDelete = useCallback(
    (id: number) => {
      dispatch(openDeleteDialog(id));
    },
    [dispatch]
  );

  // Define columns inside the component
  const columns = useMemo<ColumnDef<Exam>[]>(
    () => [
      {
        header: 'Exam Name',
        accessorKey: 'exam_name',
      },
      {
        header: 'Start Date',
        accessorKey: 'start_date',
        cell: ({ row }) => new Date(row.original.start_date).toLocaleString(),
      },
      {
        header: 'Duration',
        accessorKey: 'duration',
        cell: ({ row }) => `${row.original.duration} min`,
      },
      {
        header: 'Marks',
        accessorKey: 'marks',
      },
      {
        header: 'Level',
        accessorKey: 'level',
      },
      {
        header: 'Max Participants',
        accessorKey: 'total_student_intake',
      },
      {
        header: 'Total Questions',
        accessorKey: 'total_questions',
      },
      {
        header: 'Apply Date',
        accessorKey: 'start_registration_date',
        cell: ({ row }) =>
          row.original.start_registration_date
            ? new Date(row.original.start_registration_date).toLocaleString()
            : 'N/A',
      },
      {
        header: 'Coins Deduction',
        accessorKey: 'coins_required',
      },
      {
        header: 'Exam Type',
        accessorKey: 'exam_type',
      },
      {
        header: 'Actions',
        cell: ({ row }) => {
          const exam = row.original;
          return (
            <div className="flex gap-1">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white hover:bg-slate-300"
                  >
                    <MoreHorizontal />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem
                                    onClick={() => handleEdit('form', exam)}
                    className="flex items-center gap-2"

                  >
                                    <FaEdit className='h-4 w-4' />

                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleEdit('result', exam)}
                    className="flex items-center gap-2"
                  >
                    <BadgeCheck className="h-4 w-4" />
                    View Results
                  </DropdownMenuItem>
                  <DropdownMenuItem
                  onClick={() => handleDelete(exam.id)}
                  className='flex items-center gap-2'
                  >
                    <MdDeleteForever className='h-4 w-4'/>
                    Delete
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push(`/rank-price/${exam.id}`)}
                    className="flex items-center gap-2"
                  >
                    <FaMedal className="h-4 w-4" />
                    Rank & Price
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push(`/exam-applicant/${exam.id}`)}
                    className="flex items-center gap-2"
                  >
                    <FaAt className="h-4 w-4" />
                    Applicants
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push(`/exam-prefrence/${exam.id}`)}
                    className="flex items-center gap-2"
                  >
                    <ChevronUpCircleIcon className="h-4 w-4" />
                    exam Weightage
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => router.push(`/uwhiz-terminated-students/${exam.id}`)}
                    className="flex items-center gap-2"
                  >
                    <FaBan className="h-4 w-4" />
                    Terminated Students
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
      },
    ],
    [handleEdit, handleDelete, router]
  );

  const table = useReactTable({
    data: exams,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="space-y-6 w-full">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-foreground">Exams</h1>
        <Button onClick={() => dispatch(openFormDialog(null))}>Add Exam</Button>
      </div>

      {loading ? (
        <p>Loading exams...</p>
      ) : error ? (
        <p className="text-red-500">Error: {error}</p>
      ) : (
        <div className="overflow-x-auto border rounded-md">
          <Table>
            <TableHeader className="sticky top-0 bg-muted">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      <div className="flex items-center justify-between px-4">
        <div className="text-sm text-muted-foreground">{totalRecords} entries</div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => dispatch(setCurrentPage(1))}
            disabled={currentPage === 1}
          >
            <ChevronsLeftIcon />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => dispatch(setCurrentPage(currentPage - 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeftIcon />
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="icon"
            onClick={() => dispatch(setCurrentPage(currentPage + 1))}
            disabled={currentPage === totalPages}
          >
            <ChevronRightIcon />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => dispatch(setCurrentPage(totalPages))}
            disabled={currentPage === totalPages}
          >
            <ChevronsRightIcon />
          </Button>
        </div>
      </div>

      {/* Dialogs */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={() => dispatch(closeDeleteDialog())}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>This action cannot be undone.</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => dispatch(closeDeleteDialog())}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => dispatch(deleteExamAsync(examToDelete!))}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isFormDialogOpen} onOpenChange={(open) => { if (!open) dispatch(closeFormDialog()); }}>
        <DialogContent
          onInteractOutside={(e) => e.preventDefault()} 
          onEscapeKeyDown={(e) => e.preventDefault()} 
        >
          <DialogHeader>
            <VisuallyHidden>
              <DialogTitle>Add/Edit Exam</DialogTitle>
            </VisuallyHidden>
            <DialogDescription className="sr-only">Form to add or edit exam</DialogDescription>
            <DialogClose asChild>
              <button
                className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                <span className="sr-only">Close</span>
              </button>
            </DialogClose>
          </DialogHeader>
          <ExamForm />
        </DialogContent>
      </Dialog>
    </div>
  );
}