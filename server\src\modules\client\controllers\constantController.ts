import { Request, Response } from "express";
import {
  getAllConstants,
  getConstantsByCategory,
} from "../services/constantService";

export const fetchAllConstants = async (req: Request, res: Response) => {
  try {
    const constants = await getAllConstants();
    res.status(200).json(constants);
  } catch (error) {
    res.status(500).json({ message: "Error fetching constants", error });
  }
};

export const fetchConstantsByCategory = async (req: Request, res: Response): Promise<any> => {
  const { category } = req.params;
  try {
    const constants = await getConstantsByCategory(category);
    if (!constants) {
      return res.status(404).json({ message: "Category not found" });
    }
    res.status(200).json(constants);
  } catch (error) {
    res.status(500).json({ message: "Error fetching constants", error });
  }
};
