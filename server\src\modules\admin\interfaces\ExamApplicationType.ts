export interface ExamApplication {
  id: string;
  examId: number;
  classId: string;
  createdAt: Date;
  exam: {
    exam_name: string;
    total_questions: number;
    marks: number;
    level: string;
    duration: number;
    start_date: Date;
    coins_required: number | null;
  };
  classes: { firstName: string; lastName: string; className: string | null };
}

export interface PaginationResponse {
  applications: ExamApplication[];
  total: number;
  currentPage: number;
  totalPages: number;
}
