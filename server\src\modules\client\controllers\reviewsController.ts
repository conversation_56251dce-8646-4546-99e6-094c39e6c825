import {
  createUserReviews,
  getUserReviewsByClassId,
  getAverageRatingByClassId,
  deleteReviewsById,
  getAllReviews,
  checkStudentReviewExists,
  getReviewByStudent
} from "../services/reviewsService";
import { Request, Response } from 'express';
import prisma from "@/config/prismaClient";
import { transporter } from "@/utils/email";
import { createReviewNotificationTemplate } from "@/utils/emailTemplates";

export const createReviews = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.student || !req.student.id) {
      res.status(401).json({ message: 'Only students can create reviews' });
      return;
    }

    const { classId, message, rating, studentName } = req.body;

    if (!message || !rating) {
      res.status(400).json({ message: 'Missing required fields' });
      return;
    }


    if (rating < 1 || rating > 5) {
      res.status(400).json({ message: 'Rating must be between 1 and 5' });
      return;
    }

    const hasExistingReview = await checkStudentReviewExists(req.student.id, classId);
    if (hasExistingReview) {
      res.status(400).json({
        message: 'You have already submitted a review for this class.'
      });
      return;
    }

    let reviewStudentName = studentName;
    if (!reviewStudentName) {
      const student = await prisma.student.findUnique({
        where: { id: req.student.id },
        select: { firstName: true, lastName: true }
      });

      if (student) {
        reviewStudentName = `${student.firstName} ${student.lastName}`;
      }
    }

    const reviews = await createUserReviews({
      classId,
      message,
      rating,
      studentId: req.student.id,
      studentName: reviewStudentName,
      userType: "STUDENT"
    });


    const classDetails = await getReviewByStudent(req.student.id, classId);

    if (classDetails && classDetails.email) {
      const className = classDetails.className || `${classDetails.firstName} ${classDetails.lastName}'s Class`;

      const emailHtml = createReviewNotificationTemplate(
        className,
        reviewStudentName,
        rating,
        message
      );

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: classDetails.email,
        subject: `New Review from ${reviewStudentName}`,
        html: emailHtml
      });
    }

    res.status(200).json({ message: 'Review submitted successfully!', reviews });

  } catch (error) {
    console.error('Error creating reviews:', error);
    res.status(500).json({ message: 'Failed to create reviews' });
  }
};

export const getReviewsByClassId = async (req: Request, res: Response) => {
  try {
    const { classId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 5;

    if (!classId) {
      res.status(400).json({ message: 'Class ID is required' });
      return;
    }

    if (page < 1 || limit < 1) {
      res.status(400).json({ message: 'Invalid pagination parameters' });
      return;
    }

    const result = await getUserReviewsByClassId(classId, page, limit);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    res.status(500).json({ message: 'Failed to fetch reviews' });
  }
};

export const getAverageRating = async (req: Request, res: Response) => {
  try {
    const { classId } = req.params;

    if (!classId) {
      res.status(400).json({ message: 'Class ID is required' });
      return;
    }

    const averageRating = await getAverageRatingByClassId(classId);
    res.status(200).json({ averageRating });
  } catch (error) {
    console.error('Error calculating average rating:', error);
    res.status(500).json({ message: 'Failed to calculate average rating' });
  }
};

export const deleteReviews = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!id) {
      res.status(400).json({ message: 'Review ID is required' });
      return;
    }

    const review = await prisma.classesReviews.findUnique({
      where: { id }
    });

    if (!review) {
      res.status(404).json({ message: 'Review not found' });
      return;
    }

    await deleteReviewsById(id);
    res.status(200).json({ message: 'Review deleted successfully' });
  } catch (error) {
    console.error('Error deleting reviews:', error);
    res.status(500).json({ message: 'Failed to delete reviews' });
  }
};

export const getReviews = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (page < 1 || limit < 1) {
      res.status(400).json({ message: 'Invalid pagination parameters' });
      return;
    }

    const reviews = await getAllReviews(page, limit);
    res.status(200).json(reviews);
  } catch (error) {
    console.error('Error fetching all reviews:', error);
    res.status(500).json({ message: 'Failed to fetch reviews' });
  }
};