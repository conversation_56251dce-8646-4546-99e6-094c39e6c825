import prisma from '@/config/prismaClient';

export const findClassById = (classId: string) => {
  return prisma.classes.findUnique({
    where: { id: classId }
  });
};

export const findWishlistItem = (studentId: string, classId: string) => {
  return prisma.studentWishlist.findUnique({
    where: {
      studentId_savedClassId: {
        studentId,
        savedClassId: classId
      }
    }
  });
};

export const createWishlistItem = (studentId: string, classId: string) => {
  return prisma.studentWishlist.create({
    data: {
      studentId,
      savedClassId: classId
    }
  });
};

export const findWishlistItemById = (id: string, studentId: string) => {
  return prisma.studentWishlist.findFirst({
    where: {
      id,
      studentId
    }
  });
};

export const deleteWishlistItem = (id: string) => {
  return prisma.studentWishlist.delete({
    where: { id }
  });
};

export const getWishlistItems = (studentId: string, skip: number, limit: number) => {
  return prisma.studentWishlist.findMany({
    where: { studentId },
    include: {
      savedClass: {
        include: {
          ClassAbout: true,
          status: true
        }
      }
    },
    skip,
    take: limit,
    orderBy: { createdAt: 'desc' }
  });
};

export const countWishlistItems = (studentId: string) => {
  return prisma.studentWishlist.count({
    where: { studentId }
  });
};
