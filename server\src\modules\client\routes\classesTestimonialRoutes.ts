import express from 'express';
import {
  createTestimonialHandler,
  getClassTestimonialsHandler,
  updateTestimonialStatusHandler,
  getAllTestimonialsHandler,
  getApprovedTestimonialsHandler,
  deleteTestimonialHandler
} from '../controllers/classesTestimonialController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

router.post('/', authClientMiddleware, createTestimonialHandler);

router.get('/class/:classId', getClassTestimonialsHandler);

router.patch('/:id/status', authMiddleware, updateTestimonialStatusHandler);

router.get('/', getAllTestimonialsHandler);

router.get('/approved', getApprovedTestimonialsHandler);

router.delete('/:id', authClientMiddleware, deleteTestimonialHandler);

router.delete('/admin/:id', authMiddleware, deleteTestimonialHandler);

export default router;