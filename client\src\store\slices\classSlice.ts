import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { fetchClassDetails } from '../thunks/classThunks';

interface ClassState {
  classData: any | null;
  loading: boolean;
  error: string | null;
}

const initialState: ClassState = {
  classData: null,
  loading: false,
  error: null,
};

const classSlice = createSlice({
  name: 'class',
  initialState,
  reducers: {
    setClassData(state, action: PayloadAction) {
      state.classData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchClassDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchClassDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.classData = action.payload;
      })
      .addCase(fetchClassDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setClassData } = classSlice.actions;
export default classSlice.reducer;
