import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const getPrivateMessages = async (userId1: string, userId2: string) => {
  const messages = await prisma.chatMessage.findMany({
    where: {
      OR: [
        {
          senderId: userId1,
          recipientId: userId2
        },
        {
          senderId: userId2,
          recipientId: userId1
        }
      ]
    },
    orderBy: {
      timestamp: 'asc'
    }
  });

  const messagesWithNames = await Promise.all(messages.map(async (message) => {
    let senderName = '';
    let recipientName = '';

    if (message.senderType === 'student') {
      const student = await prisma.student.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
    } else if (message.senderType === 'class') {
      const classUser = await prisma.classes.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
    }

    if (message.recipientId && message.recipientType === 'student') {
      const student = await prisma.student.findUnique({
        where: { id: message.recipientId },
        select: { firstName: true, lastName: true }
      });
      recipientName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
    } else if (message.recipientId && message.recipientType === 'class') {
      const classUser = await prisma.classes.findUnique({
        where: { id: message.recipientId },
        select: { firstName: true, lastName: true }
      });
      recipientName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
    }

    return {
      ...message,
      sender: senderName,
      recipient: recipientName
    };
  }));

  return messagesWithNames;
};

export const getMessageUsers = async (userId: string, userType: 'student' | 'class') => {
  const messages = await prisma.chatMessage.findMany({
    where: {
      OR: [
        { senderId: userId, senderType: userType },
        { recipientId: userId, recipientType: userType }
      ]
    },
    select: {
      senderId: true,
      senderType: true,
      recipientId: true,
      recipientType: true
    }
  });

  const uniqueUserIds = new Set<string>();

  messages.forEach((message: any) => {
    if (message.senderId !== userId && message.senderId) {
      uniqueUserIds.add(message.senderId);
    }
    if (message.recipientId !== userId && message.recipientId) {
      uniqueUserIds.add(message.recipientId);
    }
  });

  const usersWithIds: Array<{ username: string; userId: string }> = [];
  for (const id of uniqueUserIds) {
    const student = await prisma.student.findUnique({
      where: { id },
      select: { firstName: true, lastName: true }
    });

    if (student) {
      usersWithIds.push({
        username: `${student.firstName} ${student.lastName}`,
        userId: id
      });
    } else {
      const classUser = await prisma.classes.findUnique({
        where: { id },
        select: { firstName: true, lastName: true }
      });
      if (classUser) {
        usersWithIds.push({
          username: `${classUser.firstName} ${classUser.lastName}`,
          userId: id
        });
      }
    }
  }

  return usersWithIds;
};

export const getPendingMessages = async (userId: string, userType: 'student' | 'class') => {
  const messages = await prisma.chatMessage.findMany({
    where: {
      recipientId: userId,
      recipientType: userType
    },
    orderBy: {
      timestamp: 'asc'
    }
  });

  const messagesWithNames = await Promise.all(messages.map(async (message) => {
    let senderName = '';

    if (message.senderType === 'student') {
      const student = await prisma.student.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
    } else if (message.senderType === 'class') {
      const classUser = await prisma.classes.findUnique({
        where: { id: message.senderId },
        select: { firstName: true, lastName: true }
      });
      senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
    }

    return {
      ...message,
      sender: senderName
    };
  }));

  return messagesWithNames;
};

export const createMessage = async (
  text: string,
  senderId: string,
  senderType: 'student' | 'class',
  recipientId?: string,
  recipientType?: 'student' | 'class'
) => {
  if (!text || !senderId || !senderType) {
    throw new Error('Missing required parameters: text, senderId, and senderType are required');
  }

  if (text.length > 250) {
    throw new Error('Message text too long. Maximum 250 characters allowed.');
  }

  if (!['student', 'class'].includes(senderType)) {
    throw new Error('Invalid sender type. Must be "student" or "class".');
  }

  if (recipientType && !['student', 'class'].includes(recipientType)) {
    throw new Error('Invalid recipient type. Must be "student" or "class".');
  }

  try {
    const message = await prisma.chatMessage.create({
      data: {
        text: text.trim(),
        senderId,
        senderType,
        recipientId: recipientId,
        recipientType,
        timestamp: new Date(),
      },
    });

    return message;
  } catch (error) {
    throw new Error('Failed to save message to database');
  }
};

export const getUnreadMessageUsers = async (userId: string, userType: 'student' | 'class') => {
  const unreadMessages = await prisma.chatMessage.findMany({
    where: {
      recipientId: userId,
      recipientType: userType,
      isRead: false
    },
    select: {
      senderId: true,
      senderType: true
    },
    distinct: ['senderId']
  });

  const uniqueUserIds = new Set<string>();
  unreadMessages.forEach((message: any) => {
    if (message.senderId !== userId) {
      uniqueUserIds.add(message.senderId);
    }
  });

  const userDetails = await Promise.all(
    Array.from(uniqueUserIds).map(async (senderId) => {
      const senderType = unreadMessages.find(msg => msg.senderId === senderId)?.senderType;

      if (senderType === 'student') {
        const student = await prisma.student.findUnique({
          where: { id: senderId },
          select: { firstName: true, lastName: true }
        });
        return {
          userId: senderId,
          username: student ? `${student.firstName} ${student.lastName}` : 'Unknown',
          userType: 'student'
        };
      } else if (senderType === 'class') {
        const classUser = await prisma.classes.findUnique({
          where: { id: senderId },
          select: { firstName: true, lastName: true }
        });
        return {
          userId: senderId,
          username: classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown',
          userType: 'class'
        };
      }
      return null;
    })
  );

  return userDetails.filter(user => user !== null);
};

export const markMessagesAsRead = async (senderId: string, recipientId: string) => {
  await prisma.chatMessage.updateMany({
    where: {
      senderId: senderId,
      recipientId: recipientId,
      isRead: false
    },
    data: {
      isRead: true
    }
  });
};
