'use client';
import { useState, useEffect, memo } from 'react';
import { Exam } from '@/lib/types';
import { RiTimerFlashFill } from 'react-icons/ri';
import { differenceInSeconds, addMinutes } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';

interface CountdownTimerProps {
  exam: Exam;
}

const CountdownTimer = ({ exam }: CountdownTimerProps) => {
  const [timerState, setTimerState] = useState<'registration' | 'application' | 'exam' | 'expired' | 'late'>('registration');

  const [countdown, setCountdown] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  const [examTimer, setExamTimer] = useState<{
    minutes: number;
    seconds: number;
    isLate: boolean;
  }>({
    minutes: 10,
    seconds: 0,
    isLate: false,
  });

  useEffect(() => {
    const calculateStatus = () => {
      const startRegistrationDate = exam.start_registration_date
        ? new Date(exam.start_registration_date)
        : null;
      const startDate = new Date(exam.start_date);

      if (isNaN(startDate.getTime())) {
        console.error(`Invalid start_date for exam ${exam.id}: ${exam.start_date}`);
        setTimerState('expired');
        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        setExamTimer({ minutes: 0, seconds: 0, isLate: true });
        return;
      }

      const now = toZonedTime(new Date(), 'Asia/Kolkata');
      const istStartDate = toZonedTime(startDate, 'Asia/Kolkata');
      const istStartTime = istStartDate.getTime();
      const startWindowEnd = addMinutes(istStartDate, exam.duration).getTime(); 
      const nowTime = now.getTime();

      // 1: Registration Phase
      if (
        startRegistrationDate &&
        !isNaN(startRegistrationDate.getTime()) &&
        nowTime < startRegistrationDate.getTime()
      ) {
        const diffSeconds = differenceInSeconds(startRegistrationDate, now);
        const days = Math.floor(diffSeconds / (60 * 60 * 24));
        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));
        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);
        const seconds = diffSeconds % 60;
        setTimerState('registration');
        setCountdown({ days, hours, minutes, seconds });
        setExamTimer({ minutes: 10, seconds: 0, isLate: false });
      }
      // 2: Application Phase
      else if (nowTime < istStartTime) {
        const diffSeconds = differenceInSeconds(istStartDate, now);
        const days = Math.floor(diffSeconds / (60 * 60 * 24));
        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));
        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);
        const seconds = diffSeconds % 60;
        setTimerState('application');
        setCountdown({ days, hours, minutes, seconds });
        setExamTimer({ minutes: 10, seconds: 0, isLate: false });
      }
      //  3: Exam Start 
      else if (nowTime >= istStartTime && nowTime <= startWindowEnd) {
        const diffSeconds = differenceInSeconds(new Date(startWindowEnd), now);
        const minutes = Math.floor(diffSeconds / 60);
        const seconds = diffSeconds % 60;
        setTimerState('exam');
        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        setExamTimer({ minutes, seconds, isLate: false });
      }
      // 4: Exam Late/Finished
      else {
        setTimerState('late');
        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        setExamTimer({ minutes: 0, seconds: 0, isLate: true });
      }
    };

    calculateStatus();
    const interval = setInterval(calculateStatus, 1000);

    return () => clearInterval(interval);
  }, [exam.start_date, exam.start_registration_date, exam.id]);

  return (
    <div className="flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300">
      <RiTimerFlashFill className="text-2xl text-customOrange animate-pulse" />
      <span className="font-semibold text-customOrange text-sm">
        {timerState === 'registration' ? (
          <span>
            Registration Starts in: {countdown.days}d {countdown.hours}h {countdown.minutes}m{' '}
            {countdown.seconds}s
          </span>
        ) : timerState === 'application' ? (
          <span>
            Exam Starts in: {countdown.days}d {countdown.hours}h {countdown.minutes}m{' '}
            {countdown.seconds}s
          </span>
        ) : timerState === 'exam' ? (
          <span>
            You May Starts In: {examTimer.minutes}m {examTimer.seconds}s
          </span>
        ) : timerState === 'late' ? (
          <span>You Are Late</span>
        ) : (
          <span>Expired</span>
        )}
      </span>
    </div>
  );
};

export default memo(CountdownTimer);