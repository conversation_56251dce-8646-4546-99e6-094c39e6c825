import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import * as wishlistService from '../services/studentWishlistServices';

export const addToWishlist = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { classId } = req.body;
    if (!classId) {
      sendError(res, 'Class ID is required', 400);
      return;
    }

    const classExists = await wishlistService.findClassById(classId);
    if (!classExists) {
      sendError(res, 'Class not found', 404);
      return;
    }

    const existingItem = await wishlistService.findWishlistItem(studentId, classId);
    if (existingItem) {
      sendSuccess(res, { id: existingItem.id }, 'Class already in wishlist');
      return;
    }

    const wishlistItem = await wishlistService.createWishlistItem(studentId, classId);
    sendSuccess(res, { id: wishlistItem.id }, 'Added to wishlist successfully');
  } catch (error: any) {
    console.error('Error adding to wishlist:', error);
    sendError(res, 'Failed to add to wishlist', 500);
  }
};


export const removeFromWishlist = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { id } = req.params;
    if (!id) {
      sendError(res, 'Wishlist item ID is required', 400);
      return;
    }

    const wishlistItem = await wishlistService.findWishlistItemById(id, studentId);

    if (!wishlistItem) {
      sendError(res, 'Wishlist item not found', 404);
      return;
    }
    await wishlistService.deleteWishlistItem(id);

    sendSuccess(res, null, 'Removed from wishlist successfully');
  } catch (error: any) {
    console.error('Error removing from wishlist:', error);
    sendError(res, 'Failed to remove from wishlist', 500);
  }
};

export const checkWishlistStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const { classId } = req.params;
    if (!classId) {
      sendError(res, 'Class ID is required', 400);
      return;
    }

    const wishlistItem = await wishlistService.findWishlistItem(studentId, classId);

    sendSuccess(res, {
      inWishlist: !!wishlistItem,
      wishlistItem
    }, 'Wishlist status retrieved');
  } catch (error: any) {
    console.error('Error checking wishlist status:', error);
    sendError(res, 'Failed to check wishlist status', 500);
  }
};

export const getWishlist = async (req: Request, res: Response): Promise<void> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      sendError(res, 'Authentication required', 401);
      return;
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const [wishlistItems, total] = await Promise.all([
      wishlistService.getWishlistItems(studentId, skip, limit),
      wishlistService.countWishlistItems(studentId)
    ]);

    const totalPages = Math.ceil(total / limit);

    sendSuccess(res, {
      items: wishlistItems,
      total,
      page,
      limit,
      totalPages
    }, 'Wishlist retrieved successfully');
  } catch (error: any) {
    console.error('Error getting wishlist:', error);
    sendError(res, 'Failed to get wishlist', 500);
  }
};
