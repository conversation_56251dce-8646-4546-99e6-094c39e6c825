import axiosInstance from '../lib/axios';

export const getTerminatedStudents = async (examId:number,page: number = 1, limit: number = 10) => {
  try {
    const response = await axiosInstance.get(`/quizTermination/${examId}?page=${page}&limit=${limit}`, {
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get Terminated Students: ${error.response?.data?.message || error.message}`,
    };
  }
};