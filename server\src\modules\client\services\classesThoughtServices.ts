import { PrismaClient, ClassesThought, Status } from "@prisma/client";

const prisma = new PrismaClient();

type ThoughtWithClassAndLogo = {
  id: string;
  thoughts: string;
  createdAt: Date;
  status: Status;
  class: {
    id: string;
    firstName: string;
    lastName: string;
    className: string | null;
    contactNo: string | null;
    ClassAbout: {
      classesLogo: string | null;
    } | null;
  };
};

interface PaginationResponse {
  thoughts: ThoughtWithClassAndLogo[];
  total: number;
  pages: number;
  currentPage: number;
}

export const createThought = async (data: {
  classId: string;
  thoughts: string;
}): Promise<ClassesThought> => {
  if (data.thoughts.length < 50 || data.thoughts.length > 500) {
    throw new Error("Thoughts must be between 50 and 500 characters");
  }
  return prisma.classesThought.create({
    data: {
      classId: data.classId,
      thoughts: data.thoughts,
    },
  });
};

export const getThought = async (
  status?: Status,
  classId?: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginationResponse> => {
  const skip = (page - 1) * limit;

  const [thoughts, total] = await Promise.all([
    prisma.classesThought.findMany({
      where: {
        status: status ? status : undefined,
        classId: classId ? classId : undefined,
      },
      select: {
        id: true,
        thoughts: true,
        createdAt: true,
        status: true,
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            contactNo: true,
            ClassAbout: {
              select: {
                classesLogo: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: skip,
    }),
    prisma.classesThought.count({
      where: {
        status: status ? status : undefined,
        classId: classId ? classId : undefined,
      },
    }),
  ]);

  return {
    thoughts,
    total,
    pages: Math.ceil(total / limit),
    currentPage: page,
  };
};

export const getThoughtById = async (
  id: string
): Promise<ClassesThought | null> => {
  return prisma.classesThought.findUnique({
    where: { id },
    include: { class: true },
  });
};

export const updateThought = async (
  id: string,
  data: { thoughts?: string }
): Promise<ClassesThought> => {
  if (
    data.thoughts &&
    (data.thoughts.length < 50 || data.thoughts.length > 500)
  ) {
    throw new Error("Thoughts must be between 50 and 500 characters");
  }
  return prisma.classesThought.update({
    where: { id },
    data,
  });
};

export const deleteThought = async (id: string): Promise<ClassesThought> => {
  return prisma.classesThought.delete({
    where: { id },
  });
};

export const updateThoughtStatus = async (
  id: string,
  status: Status
): Promise<ClassesThought> => {
  return prisma.classesThought.update({
    where: { id },
    data: { status },
    include: { class: true },
  });
};
