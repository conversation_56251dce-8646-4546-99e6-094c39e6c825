'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Users, IndianRupee, Check } from 'lucide-react';
import { toast } from 'sonner';
import { axiosInstance } from '@/lib/axios';
import { format } from 'date-fns';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/app-components/dataTable';

interface ReferredUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: 'STUDENT' | 'CLASS';
  createdAt: string;
  earnings?: Array<{
    id: string;
    earningType: 'REGISTRATION' | 'UWHIZ_APPLICATION';
    amount: number;
    paymentStatus: 'PAID' | 'UNPAID';
    examId?: string;
  }>;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface ReferralLinkInfo {
  id: string;
  code: string;
  userType: string;
}

export default function ReferredUsersPage() {
  const params = useParams();
  const router = useRouter();
  const linkId = params.linkId as string;

  const [referredUsers, setReferredUsers] = useState<ReferredUser[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);
  const [referralLink, setReferralLink] = useState<ReferralLinkInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedEarnings, setSelectedEarnings] = useState<Set<string>>(new Set());

  // Helper functions for managing selected earnings
  const toggleEarningSelection = (earningId: string) => {
    setSelectedEarnings(prev => {
      const newSet = new Set(prev);
      if (newSet.has(earningId)) {
        newSet.delete(earningId);
      } else {
        newSet.add(earningId);
      }
      return newSet;
    });
  };

  const toggleSelectAll = () => {
    const allUnpaidEarningIds = referredUsers
      .flatMap(user => user.earnings || [])
      .filter(earning => earning.paymentStatus === 'UNPAID')
      .map(earning => earning.id);

    if (selectedEarnings.size === allUnpaidEarningIds.length) {
      // If all are selected, deselect all
      setSelectedEarnings(new Set());
    } else {
      // Select all unpaid earnings
      setSelectedEarnings(new Set(allUnpaidEarningIds));
    }
  };

  const clearSelection = () => {
    setSelectedEarnings(new Set());
  };

  // Update payment status function
  const updatePaymentStatus = async (earningIds: string[], paymentStatus: 'PAID' | 'UNPAID') => {
    try {
      const response = await axiosInstance.patch('/referral/admin/earnings/payment-status', {
        earningIds,
        paymentStatus,
      });

      if (response.data.success) {
        toast.success(`Payment status updated to ${paymentStatus.toLowerCase()}`);
        // Clear selection after successful update
        clearSelection();
        // Refresh the data
        fetchReferredUsers(pagination?.currentPage || 1);
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      toast.error('Failed to update payment status');
    }
  };

  // Column definitions for DataTable
  const columns: ColumnDef<ReferredUser>[] = [
    {
      accessorKey: 'paymentActions',
      header: () => (
        <div className="flex items-center gap-2">
          <Checkbox
            checked={
              referredUsers.flatMap(user => user.earnings || [])
                .filter(earning => earning.paymentStatus === 'UNPAID').length > 0 &&
              selectedEarnings.size === referredUsers.flatMap(user => user.earnings || [])
                .filter(earning => earning.paymentStatus === 'UNPAID').length
            }
            onCheckedChange={toggleSelectAll}
          />
          <span>Select</span>
        </div>
      ),
      cell: ({ row }) => {
        const { earnings } = row.original;
        return earnings && earnings.length > 0 ? (
          <div className="flex flex-col gap-2">
            {earnings.map((earning) => (
              <div key={earning.id} className="flex items-center gap-2">
                {earning.paymentStatus === 'UNPAID' ? (
                  <Checkbox
                    checked={selectedEarnings.has(earning.id)}
                    onCheckedChange={() => toggleEarningSelection(earning.id)}
                  />
                ) : (
                  <div className="w-4 h-4" /> // Placeholder for paid earnings
                )}
                <span className="text-sm">
                  ₹{earning.amount} ({earning.earningType === 'REGISTRATION' ? 'Registration' : 'U-whiz'})
                </span>
              </div>
            ))}
          </div>
        ) : null;
      },
    },
    {
      accessorKey: 'firstName',
      header: () => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-orange-600" />
          First Name
        </div>
      ),
      cell: ({ row }) => (
        <span className="font-semibold text-gray-900">{row.original.firstName}</span>
      ),
    },
    {
      accessorKey: 'lastName',
      header: 'Last Name',
      cell: ({ row }) => (
        <span className="font-semibold text-gray-900">{row.original.lastName}</span>
      ),
    },
    {
      accessorKey: 'email',
      header: 'Email Address',
      cell: ({ row }) => (
        <span className="text-gray-700">{row.original.email}</span>
      ),
    },
    {
      accessorKey: 'userType',
      header: 'User Type',
      cell: ({ row }) => (
        <Badge
          variant={row.original.userType === 'STUDENT' ? 'default' : 'secondary'}
          className={`font-medium ${
            row.original.userType === 'STUDENT'
              ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
              : 'bg-green-100 text-green-800 hover:bg-green-200'
          }`}
        >
          {row.original.userType}
        </Badge>
      ),
    },
    {
      accessorKey: 'earnings',
      header: () => (
        <div className="flex items-center gap-1">
          <IndianRupee className="h-4 w-4" />
          Earnings
        </div>
      ),
      cell: ({ row }) => {
        const { earnings } = row.original;
        return earnings && earnings.length > 0 ? (
          <div className="space-y-1">
            {earnings.map((earning) => (
              <div key={earning.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <div className="text-sm">
                  <span className="font-medium">₹{earning.amount}</span>
                  <span className="text-gray-500 ml-1">
                    ({earning.earningType === 'REGISTRATION' ? 'Registration' : 'U-whiz'})
                  </span>
                </div>
                <Badge
                  variant={earning.paymentStatus === 'PAID' ? 'default' : 'secondary'}
                  className={earning.paymentStatus === 'PAID' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}
                >
                  {earning.paymentStatus}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <span className="text-gray-400">No earnings</span>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Joined Date',
      cell: ({ row }) => (
        <span className="text-gray-700 font-medium">
          {format(new Date(row.original.createdAt), 'MMM dd, yyyy')}
        </span>
      ),
    },
  ];

  const fetchReferredUsers = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
      });

      const response = await axiosInstance.get(`/referral/admin/referred-users/${linkId}?${params.toString()}`);
      if (response.data.success) {
        setReferredUsers(response.data.data.referredUsers);
        setPagination(response.data.data.pagination);
        setReferralLink(response.data.data.referralLink);
        // Clear selection when data is refreshed
        clearSelection();
      }
    } catch (error) {
      console.error('Error fetching referred users:', error);
      toast.error('Failed to load referred users');
    } finally {
      setLoading(false);
    }
  }, [linkId]);

  const handlePageChange = (page: number) => {
    fetchReferredUsers(page);
  };



  const markAllUnpaidAsPaid = async () => {
    const allUnpaidEarningIds = referredUsers
      .flatMap(user => user.earnings || [])
      .filter(earning => earning.paymentStatus === 'UNPAID')
      .map(earning => earning.id);

    if (allUnpaidEarningIds.length === 0) {
      toast.info('No unpaid earnings found');
      return;
    }

    await updatePaymentStatus(allUnpaidEarningIds, 'PAID');
  };

  useEffect(() => {
    if (linkId) {
      fetchReferredUsers(1);
    }
  }, [linkId, fetchReferredUsers]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-4 sm:py-8 max-w-7xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6 sm:mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="flex items-center gap-2"
          size="sm"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="hidden sm:inline">Back to Referral Management</span>
          <span className="sm:hidden">Back</span>
        </Button>
      </div>

      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Referred Users</h1>
        <div className="text-gray-600 text-sm sm:text-base">
          <span className="block sm:inline">Users referred by referral code:</span>{' '}
          <code className="bg-orange-100 text-orange-800 px-2 sm:px-3 py-1 rounded-md font-mono text-xs sm:text-sm mt-1 sm:mt-0 inline-block">
            {referralLink?.code}
          </code>
        </div>
      </div>

  

      {/* Stats Card */}
      {pagination && (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4 lg:gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Referred Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {pagination.totalCount}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {referredUsers.filter(user => user.userType === 'STUDENT').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Classes</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {referredUsers.filter(user => user.userType === 'CLASS').length}
              </div>
            </CardContent>
          </Card>

          {/* Earnings Summary Cards */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <IndianRupee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                ₹{referredUsers.reduce((total, user) =>
                  total + (user.earnings?.reduce((sum, earning) => sum + earning.amount, 0) || 0), 0
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                All referral earnings
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
              <IndianRupee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="text-sm">
                  <span className="text-green-600 font-semibold">
                    Paid: ₹{referredUsers.reduce((total, user) =>
                      total + (user.earnings?.filter(e => e.paymentStatus === 'PAID').reduce((sum, earning) => sum + earning.amount, 0) || 0), 0
                    )}
                  </span>
                </div>
                <div className="text-sm">
                  <span className="text-orange-600 font-semibold">
                    Unpaid: ₹{referredUsers.reduce((total, user) =>
                      total + (user.earnings?.filter(e => e.paymentStatus === 'UNPAID').reduce((sum, earning) => sum + earning.amount, 0) || 0), 0
                    )}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Referred Users Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Referred Users List</CardTitle>
              <CardDescription>
                Complete list of users who registered using this referral code
              </CardDescription>
            </div>
            {/* Bulk Payment Actions */}
            <div className="flex flex-col sm:flex-row gap-2">
              {selectedEarnings.size > 0 && (
                <>
                  <Button
                    onClick={() => updatePaymentStatus(Array.from(selectedEarnings), 'PAID')}
                    className="bg-green-600 hover:bg-green-700 text-white w-full sm:w-auto"
                    size="sm"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">Mark Selected as Paid ({selectedEarnings.size})</span>
                    <span className="sm:hidden">Pay Selected ({selectedEarnings.size})</span>
                  </Button>
                  <Button
                    onClick={clearSelection}
                    variant="outline"
                    className="border-gray-300 w-full sm:w-auto"
                    size="sm"
                  >
                    Clear Selection
                  </Button>
                </>
              )}
              {referredUsers.some(user => user.earnings?.some(earning => earning.paymentStatus === 'UNPAID')) && (
                <Button
                  onClick={markAllUnpaidAsPaid}
                  variant="outline"
                  className="border-orange-300 text-orange-600 hover:bg-orange-50 w-full sm:w-auto"
                  size="sm"
                >
                  <Check className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Mark All Unpaid as Paid</span>
                  <span className="sm:hidden">Pay All Unpaid</span>
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {!loading && referredUsers.length === 0 ? (
            <div className="text-center p-16 bg-gray-50 rounded-xl border-2 border-dashed border-gray-200">
              <Users className="h-16 w-16 mx-auto mb-6 text-gray-400" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No Referred Users Yet</h3>
              <p className="text-gray-500">Users who register using this referral code will appear here.</p>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden md:block">
                <DataTable
                  columns={columns}
                  data={referredUsers}
                  totalItems={pagination?.totalCount || 0}
                  totalPages={pagination?.totalPages || 1}
                  currentPage={pagination?.currentPage || 1}
                  pageSize={pagination?.limit || 10}
                  onPageChange={handlePageChange}
                  isLoading={loading}
                />
              </div>

              {/* Mobile Card View */}
              <div className="block md:hidden">
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Mobile Select All Button */}
                    {referredUsers.some(user => user.earnings?.some(earning => earning.paymentStatus === 'UNPAID')) && (
                      <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium text-gray-700">
                          {selectedEarnings.size > 0 ? `${selectedEarnings.size} selected` : 'Select earnings'}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleSelectAll}
                          className="text-xs"
                        >
                          {selectedEarnings.size === referredUsers.flatMap(user => user.earnings || [])
                            .filter(earning => earning.paymentStatus === 'UNPAID').length
                            ? 'Deselect All'
                            : 'Select All Unpaid'
                          }
                        </Button>
                      </div>
                    )}
                    {referredUsers.map((user) => (
                      <div key={user.id} className="bg-white border rounded-lg p-4 shadow-sm">
                        {/* User Info */}
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 text-lg">
                              {user.firstName} {user.lastName}
                            </h3>
                            <p className="text-gray-600 text-sm">{user.email}</p>
                            <div className="mt-1">
                              <Badge
                                variant={user.userType === 'STUDENT' ? 'default' : 'secondary'}
                                className={`font-medium ${
                                  user.userType === 'STUDENT'
                                    ? 'bg-blue-100 text-blue-800'
                                    : 'bg-green-100 text-green-800'
                                }`}
                              >
                                {user.userType}
                              </Badge>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm text-gray-500">Joined</p>
                            <p className="text-sm font-medium">
                              {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                            </p>
                          </div>
                        </div>

                        {/* Earnings Section */}
                        {user.earnings && user.earnings.length > 0 ? (
                          <div className="border-t pt-3">
                            <div className="flex items-center gap-1 mb-2">
                              <IndianRupee className="h-4 w-4 text-gray-600" />
                              <span className="font-medium text-gray-700">Earnings</span>
                            </div>
                            <div className="space-y-2">
                              {user.earnings.map((earning) => (
                                <div key={earning.id} className="bg-gray-50 rounded-lg p-3">
                                  <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center gap-2">
                                      {earning.paymentStatus === 'UNPAID' && (
                                        <Checkbox
                                          checked={selectedEarnings.has(earning.id)}
                                          onCheckedChange={() => toggleEarningSelection(earning.id)}
                                        />
                                      )}
                                      <span className="font-medium text-gray-900">₹{earning.amount}</span>
                                    </div>
                                    <Badge
                                      variant={earning.paymentStatus === 'PAID' ? 'default' : 'secondary'}
                                      className={earning.paymentStatus === 'PAID' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}
                                    >
                                      {earning.paymentStatus}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    {earning.earningType === 'REGISTRATION' ? 'Registration' : 'U-whiz Application'}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div className="border-t pt-3">
                            <p className="text-gray-400 text-sm">No earnings</p>
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Mobile Pagination */}
                    {pagination && pagination.totalPages > 1 && (
                      <div className="flex flex-col items-center gap-4 mt-6 p-4 bg-gray-50 rounded-lg">
                        <div className="text-sm text-gray-700 text-center">
                          Showing <span className="font-medium">{((pagination.currentPage - 1) * pagination.limit) + 1}</span> to{' '}
                          <span className="font-medium">
                            {Math.min(pagination.currentPage * pagination.limit, pagination.totalCount)}
                          </span> of{' '}
                          <span className="font-medium">{pagination.totalCount}</span> results
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.currentPage - 1)}
                            disabled={!pagination.hasPreviousPage}
                            className="flex items-center gap-1"
                          >
                            Previous
                          </Button>
                          <span className="text-sm px-3 py-1 bg-white rounded border">
                            {pagination.currentPage} / {pagination.totalPages}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.currentPage + 1)}
                            disabled={!pagination.hasNextPage}
                            className="flex items-center gap-1"
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Mobile Floating Action Button for Bulk Actions */}
      {selectedEarnings.size > 0 && (
        <div className="fixed bottom-4 left-4 right-4 md:hidden z-50">
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3">
            <div className="flex gap-2">
              <Button
                onClick={() => updatePaymentStatus(Array.from(selectedEarnings), 'PAID')}
                className="bg-green-600 hover:bg-green-700 text-white flex-1"
                size="sm"
              >
                <Check className="h-4 w-4 mr-2" />
                Pay Selected ({selectedEarnings.size})
              </Button>
              <Button
                onClick={clearSelection}
                variant="outline"
                className="border-gray-300"
                size="sm"
              >
                Clear
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
