'use client';
import Header from '@/app-components/Header';
import { BsFillCalendar2DateFill } from 'react-icons/bs';
import { IoCheckmarkSharp, IoTime } from 'react-icons/io5';
import { Button } from '@/components/ui/button';
import { PiCertificateFill } from 'react-icons/pi';
import { useEffect, useState, useRef } from 'react';
import { toast } from 'sonner';
import { Exam, Ranking } from '@/lib/types';
import { getExamsById } from '@/services/examApi';
import { getExamRankings } from '@/services/uwhizRankingApi';
import { getExamParticipants, getUserRankings } from '@/services/uwhizCertificateApi';
import { useParams } from 'next/navigation';
import { jsPDF } from 'jspdf';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { GiDuration, GiPodiumWinner } from 'react-icons/gi';
import { FaCircleQuestion } from 'react-icons/fa6';
import { motion } from 'framer-motion'; 
import Footer from '@/app-components/Footer';
import Image from 'next/image';

const UwizDetail = () => {
  const { examId } = useParams();
  const [exam, setExam] = useState<Exam | null>(null);
  const [loading, setLoading] = useState(false);
  const [rankings, setRankings] = useState<Ranking[]>([]);
  const [userRank, setUserRank] = useState<number>(0);
  const [visibleCount, setVisibleCount] = useState<number>(2);
  const [participantName, setParticipantName] = useState<string>('');
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const user = useSelector((state: RootState) => state.user);
  const userId = user.user?.id || '';

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const examResponse = await getExamsById(Number(examId));
        if (examResponse.success === false) throw new Error(examResponse.error);
        setExam(examResponse as Exam);

        const userRankingsResponse = await getUserRankings(Number(examId), userId);
        setUserRank(userRankingsResponse.rank);

        const rankingsResponse = await getExamRankings(Number(examId));
        setRankings(rankingsResponse as Ranking[]);

        const participantResponse = await getExamParticipants(Number(examId), userId);
        if (participantResponse.success && participantResponse.data?.length) {
          const participant = participantResponse.data[0];
          setParticipantName(`${participant.firstName} ${participant.lastName}`);
        }
      } catch (error: any) {
        console.error('Error fetching data:', error);
        toast.error(error.message || 'Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    if (examId) fetchData();
  }, [examId, userId]);

  const handleLoadMore = () => setVisibleCount(rankings.length);

  const drawCertificate = (imageSrc: string, name: string, rank?: number) => {
    const canvas = canvasRef.current;
    if (!canvas) {
      toast.error('Canvas not available');
      return;
    }

    const ctx = canvas.getContext('2d', { alpha: false });
    if (!ctx) {
      toast.error('Failed to get canvas context');
      return;
    }

    const image = document.createElement('img');
    image.src = imageSrc;

    image.onload = async () => {
      try {
        await document.fonts.ready;
        const scaleFactor = 2;
        const mmToPx = 3.78 * scaleFactor;
        const pdfWidthMM = 297;
        const pdfHeightMM = 210;

        const canvasWidth = pdfWidthMM * mmToPx;
        const canvasHeight = pdfHeightMM * mmToPx;

        canvas.width = canvasWidth;
        canvas.height = canvasHeight;
        ctx.scale(scaleFactor, scaleFactor);

        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(image, 0, 0, canvas.width / scaleFactor, canvas.height / scaleFactor);

        ctx.textRendering = 'geometricPrecision';
        ctx.fontKerning = 'normal';
        ctx.textBaseline = 'middle';

        const nameX = 560;
        const nameY = 458;

        ctx.font = '48px Balsamiq Sans';
        ctx.textAlign = 'center';
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.4)';
        ctx.lineWidth = 3;
        ctx.strokeText(name, nameX, nameY);

        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(name, nameX, nameY);

        if (rank !== undefined && rank !== null) {
          const rankX = 359;
          const rankY = 503;

          ctx.font = '22px Balsamiq Sans';
          ctx.strokeText(rank.toString(), rankX, rankY);
          ctx.fillText(`#${rank}`, rankX, rankY);
        }

        const pdf = new jsPDF('landscape', 'mm', 'a4');
        const imgData = canvas.toDataURL('image/jpeg', 1.0);

        pdf.addImage(imgData, 'JPEG', 0, 0, pdf.internal.pageSize.width, pdf.internal.pageSize.height, undefined, 'NONE');
        pdf.save(`UWHIZ_Certificate_${name}.pdf`);
      } catch (error) {
        console.error('Error generating certificate:', error);
        toast.error('Failed to generate certificate');
      }
    };

    image.onerror = () => {
      console.error(`Failed to load image: ${imageSrc}`);
      toast.error('Failed to load certificate image');
    };
  };

  const handleRankCertificate = () => drawCertificate('/RankCertificate.png', participantName, userRank);
  const handleCertificate = () => drawCertificate('/participantCertificate.png', participantName);



  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-lg font-semibold text-gray-600"
        >
          Loading exam details...
        </motion.div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-lg font-semibold text-red-600"
        >
          Error: Failed to load exam details
        </motion.div>
      </div>
    );
  }

  const loggedInUserRanking = rankings.find((ranking) => ranking.userId === userId);
  const otherRankings = rankings.filter((ranking) => ranking.userId !== userId);

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 py-12 px-4">
        <div className="max-w-6xl mx-auto">
          {/* Exam Info Card */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-2xl shadow-xl overflow-hidden mb-12"
          >
            <div className="p-8 text-center">
              <h1 className="text-4xl font-bold text-customOrange mb-6">U whiz - Super Tutors</h1>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 text-gray-700">
                {[
                  { icon: IoCheckmarkSharp, label: 'Marks', value: 100 },
                  { icon: IoTime, label: 'Start Time', value: "10:00 AM" },
                  {
                    icon: BsFillCalendar2DateFill,
                    label: 'Date',
                    value: "27-4-2025"
                  },
                  { icon: GiDuration, label: 'Duration', value: `90 min` },
                  { icon: FaCircleQuestion, label: 'Questions', value: 100 },
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-3"
                  >
                    <item.icon className="text-customOrange text-2xl" />
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-500">{item.label}</p>
                      <p className="text-lg font-semibold">{item.value}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
            <div className="p-6 bg-gray-50 flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleRankCertificate}
                disabled={participantName === ''}
                className="bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-full transition-all transform hover:scale-105 disabled:opacity-50"
              >
                Download Result
              </Button>
              <Button
                onClick={handleCertificate}
                disabled={participantName === ''}
                className="bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 px-8 rounded-full transition-all transform hover:scale-105 disabled:opacity-50 flex items-center gap-2"
              >
                <PiCertificateFill className="text-xl" /> Download Certificate
              </Button>
            </div>
          </motion.div>

          {/* Ranking List */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-2xl shadow-xl p-8"
          >
            <h2 className="text-3xl font-bold text-customOrange text-center mb-8">Exam Rankings</h2>
            <div className="space-y-4">
              {loggedInUserRanking && (
                <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center justify-between p-4 bg-orange-100 rounded-xl shadow-lg"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 flex items-center justify-center bg-orange-500 text-white rounded-full font-bold text-base shadow">
                    #{loggedInUserRanking.rank}
                  </div>
              
                  <div className="flex items-center gap-4">
                    {/* Image */}
                    {loggedInUserRanking.classesLogo ? (
                      <Image
                        src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${loggedInUserRanking.classesLogo}`}
                        alt={`${loggedInUserRanking.name}'s logo`}
                        className="rounded-sm object-cover"
                        height={80}
                        width={80}
                      />
                    ) : (
                      <div className="w-12 h-12 flex items-center justify-center bg-gray-200 rounded-full text-xs text-gray-500">
                        No Logo
                      </div>
                    )}
              
                    <div>
                      <p className="text-base font-semibold text-orange-800">{loggedInUserRanking.name}</p>
                      <p className="text-sm text-gray-600">({loggedInUserRanking.classesName})</p>
              
                      {loggedInUserRanking.rank === 1 && (
                        <div className="flex items-center gap-1 text-orange-600 text-sm mt-1">
                          <GiPodiumWinner className="text-base" />
                          <span className="font-medium text-xl">₹21,000</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              
                {/* Score */}
                <div className="text-base font-bold text-green-600 bg-green-100 px-3 py-1 rounded-full shadow-sm">
                  {loggedInUserRanking.score}/{loggedInUserRanking.totalQuestions}
                </div>
              </motion.div>
              
              )}

              {otherRankings.slice(0, visibleCount).map((ranking) => (
               <motion.div
               key={ranking.userId}
               initial={{ opacity: 0, y: 10 }}
               animate={{ opacity: 1, y: 0 }}
               transition={{ duration: 0.3 }}
               className={`flex items-center justify-between p-4 rounded-xl shadow-md ${
                 ranking.rank === 1
                   ? 'bg-orange-50 border-l-4 border-orange-500'
                   : 'hover:bg-gray-50'
               }`}
             >
               <div className="flex items-center gap-4">
                 <div
                   className={`w-10 h-10 flex items-center justify-center rounded-full ${
                     ranking.rank === 1
                       ? 'bg-orange-500 text-white'
                       : 'bg-orange-100 text-orange-600'
                   } font-bold text-sm shadow`}
                 >
                   #{ranking.rank}
                 </div>
             
                 <div className="flex items-center gap-4">
                   {/* Image */}
                   {ranking.classesLogo ? (
                     <Image
                       src={`${process.env.NEXT_PUBLIC_API_BASE_URL}${ranking.classesLogo}`}
                       alt={`${ranking.name}'s logo`}
                       className="rounded-sm object-cover"
                       width={80}
                       height={80}
                     />
                   ) : (
                     <div className="w-12 h-12 flex items-center justify-center bg-gray-200 rounded-full text-xs text-gray-500">
                       No Logo
                     </div>
                   )}
             
                   <div>
                     <p
                       className={`font-semibold ${
                         ranking.rank === 1 ? 'text-lg text-orange-800' : 'text-base text-gray-700'
                       }`}
                     >
                       {ranking.name}
                     </p>
                     <p className="text-sm text-gray-500">({ranking.classesName})</p>
             
                     {ranking.rank === 1 && (
                       <div className="flex items-center gap-2 text-orange-500 mt-1">
                         <GiPodiumWinner className="text-base" />
                         <span className="font-medium text-sm">₹21,000</span>
                       </div>
                     )}
                   </div>
                 </div>
               </div>
             
               {/* Score */}
               <div
                 className={`font-semibold text-green-600 bg-green-100 px-3 py-1 rounded-full ${
                   ranking.rank === 1 ? 'text-base' : 'text-sm'
                 }`}
               >
                 {ranking.score}/{ranking.totalQuestions}
               </div>
             </motion.div>
              ))}
            </div>

            {visibleCount < otherRankings.length && (
              <div className="flex justify-center mt-6">
                <Button
                  onClick={handleLoadMore}
                  className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-6 rounded-full transition-all transform hover:scale-105"
                >
                  Load More
                </Button>
              </div>
            )}
          </motion.div>
        </div>
      </div>
      <Footer />
      <canvas ref={canvasRef} style={{ display: 'none' }} />
    </>
  );
};

export default UwizDetail;
