import prisma from '@/config/prismaClient';
import { ClassApprovalStatus, TransactionType } from '@prisma/client';
import {
  ExamApplication,
  PaginationResponse,
} from '@/modules/admin/interfaces/ExamApplicationType';

//Apply for Exam
export const applyForExam = async (examId: number, classId: string) => {
  const exam = await prisma.exam.findUnique({
    where: { id: examId },
    select: {
      id: true,
      exam_name: true,
      coins_required: true,
      total_student_intake: true,
    },
  });

  if (!exam) {
    throw new Error('Exam not found');
  }

  if (exam.coins_required != null) {
    const classStatus = await prisma.classesStatus.findUnique({
      where: { classId },
    });

    if (!classStatus || classStatus.status !== ClassApprovalStatus.APPROVED) {
      throw new Error('Class is not approved to apply for exams');
    }
  }

  const existingApplication = await prisma.examApplication.findUnique({
    where: { examId_classId: { examId, classId } },
  });

  if (existingApplication) {
    throw new Error('This class has already joined the exam');
  }

  const joinedClassesCount = await prisma.examApplication.count({
    where: { examId },
  });

  if (joinedClassesCount >= exam.total_student_intake) {
    throw new Error('Maximum number of classes already joined this exam');
  }

  if (exam.coins_required != null) {
    if (exam.coins_required < 0) {
      throw new Error('Exam coins required is invalid');
    }

    const uestCoins = await prisma.uestCoins.findUnique({
      where: { modelId_modelType: { modelId: classId, modelType: 'CLASS' } },
    });

    if (!uestCoins || uestCoins.coins < exam.coins_required) {
      throw new Error('Insufficient coins to apply for the exam');
    }
  }

  const application = await prisma.examApplication.create({
    data: {
      classId,
      examId,
    },
  });

  if (exam.coins_required != null && exam.coins_required > 0) {
    await prisma.$transaction(async (tx) => {
      await tx.uestCoins.update({
        where: { modelId_modelType: { modelId: classId, modelType: 'CLASS' } },
        data: { coins: { decrement: exam.coins_required ?? 0 } },
      });

      await tx.uestCoinTransaction.create({
        data: {
          modelId: classId,
          modelType: 'CLASS',
          amount: exam.coins_required ?? 0,
          type: TransactionType.DEBIT,
          reason: `Applied for exam ${exam.exam_name}`,
          createdAt: new Date(),
        },
      });
    });
  }

  return {
    application,
    joinedClassesCount: joinedClassesCount + 1,
  };
};

// Get all exam applications (for admin)
export const getAllExamApplications = async (
  page: number,
  limit: number
): Promise<PaginationResponse> => {
  const safePage = Math.max(1, page);
  const safeLimit = Math.max(1, Math.min(100, limit));
  const skip = (safePage - 1) * safeLimit;

  const [applications, total] = await Promise.all([
    prisma.examApplication.findMany({
      skip,
      take: safeLimit,
      orderBy: { createdAt: 'desc' },
      include: {
        exam: {
          select: {
            exam_name: true,
            total_questions: true,
            marks: true,
            level: true,
            duration: true,
            start_date: true,
            coins_required: true,
          },
        },
        class: {
          select: {
            firstName: true,
            lastName: true,
            className: true,
            email: true,
            contactNo: true,
          },
        },
      },
    }),
    prisma.examApplication.count(),
  ]);

  return {
    applications: applications as unknown as ExamApplication[],
    total,
    currentPage: safePage,
    totalPages: Math.ceil(total / safeLimit),
  };
};
