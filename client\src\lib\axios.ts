import axios from 'axios';
import { toast } from 'sonner';

const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';
const baseURL2 = process.env.NEXT_PUBLIC_UWHIZ_API_URL || 'http://localhost:4006';

console.log('Axios baseURL:', baseURL);

export const axiosInstance = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

axiosInstance.interceptors.request.use(
  (config) => {    
    const serverSelect = config.headers['Server-Select'];
    config.baseURL = serverSelect === 'uwhizServer' ? baseURL2 : baseURL;

    const studentToken = localStorage.getItem('studentToken');
    if (studentToken) {
      config.headers.Authorization = `Bearer ${studentToken}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      toast.error(error.response.data.message || 'Unauthorized');
      localStorage.removeItem('user');
      localStorage.removeItem('studentToken');
      localStorage.removeItem('student_data');
      if (typeof window !== 'undefined') {
          window.location.replace('/?authError=1');
      }
    }
    return Promise.reject(error);
  }
);
