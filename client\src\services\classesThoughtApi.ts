import { axiosInstance } from '@/lib/axios';

export interface Class {
  id: string;
  firstName: string;
  lastName: string;
  className: string;
  contactNo: string | null;
  ClassAbout: {
    classesLogo: string | null;
  } | null;
}

export interface Thought {
  id: string;
  thoughts: string;
  createdAt: string;
  updatedAt?: string;
  status: string;
  class: Class;
}

export interface ThoughtPaginationResponse {
  thoughts: Thought[];
  total: number;
  pages: number;
  currentPage: number;
}

export interface ThoughtPaginationResponse {
  thoughts: Thought[];
  total: number;
  pages: number;
  currentPage: number;
}
// Get all thoughts
export const getThought = async (
  status?: 'PENDING' | 'APPROVED' | 'REJECTED',
  classId?: string,
  page: number = 1,
  limit: number = 10
): Promise<ThoughtPaginationResponse> => {
  try {
    const response = await axiosInstance.get('/classes-thought', {
      params: {
        status: status ? status : undefined,
        classId: classId ? classId : undefined,
        page,
        limit,
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch thoughts: ${error.message}`);
  }
};

export const createThought = async (data: { classId: string; thoughts: string }): Promise<Thought> => {
  try {
    const response = await axiosInstance.post('/classes-thought', data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to create thought: ${error.message}`);
  }
};

export const updateThought = async (id: string, data: { thoughts: string }): Promise<Thought> => {
  try {
    const response = await axiosInstance.put(`/classes-thought/${id}`, data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to update thought: ${error.message}`);
  }
};

export const deleteThought = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/classes-thought/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to delete thought: ${error.message}`);
  }
};