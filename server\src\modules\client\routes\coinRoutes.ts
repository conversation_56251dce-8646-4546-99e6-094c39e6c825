import { Router } from "express";
import { createOrder, getCoins, verify, verifyMobile } from "../controllers/coinsController";
import { getTransactions } from "../controllers/transactionsController";
import { authClientMiddleware } from "@/middlewares/clientAuth";
import { studentAuthMiddleware } from "@/middlewares/studentAuth";

const coinRouter = Router();

coinRouter.get("/get-total-coins", authClientMiddleware, getCoins);
coinRouter.get("/get-total-coins/student", studentAuthMiddleware, getCoins);
coinRouter.get("/transaction-history", authClientMiddleware, getTransactions);
coinRouter.get(
  "/transaction-history/student",
  studentAuthMiddleware,
  getTransactions
);
coinRouter.post("/create-order", studentAuthMiddleware, createOrder);
coinRouter.post("/create-order/class", authClientMiddleware, createOrder);
coinRouter.post("/verify", studentAuthMiddleware, verify);
coinRouter.post("/verify/class", authClientMiddleware, verify);
coinRouter.post("/verify-mobile/student", studentAuthMiddleware, verifyMobile);

export default coinRouter;
