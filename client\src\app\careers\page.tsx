import Link from 'next/link';
import { MapPin, ChevronRight } from 'lucide-react';
import { fetchJobs } from '@/services/careerService';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';

export default async function CareersPage() {
  const jobs = await fetchJobs();

  return (
    <>
      <Header />
      <div className="min-h-screen bg-white text-black dark:bg-black dark:text-white">
        {/* Hero Section */}
        <section className="bg-black text-white py-24 text-center px-6 dark:bg-gray-950">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Join Our Mission</h1>
          <p className="text-lg md:text-xl max-w-2xl mx-auto mb-8">
            We are looking for passionate individuals to shape the future of technology.
            Explore our open roles and start your journey with us.
          </p>
          <Link
            href="#jobs"
            className="inline-flex items-center px-6 py-3 bg-white text-black font-semibold rounded-full hover:bg-gray-200 transition dark:bg-white dark:text-black"
          >
            View Openings <ChevronRight className="ml-2 h-5 w-5" />
          </Link>
        </section>

        {/* Job Cards */}
        <section id="jobs" className="py-20 bg-gray-50 dark:bg-black">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
              Open Positions
            </h2>

            <div className="grid gap-10 sm:grid-cols-2 lg:grid-cols-3">
              {jobs.map((job: { id: number; job_title: string; short_description: string }) => (
                <div
                  key={job.id}
                  className="group bg-white border border-gray-200 rounded-xl shadow-md hover:shadow-xl transition-all p-6 flex flex-col justify-between
                  dark:bg-gray-800 dark:border-gray-700"
                >
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 group-hover:text-[#FD904B] transition-colors dark:text-white">
                      {job.job_title}
                    </h3>
                    <p className="text-sm text-gray-600 mt-2 dark:text-gray-300">
                      {job.short_description.length > 150
                        ? `${job.short_description.slice(0, 150)}...`
                        : job.short_description}
                    </p>

                    <div className="flex items-center text-sm text-gray-500 mt-4 dark:text-gray-400">
                      <MapPin className="h-4 w-4 mr-1 text-[#FD904B]" />
                      Morbi
                    </div>

                    <span className="inline-block mt-3 bg-[#FD904B] text-white text-xs font-medium px-3 py-1 rounded-full">
                      Full-time
                    </span>
                  </div>

                  <div className="mt-6 flex flex-col gap-2">
                    <Link
                      href={`/careers/details/${job.id}`}
                      className="inline-flex items-center justify-center rounded-md border border-[#FD904B] text-[#FD904B] px-4 py-2 text-sm font-medium hover:bg-[#FD904B] hover:text-white transition"
                    >
                      View Details <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                    <Link
                      href={`/careers/apply/${job.id}`}
                      className="inline-flex items-center justify-center rounded-md bg-[#FD904B] text-white px-4 py-2 text-sm font-medium hover:bg-[#e67e22] transition"
                    >
                      Apply Now <ChevronRight className="ml-2 h-4 w-4" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
}