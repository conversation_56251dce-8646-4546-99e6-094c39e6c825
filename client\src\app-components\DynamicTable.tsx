"use client";

import React, { useState, useEffect } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ChevronLeftIcon, ChevronRightIcon, ChevronsLeftIcon, ChevronsRightIcon, Loader2 } from "lucide-react";
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    useReactTable,
    getPaginationRowModel,
} from "@tanstack/react-table";

interface CustomTableMeta<TData> {
    onEdit?: (row: TData) => void;
    onDelete?: (id: string) => void;
    deletingId?: string | null;
}

interface DynamicTableProps<TData> {
    columns: ColumnDef<TData>[];
    data: TData[];
    fetchData: (pageIndex: number) => Promise<void>;
    totalItems: number;
    pageSize?: number;
    isLoading?: boolean;
    onEdit?: (row: TData) => void;
    onDelete?: (id: string) => void;
    deletingId?: string | null;
    hidePagination?: boolean; 
}

export function DynamicTable<TData>({
    columns,
    data,
    fetchData,
    totalItems,
    pageSize = 10,
    isLoading = false,
    onEdit,
    onDelete,
    deletingId,
    hidePagination = false,
}: DynamicTableProps<TData>) {
    const [currentPageIndex, setCurrentPageIndex] = useState(0);

    const table = useReactTable<TData>({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        manualPagination: true,
        pageCount: hidePagination ? 1 : Math.ceil(totalItems / pageSize),
        state: {
            pagination: {
                pageIndex: hidePagination ? 0 : currentPageIndex,
                pageSize,
            },
        },
        onPaginationChange: (updater) => {
            if (hidePagination) return;
            const newState = typeof updater === 'function' ? updater(table.getState().pagination) : updater;
            setCurrentPageIndex(newState.pageIndex);
            table.setState((old) => ({
                ...old,
                pagination: newState,
            }));
        },
        meta: {
            onEdit,
            onDelete,
            deletingId,
        } as CustomTableMeta<TData>,
    });

    useEffect(() => {
        if (!hidePagination) {
            fetchData(currentPageIndex);
        }
    }, [fetchData, currentPageIndex, hidePagination]);

    return (
        <>
            <div className="bg-siderbar rounded-lg shadow-sm border overflow-hidden">
                {isLoading ? (
                    <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    </div>
                ) : data.length === 0 ? (
                    <div className="text-center p-8 text-muted-foreground">
                        <p>No data found.</p>
                    </div>
                ) : (
                    <div className="relative w-full overflow-auto">
                        <Table>
                            <TableHeader>
                                {table.getHeaderGroups().map((headerGroup) => (
                                    <TableRow key={headerGroup.id} className="bg-muted/50">
                                        {headerGroup.headers.map((header) => (
                                            <TableHead key={header.id} className="font-semibold">
                                                {header.isPlaceholder
                                                    ? null
                                                    : flexRender(
                                                        header.column.columnDef.header,
                                                        header.getContext()
                                                    )}
                                            </TableHead>
                                        ))}
                                    </TableRow>
                                ))}
                            </TableHeader>
                            <TableBody className="dark:text-white">
                                {table.getRowModel().rows.map((row) => (
                                    <TableRow
                                        key={row.id}
                                        className="hover:bg-muted/50 transition-colors"
                                    >
                                        {row.getVisibleCells().map((cell) => (
                                            <TableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                )}

            </div>
            {!hidePagination && !isLoading && data.length > 0 && (
                <div className="flex items-center justify-between gap-2 mt-4">
                    <div className="text-sm text-muted-foreground">
                        Showing {data.length} of {totalItems} items
                    </div>
                    <div className="flex items-center gap-2">
                        {/* First Page Button */}
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => table.setPageIndex(0)}
                            disabled={!table.getCanPreviousPage() || isLoading}
                            className="border rounded-md p-2"
                        >
                            <ChevronsLeftIcon
                                className={`h-10 w-10 ${!table.getCanPreviousPage() || isLoading
                                        ? 'text-slate-800'
                                        : 'text-slate-800'
                                    }`}
                            />
                        </Button>
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => table.previousPage()}
                            disabled={!table.getCanPreviousPage() || isLoading}
                            className="border rounded-md p-2"
                        >
                            <ChevronLeftIcon
                                className={`h-10 w-10 ${!table.getCanPreviousPage() || isLoading
                                        ? 'text-slate-800'
                                        : 'text-slate-800'
                                    }`}
                            />
                        </Button>
                        {/* Page Number Display */}
                        <span className="text-sm px-2">
                            Page {table.getState().pagination.pageIndex + 1} of{' '}
                            {table.getPageCount()}
                        </span>
                        {/* Next Page Button */}
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => table.nextPage()}
                            disabled={!table.getCanNextPage() || isLoading}
                            className="border rounded-md p-2"
                        >
                            <ChevronRightIcon
                                className={`h-10 w-10  ${!table.getCanNextPage() || isLoading
                                        ? 'text-slate-800'
                                        : 'text-slate-800'
                                    }`}
                            />
                        </Button>
                        {/* Last Page Button */}
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                            disabled={!table.getCanNextPage() || isLoading}
                            className="border rounded-md p-2"
                        >
                            <ChevronsRightIcon
                                className={`h-10 w-10 ${!table.getCanNextPage() || isLoading
                                        ? 'text-slate-800'
                                        : 'text-slate-800'
                                    }`}
                            />
                        </Button>
                    </div>
                </div>
            )}
        </>


    );
}
