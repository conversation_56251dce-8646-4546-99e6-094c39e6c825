import { Router } from 'express';
import {
  getAllStudentController,
  getAllStudentCountsController,
  getCountStudent,
  getCurrentStudentController,
  updateStudentController
} from '../controllers/studentAuthController';
import { googleAuthStudent } from '../controllers/studentGoogleAuthController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
// import validateRequest from '@/middlewares/validateRequest';
// import { loginSchema, registerSchema } from '../requests/authRequest'

const studentAuthRouter = Router();

// Google authentication
// studentAuthRouter.post('/register', validateRequest(registerSchema), registerStudent);
// studentAuthRouter.post('/login', validateRequest(loginSchema), loginStudent);
studentAuthRouter.post('/google-auth', googleAuthStudent);
// studentAuthRouter.post('/forgot-password', forgotPasswordController);
// studentAuthRouter.post('/verify-email', verifyEmail);
// studentAuthRouter.post('/resend-verification', resendVerificationEmail);
// studentAuthRouter.post('/reset-password', resetPasswordController);

// Student data endpoints
studentAuthRouter.get('/', getAllStudentController);
studentAuthRouter.get('/count', getCountStudent);
studentAuthRouter.get('/me', studentAuthMiddleware, getCurrentStudentController);
studentAuthRouter.put('/update', studentAuthMiddleware, updateStudentController);
studentAuthRouter.get('/counts', getAllStudentCountsController);

export default studentAuthRouter;