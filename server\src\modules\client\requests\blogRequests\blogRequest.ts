import { z } from "zod";

export const createBlogSchema = z.object({
  blogTitle: z.string().min(3, "Blog title must be at least 3 characters"),
  blogDescription: z.string().min(10, "Blog description must be at least 10 characters"),
  blogImage: z.string().optional(),
  classId: z.string().optional(), // This will be set by the controller
});

export const updateBlogSchema = z.object({
  blogTitle: z.string().min(3, "Blog title must be at least 3 characters").optional(),
  blogDescription: z.string().min(10, "Blog description must be at least 10 characters").optional(),
  blogImage: z.string().optional(),
  status: z.enum(["PENDING", "APPROVED", "REJECTED"]).optional(),
});

export const updateBlogStatusSchema = z.object({
  status: z.enum(["PENDING", "APPROVED", "REJECTED"]),
});

export type CreateBlogInput = z.infer<typeof createBlogSchema>;
export type UpdateBlogInput = z.infer<typeof updateBlogSchema>;
export type UpdateBlogStatusInput = z.infer<typeof updateBlogStatusSchema>;
