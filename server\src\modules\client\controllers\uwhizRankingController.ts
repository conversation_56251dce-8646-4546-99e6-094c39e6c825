import { Request, Response } from 'express';
import { getExamRankings, getUserExamRank } from '../services/uwhizRankingServices';

export async function getRankings(req: Request, res: Response): Promise<void> {
  try {
    const { examId } = req.params;
    const examIdNum = Number(examId);

    if (isNaN(examIdNum)) {
      res.status(400).json({ error: 'Valid examId is required' });
      return;
    }

    const rankings = await getExamRankings(examIdNum);
    res.status(200).json(rankings);
  } catch (error:unknown) {
    res.status(500).json({ error: String(error) || 'Internal server error' });
  }
}

// Fetches ranking for a specific user in an exam
export async function getUserRanking(req: Request, res: Response): Promise<void> {
  try {
    const { examId, userId } = req.params;
    const examIdNum = Number(examId);

    if (isNaN(examIdNum)) {
      res.status(400).json({ error: 'Valid examId is required' });
      return;
    }

    if (!userId || typeof userId !== 'string') {
      res.status(400).json({ error: 'Valid userId is required' });
      return;
    }

    const userRanking = await getUserExamRank(examIdNum, userId);
    res.status(200).json(userRanking);
  } catch (error: any) {
    res.status(500).json({ error: error.message || 'Internal server error' });
  }
}
