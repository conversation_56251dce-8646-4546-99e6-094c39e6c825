import { axiosInstance } from '@/lib/axios';
import { Blog, CreateBlogInput, PaginatedBlogResponse, UpdateBlogInput } from '@/lib/types';

export const getBlogs = async (page: number = 1, limit: number = 10): Promise<PaginatedBlogResponse> => {
  try {
    const response = await axiosInstance.get('/blogs', {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch blogs: ${error.message}`);
  }
};

export const getApprovedBlogs = async (page: number = 1, limit: number = 10): Promise<PaginatedBlogResponse> => {
  try {
    const response = await axiosInstance.get('/blogs/approved', {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch approved blogs: ${error.message}`);
  }
};

export const getMyBlogs = async (page: number = 1, limit: number = 10, status?: string): Promise<PaginatedBlogResponse> => {
  try {
    const response = await axiosInstance.get('/blogs/my-blogs', {
      params: { page, limit, status }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch your blogs: ${error.message}`);
  }
};

export const getBlogById = async (id: string): Promise<Blog> => {
  try {
    const response = await axiosInstance.get(`/blogs/${id}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to fetch blog: ${error.message}`);
  }
};

export const createBlog = async (data: CreateBlogInput): Promise<Blog> => {
  try {
    const formData = new FormData();
    formData.append('blogTitle', data.blogTitle);
    formData.append('blogDescription', data.blogDescription);

    if (data.blogImage) {
      formData.append('blogImage', data.blogImage);
    }

    const response = await axiosInstance.post('/blogs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to create blog: ${error.message}`);
  }
};

export const updateBlog = async (id: string, data: UpdateBlogInput): Promise<Blog> => {
  try {
    const formData = new FormData();

    if (data.blogTitle) {
      formData.append('blogTitle', data.blogTitle);
    }

    if (data.blogDescription) {
      formData.append('blogDescription', data.blogDescription);
    }

    if (data.blogImage) {
      formData.append('blogImage', data.blogImage);
    }

    if (data.status) {
      formData.append('status', data.status);
    }

    const response = await axiosInstance.put(`/blogs/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to update blog: ${error.message}`);
  }
};

export const deleteBlog = async (id: string): Promise<void> => {
  try {
    await axiosInstance.delete(`/blogs/${id}`);
  } catch (error: any) {
    throw new Error(error.response?.data?.message || `Failed to delete blog: ${error.message}`);
  }
};
