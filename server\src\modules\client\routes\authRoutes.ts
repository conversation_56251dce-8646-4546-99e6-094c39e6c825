import { Router } from 'express'
import { forgotPasswordController, loginUser, logout, registerUser, resendVerificationEmail, resetPasswordController, verifyEmail } from '../controllers/authController'
import validateRequest from '@/middlewares/validateRequest'
import { loginSchema, registerSchema } from '../requests/authRequest'

const authClientRouter = Router();

authClientRouter.post('/register', validateRequest(registerSchema), registerUser)
authClientRouter.post('/login', validateRequest(loginSchema), loginUser)
authClientRouter.get('/verify-email', verifyEmail)
authClientRouter.post('/resend-verification', resendVerificationEmail)
authClientRouter.post('/forgot-password', forgotPasswordController);
authClientRouter.post('/reset-password', resetPasswordController);
authClientRouter.post('/logout', logout);

export default authClientRouter;
