import { Router } from 'express';
import {
  exportClassesToExcel,
  exportExamApplicationsToExcel,
  exportStudentsToExcel,
} from '../controllers/exportController';
import { authMiddleware } from '@/middlewares/adminAuth';

const exportRouter = Router();

exportRouter.get('/classes/excel', authMiddleware, exportClassesToExcel);
exportRouter.get('/exam-applications/excel', authMiddleware, exportExamApplicationsToExcel);
exportRouter.get('/students/excel', authMiddleware, exportStudentsToExcel);

export default exportRouter;
