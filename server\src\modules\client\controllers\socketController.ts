import { Server, Socket } from "socket.io";
import { getPendingMessages, createMessage, markMessagesAsRead } from "../services/chatQueryService";
import { PrismaClient } from "@prisma/client";
import {
  SocketErrorHandler,
  handleDatabaseError,
  handleValidationError,
  withErrorHandling,
  SocketRateLimiter
} from "../../../utils/socketErrorHandler";

const prisma = new PrismaClient();

const createRoomId = (userId1: string, userId2: string): string => {
  const sortedIds = [userId1, userId2].sort();
  return `room_${sortedIds[0]}_${sortedIds[1]}`;
};

// Enhanced state management with better tracking
const seenMessages = new Map<string, boolean>();
const activeChats = new Map<string, string>(); // userId -> recipientId
const userSockets = new Map<string, string>(); // userId -> socketId
const socketUsers = new Map<string, string>(); // socketId -> userId

// Connection tracking for better reliability
const connectionHeartbeats = new Map<string, NodeJS.Timeout>();

// Initialize error handler and rate limiter
const errorHandler = SocketErrorHandler.getInstance();
const messageLimiter = new SocketRateLimiter(60000, 30); // 30 messages per minute
const connectionLimiter = new SocketRateLimiter(60000, 5); // 5 connections per minute

// Cleanup rate limiters every 5 minutes
setInterval(() => {
  messageLimiter.cleanup();
  connectionLimiter.cleanup();
}, 5 * 60 * 1000);

export const initializeSocket = (io: Server, onlineUsers: Map<string, { socketId: string; userType: string; userId: string }>) => {
  io.on("connection", (socket: Socket) => {
    console.log(`New socket connection: ${socket.id}`);

    // Set up heartbeat for connection monitoring
    const heartbeat = setInterval(() => {
      socket.emit('ping');
    }, 30000); // 30 seconds

    connectionHeartbeats.set(socket.id, heartbeat);

    socket.on('pong', () => {
      // Client responded to ping, connection is alive
    });

    socket.on("join", async (data: { username: string, userType: string, userId: string }) => {
      const { username, userType, userId } = data;

      // Enhanced user tracking with cleanup of previous connections
      const existingUserEntry = Array.from(onlineUsers.entries()).find(([_, userData]) => userData.userId === userId);
      if (existingUserEntry) {
        onlineUsers.delete(existingUserEntry[0]);
        userSockets.delete(userId);
        const oldSocketId = existingUserEntry[1].socketId;
        socketUsers.delete(oldSocketId);
      }

      onlineUsers.set(username, { socketId: socket.id, userType, userId });
      userSockets.set(userId, socket.id);
      socketUsers.set(socket.id, userId);

      console.log(`User joined: ${username} (${userId}) with socket ${socket.id}`);

      try {
        const pendingMessages = await getPendingMessages(userId, userType as 'student' | 'class');
        if (pendingMessages.length > 0) {
          pendingMessages.forEach((message: any) => {
            socket.emit("privateMessage", message);
          });
        }
      } catch (error) {
        console.error("Error fetching pending messages:", error);
      }

      try {
        const unreadMessages = await prisma.chatMessage.findMany({
          where: {
            recipientId: userId,
            recipientType: userType as 'student' | 'class',
            isRead: false
          },
          select: {
            senderId: true,
            senderType: true
          }
        });

        const unreadCounts = new Map<string, number>();
        unreadMessages.forEach((message: any) => {
          if (message.senderId !== userId) {
            const currentCount = unreadCounts.get(message.senderId) || 0;
            unreadCounts.set(message.senderId, currentCount + 1);
          }
        });

        const unreadCountsArray = Array.from(unreadCounts.entries()).map(([senderId, count]) => ({
          userId: senderId,
          unreadCount: count
        }));

        socket.emit("unreadCountsData", unreadCountsArray);
      } catch (error) {
        console.error("Error fetching initial unread counts:", error);
      }

      const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
        username,
        userType: data.userType,
        userId: data.userId
      }));
      io.emit("onlineUsers", users);
    });

    socket.on("joinChatRoom", (data: { userId: string, recipientId: string }) => {
      const { userId, recipientId } = data;
      const roomId = createRoomId(userId, recipientId);

      socket.join(roomId);
      activeChats.set(userId, recipientId);

      const otherUserData = Array.from(onlineUsers.entries()).find(([_, userData]) => userData.userId === recipientId);
      if (otherUserData) {
        io.to(otherUserData[1].socketId).emit("userStartedViewing", { viewerId: userId });
      }

      socket.emit("roomJoined", { roomId });
    });

    socket.on("leaveChatRoom", (data: { userId: string, recipientId: string }) => {
      const { userId, recipientId } = data;
      const roomId = createRoomId(userId, recipientId);

      socket.leave(roomId);
      activeChats.delete(userId);

      const otherUserData = Array.from(onlineUsers.entries()).find(([_, userData]) => userData.userId === recipientId);
      if (otherUserData) {
        io.to(otherUserData[1].socketId).emit("userStoppedViewing", { viewerId: userId });
      }

      socket.emit("roomLeft", { roomId });
    });

    socket.on("sendPrivateMessage", async (messageData: any) => {
      try {
        const { text, senderId, recipientId, senderType, recipientType } = messageData;

        if (!text || !senderId || !recipientId || !senderType || !recipientType) {
          socket.emit("error", { message: "Invalid message data" });
          return;
        }

        if (senderType === recipientType) {
          socket.emit("error", { message: "Cannot send messages to users with the same role" });
          return;
        }

        const message = await createMessage(text, senderId, senderType, recipientId, recipientType);

        let senderName = '';
        let recipientName = '';

        if (senderType === 'student') {
          const student = await prisma.student.findUnique({
            where: { id: senderId },
            select: { firstName: true, lastName: true }
          });
          senderName = student ? `${student.firstName} ${student.lastName}` : 'Unknown';
        } else if (senderType === 'class') {
          const classUser = await prisma.classes.findUnique({
            where: { id: senderId },
            select: { firstName: true, lastName: true }
          });
          senderName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown';
        }

        if (recipientId && recipientType === 'student') {
          const student = await prisma.student.findUnique({
            where: { id: recipientId },
            select: { firstName: true, lastName: true }
          });
          recipientName = student ? `${student.firstName} ${student.lastName}` : 'Unknown Student';
        } else if (recipientId && recipientType === 'class') {
          const classUser = await prisma.classes.findUnique({
            where: { id: recipientId },
            select: { firstName: true, lastName: true }
          });
          recipientName = classUser ? `${classUser.firstName} ${classUser.lastName}` : 'Unknown Class';
        }

        const roomId = createRoomId(senderId, recipientId);
        const isRecipientViewing = activeChats.get(recipientId) === senderId;

        const messageWithNames = {
          ...message,
          sender: senderName,
          recipient: recipientName,
          isRecipientViewing,
          senderId: message.senderId,
          recipientId: message.recipientId
        };

        // FIXED: Only emit to room, not individual sockets to prevent duplication
        // Users in the room will receive the message automatically
        io.to(roomId).emit("privateMessage", messageWithNames);

        // Also emit to sender and recipient if they're not in the room yet
        const senderSocketId = userSockets.get(senderId);
        const recipientSocketId = userSockets.get(recipientId);

        if (senderSocketId && !io.sockets.adapter.rooms.get(roomId)?.has(senderSocketId)) {
          io.to(senderSocketId).emit("privateMessage", messageWithNames);
        }

        if (recipientSocketId && !io.sockets.adapter.rooms.get(roomId)?.has(recipientSocketId)) {
          io.to(recipientSocketId).emit("privateMessage", messageWithNames);
        }

        // Update message users and unread counts using improved socket tracking
        const recipientSocketId = userSockets.get(recipientId);
        if (recipientSocketId) {
          io.to(recipientSocketId).emit("updateMessageUsers", {
            username: senderName,
            userId: senderId
          });

          if (!isRecipientViewing) {
            try {
              const unreadCount = await prisma.chatMessage.count({
                where: {
                  senderId: senderId,
                  recipientId: recipientId,
                  isRead: false
                }
              });

              io.to(recipientSocketId).emit("unreadCountUpdate", {
                senderId: senderId,
                senderName: senderName,
                unreadCount: unreadCount
              });
            } catch (error) {
              console.error("Error getting unread count:", error);
            }
          }
        }
      } catch (error) {
        console.error("Error saving private message:", error);
        socket.emit("error", { message: "Failed to send message" });
      }
    });

    socket.on("markMessagesAsSeen", async (data: { senderId: string, recipientId: string, messageIds: string[] }) => {
      const { senderId, recipientId, messageIds } = data;
      const recipientActiveWith = activeChats.get(recipientId);
      const isActivelyViewing = recipientActiveWith === senderId;

      if (isActivelyViewing) {
        messageIds.forEach(messageId => {
          seenMessages.set(messageId, true);
        });

        try {
          await markMessagesAsRead(senderId, recipientId);
          const recipientData = Array.from(onlineUsers.entries()).find(([_, userData]) => userData.userId === recipientId);
          if (recipientData) {
            io.to(recipientData[1].socketId).emit("unreadCountUpdate", {
              senderId: senderId,
              senderName: "",
              unreadCount: 0
            });
          }
        } catch (error) {
          console.error('Error marking messages as read:', error);
        }

        const senderData = Array.from(onlineUsers.entries()).find(([_, userData]) => userData.userId === senderId);
        if (senderData) {
          io.to(senderData[1].socketId).emit("messagesMarkedAsSeen", {
            byUserId: recipientId,
            messageIds: messageIds
          });
        }
      }
    });

    socket.on("getSeenStatus", (data: { messageIds: string[] }) => {
      const { messageIds } = data;
      const seenStatus: { [key: string]: boolean } = {};

      messageIds.forEach(messageId => {
        seenStatus[messageId] = seenMessages.has(messageId);
      });

      socket.emit("seenStatus", seenStatus);
    });

    socket.on("getOnlineUsers", () => {
      const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
        username,
        userType: data.userType,
        userId: data.userId
      }));
      io.emit("onlineUsers", users);
    });

    socket.on("getUnreadCounts", async (data: { userId: string, userType: string }) => {
      try {
        const { userId, userType } = data;
        const unreadMessages = await prisma.chatMessage.findMany({
          where: {
            recipientId: userId,
            recipientType: userType as 'student' | 'class',
            isRead: false
          },
          select: {
            senderId: true,
            senderType: true
          }
        });

        const unreadCounts = new Map<string, number>();
        unreadMessages.forEach((message: any) => {
          if (message.senderId !== userId) {
            const currentCount = unreadCounts.get(message.senderId) || 0;
            unreadCounts.set(message.senderId, currentCount + 1);
          }
        });

        const unreadCountsArray = Array.from(unreadCounts.entries()).map(([senderId, count]) => ({
          userId: senderId,
          unreadCount: count
        }));

        socket.emit("unreadCountsData", unreadCountsArray);
      } catch (error) {
        console.error("Error fetching unread counts:", error);
        socket.emit("error", { message: "Failed to fetch unread counts" });
      }
    });

    socket.on("disconnect", () => {
      console.log(`Socket disconnected: ${socket.id}`);

      // Clean up heartbeat
      const heartbeat = connectionHeartbeats.get(socket.id);
      if (heartbeat) {
        clearInterval(heartbeat);
        connectionHeartbeats.delete(socket.id);
      }

      // Enhanced cleanup with better tracking
      const disconnectedUserId = socketUsers.get(socket.id);
      let disconnectedUsername: string | null = null;

      // Clean up all tracking maps
      if (disconnectedUserId) {
        userSockets.delete(disconnectedUserId);
        socketUsers.delete(socket.id);

        // Find and remove from onlineUsers
        for (const [username, userData] of onlineUsers.entries()) {
          if (userData.socketId === socket.id) {
            disconnectedUsername = username;
            onlineUsers.delete(username);
            break;
          }
        }

        // Clean up active chats
        const recipientId = activeChats.get(disconnectedUserId);
        activeChats.delete(disconnectedUserId);

        // Notify other user that this user stopped viewing
        if (recipientId) {
          const recipientSocketId = userSockets.get(recipientId);
          if (recipientSocketId) {
            io.to(recipientSocketId).emit("userStoppedViewing", { viewerId: disconnectedUserId });
          }
        }

        console.log(`User disconnected: ${disconnectedUsername} (${disconnectedUserId})`);
      }

      // Broadcast updated online users list
      const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
        username,
        userType: data.userType,
        userId: data.userId
      }));
      io.emit("onlineUsers", users);
    });
  });
};

export default initializeSocket;