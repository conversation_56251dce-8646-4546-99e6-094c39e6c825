import { SetStateAction } from "react";

// Exam-related Types
export interface Exam {
  total_student_intake: number | undefined;
  id: number;
  exam_name: string;
  start_date: string;
  duration: number;
  marks: string | number;
  total_questions: number;
  level: "easy" | "medium" | "hard";
  // max_classes_can_join: number;
  // max_questions_class_can_apply?: number;
  start_registration_date?: string;
  coins_required: number;
  exam_type: "CLASSES" | "STUDENTS";

  createdAt: string;
  updatedAt: string;
}

export interface TransformedExam {
  id: number;
  exam_name: string;
  start_date: string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: "easy" | "medium" | "hard";
  coins_required: number;
  exam_type: "CLASSES" | "STUDENTS";
  start_registration_date: string;
  createdAt: string;
  updatedAt: string;
}

export interface ExamInput {
  exam_name: string;
  start_date: string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: "easy" | "medium" | "hard";
  // max_classes_can_join: number;
  // max_questions_class_can_apply: number;
  start_registration_date?: string;
  coins_required?: number;
  exam_type?: "CLASSES" | "STUDENTS";
}

// Applicant Classes Type
export interface ExamClass {
  id: number;
  examName: string;
  classesName: string;
  status: "ACCEPT" | "REJECT" | "PENDING";
}

export interface PaginatedResponse<T> {
  data: SetStateAction<ExamClass[]>;
  max_questions_class_can_apply: SetStateAction<number>;
  exam_name: string;
  exam: any;
  questions: T[];
  total_questions: number;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Question-related Types
export interface Question {
  id: number;
  examId: number;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAns: string;
  createdAt: string;
  updatedAt: string;
}

export interface QuestionInput {
  examId: number;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAns: string;
}

// For Results
export interface Result {
  userId: number;
  userName: string;
  examName: string;
  correctAnswers: number;
  totalQuestions: number;
  attempted: boolean;
  success: boolean;
  message: string;
  results: Result[];
}

export interface Answer {
  id: number;
  userId: number;
  questionId: number;
  answer: string;
}

export interface ExamQuestionsResponse {
  questions: Question[];
  total_questions: number;
  exam_name: string;
}

//types for quiz
export interface Option {
  key: string;
  value: string;
}

export interface Question {
  id: number;
  text: string;
  options: Option[];
  correctAnswer: string;
}

export interface QuizCardProps {
  examId: number | null;
}

export interface ExamApplication {
  id: string;
  examId: number;
  classId: string;
  createdAt: string;
  class: {
    firstName: string;
    lastName: string;
    className: string | null;
  };
  exam: { exam_name: string };
  classes: { firstName: string; lastName: string; className: string | null };
}

export interface PaginationResponse {
  applications: ExamApplication[];
  total: number;
  currentPage: number;
  totalPages: number;
}

// for email
export interface emailData {
  email?: any;
  subject: string;
  message: any;
  status?: string;
}

//question bank
export interface QuestionBank {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: "optionOne" | "optionTwo" | "optionThree" | "optionFour";
  medium: "ENGLISH" | "GUJARATI";
  standard: string;
  subject: string;
  level: "EASY" | "MEDIUM" | "HARD";
}

export interface QuestionBankInput {
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer:
    | "optionOne"
    | "optionTwo"
    | "optionThree"
    | "optionFour"
    | undefined;
  medium: "ENGLISH" | "GUJARATI" | undefined;
  standard: string;
  subject: string;
  level: "EASY" | "MEDIUM" | "HARD" | undefined;
}
export interface Blog {
  id: string;
  blogTitle: string;
  blogImage: string;
  blogDescription: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  classId?: string;
  class?: {
    id: string;
    firstName: string;
    lastName: string;
    className: string;
  };
}

export interface PaginatedBlogResponse {
  blogs: Blog[];
  total: number;
  currentPage: number;
  totalPages: number;
}

export interface Thought {
  id: string;
  classId: string;
  thoughts: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  class: {
    id: string;
    firstName: string;
    lastName: string;
    className: string;
    contactNo: string;
    email: string;
    username: string;
  };
}

export interface Reviews {
  id: string;
  message: string;
  rating: number;
  createdAt: string;
  classId: string;
  studentName: string;
  class: {
    className: string;
  };
}

export interface Testimonial {
  id: string;
  message: string;
  rating: number;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  class: {
    className: string;
    firstName: string;
    lastName: string;
    fullName?: string;
    classesLogo?: string | null;
    profilePhoto?: string | null;
    tutorBio?: string | null;
    ClassAbout?: {
      classesLogo?: string | null;
      profilePhoto?: string | null;
      tutorBio?: string | null;
    };
  };
}

export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  contact: string;
  coins?: number;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  profile?: StudentProfile;
}

export interface StudentProfile {
  id: string;
  studentId: string;
  medium: string;
  classroom: string;
  birthday: string;
  school: string;
  address: string;
  photo?: string;
  documentUrl?: string;
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  student?: Student;
}

export interface UwhizPriceRank {
  id: string;
  examId: number;
  rank: number;
  price: number;
  exam?: {
    exam_name: string;
  };
}

export interface Applicant {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  contact:string
}

export interface ApplicantsResponse {
  total: number;
  page: number;
  limit: number;
  data: Applicant[];
}

export type Applicants = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  contact:string
};

export type ApiResponse = {
  success: boolean;
  total: number;
  page: number;
  limit: number;
  data: Applicant[] | terminatedStudents[];
};

export type ClassData = {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  contactNo: string;
  className: string;
  isVerified: boolean;
  createdAt: string;
  ClassAbout: {
    id: string;
    birthDate: string;
    catchyHeadline: string;
    tutorBio: string;
    profilePhoto: string;
    classesLogo: string;
  };
  education: {
    id: string;
    degree: string;
    university: string;
    passoutYear: string;
    degreeType: string;
    isDegree: boolean;
  }[];
  experience: {
    id: string;
    title: string;
    from: string;
    to: string;
    isExperience: boolean;
  }[];
  certificates: {
    id: string;
    title: string;
    isCertificate: boolean;
    certificateUrl: string;
  }[];
  tuitionClasses: {
    id: string;
    education: string;
    coachingType: string;
    boardType: string;
    medium: string;
    section: string;
    subject: string;
    details: string;
  }[];
  status:{
    id:string;
    status:string;
  }
};

export interface subjectPrefrence {
  examId: number;
  weightage: number;
  subject?: string;
  level?: string;
  id?: string;
}

export interface LevelPreference {
  id?: string;
  level: string;
  weightage: number;
  examId:number;
}


export type terminatedStudents = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  reason:string;
  contact:string;
};

export interface ChatMessage {
  id: string;
  text: string;
  sender: string;
  recipient?: string;
  timestamp: string;
  senderType?: string;
  recipientType?: string;
}

export interface OnlineUser {
  username: string;
  userType: string;
}

export interface AdminChatProps {
  isAuthenticated: boolean;
  loginPath: string;
}

export interface Class {
  id: string;
  firstName: string;
  lastName: string;
  className?: string;
  email: string;
  username: string;
  createdAt: string;
}

export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: string;
  recipient?: string;
  timestamp: string;
  senderType?: string;
  recipientType?: string;
}

export interface ConversationResponse {
  classDetails: {
    id: string;
    name: string;
    username: string;
  };
  studentDetails: {
    id: string;
    name: string;
  };
  messages: ChatMessage[];
}