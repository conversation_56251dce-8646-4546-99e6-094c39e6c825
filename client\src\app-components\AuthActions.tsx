import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { clearUser } from "@/store/slices/userSlice";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { axiosInstance } from "@/lib/axios";

const AuthActions = ({ toggleModal }: { toggleModal: () => void }) => {
  const dispatch = useDispatch();
  const { user, isAuthenticated } = useSelector(
    (state: RootState) => state.user
  );
  const classData = useSelector((state: RootState) => state.class.classData);
  const router = useRouter();

  const handleLogout = async () => {
    const response = await axiosInstance.post("/auth-client/logout", {});
    if (response.data.success) {
      router.push("/");
      dispatch(clearUser());
      localStorage.removeItem("token");
    }
  };

  console.log(classData)

  if (isAuthenticated && user) {
    const initials =
      `${user.firstName?.[0] ?? "U"}${user.lastName?.[0] ?? "A"}`.toUpperCase();

    return (
      <Popover>
        <PopoverTrigger asChild>
          <Avatar className="cursor-pointer">
            <AvatarImage
              src={
                process.env.NEXT_PUBLIC_API_BASE_URL +
                classData?.ClassAbout?.classesLogo || ""
              }
            />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
        </PopoverTrigger>
        <PopoverContent className="w-64">
          <div className="mb-4 p-3 border border-[#ff914d]/20 rounded-lg bg-white">
            <div className="flex items-center gap-3">
              <Avatar className="h-12 w-12 border-2 border-[#ff914d]">
                <AvatarFallback className="bg-white text-black">
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium text-black">{user?.firstName} {user?.lastName}</p>
                <p className="text-xs text-gray-600">{user?.className}</p>
              </div>
            </div>
          </div>
          <Button asChild className="w-full mb-2">
            <Link href="/classes/profile">Dashboard</Link>
          </Button>
          <Button variant="outline" className="w-full" onClick={handleLogout}>
            Logout
          </Button>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <div className="flex items-center space-x-4">
      <Button
        className="bg-customOrange hover:bg-[#E88143] text-white"
        onClick={toggleModal}
      >
        Join as a classes
      </Button>
    </div>
  );
};

export default AuthActions;
