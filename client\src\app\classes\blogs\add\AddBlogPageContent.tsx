"use client";
import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import { useSelector } from "react-redux";
import { useRouter, useSearchParams } from "next/navigation";
import dynamic from "next/dynamic";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader2, AlertCircle } from "lucide-react";
import { createBlog, getBlogById, updateBlog } from "@/services/blogApi";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });
import "react-quill-new/dist/quill.snow.css";

// Define the blog form schema using Zod
const blogFormSchema = z.object({
  blogTitle: z.string().min(3, "Blog title must be at least 3 characters"),
  blogDescription: z.string().min(10, "Blog description must be at least 10 characters"),
  blogImage: z.custom<File>((val) => val instanceof File || !!val, {
    message: "Blog image is required"
  }),
});

type BlogFormValues = z.infer<typeof blogFormSchema>;

// Form error alert component
const FormErrorAlert = ({ message }: { message: string }) => {
  if (!message) return null;

  return (
    <Alert className="mb-4 border-red-500 bg-red-50 dark:bg-red-900/20">
      <AlertCircle className="h-4 w-4 text-red-500" />
      <AlertDescription className="text-red-500">{message}</AlertDescription>
    </Alert>
  );
};

const AddBlogPageContent = () => {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string>("");

  const { user } = useSelector((state: any) => state.user);
  const router = useRouter();
  const searchParams = useSearchParams();
  const blogId = searchParams.get("id");
  const isEditing = !!blogId;

  // Initialize the form with React Hook Form and Zod validation
  const form = useForm<BlogFormValues>({
    resolver: zodResolver(blogFormSchema),
    defaultValues: {
      blogTitle: "",
      blogDescription: "",
      blogImage: undefined,
    },
    mode: "onChange",
  });

  const fetchBlog = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      const blogData = await getBlogById(id);

      // Set form values
      form.setValue("blogTitle", blogData.blogTitle);
      form.setValue("blogDescription", blogData.blogDescription);

      if (blogData.blogImage) {
        const cleanPath = blogData.blogImage.replace(/^\/+/, '');
        const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/';
        const imageUrl = `${baseUrl}${cleanPath}`;
        setImagePreview(imageUrl);

        // Set a placeholder value for the image field to pass validation
        form.setValue("blogImage", new File([], "existing-image.jpg"));
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch blog");
      router.push("/classes/blogs");
    } finally {
      setIsLoading(false);
    }
  }, [router, form]);

  useEffect(() => {
    if (!user) {
      router.push("/");
      return;
    }

    if (isEditing && blogId) {
      fetchBlog(blogId);
    }
  }, [user, router, isEditing, blogId, fetchBlog]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
      
      if (!allowedTypes.includes(file.type)) {
        toast.error("Only image files (.jpg, .jpeg, .png) are allowed");
        e.target.value = ""; // Clear the input
        return;
      }

      form.setValue("blogImage", file);

      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (values: BlogFormValues) => {
    setApiError("");

    try {
      if (isEditing && blogId) {
        await updateBlog(blogId, {
          blogTitle: values.blogTitle,
          blogDescription: values.blogDescription,
          blogImage: values.blogImage as File,
        });
        toast.success("Blog updated successfully");
      } else {
        await createBlog({
          blogTitle: values.blogTitle,
          blogDescription: values.blogDescription,
          blogImage: values.blogImage as File,
        });
        toast.success("Blog created successfully");
      }

      router.push("/classes/blogs");
    } catch (error: any) {
      const errorMessage = error.message || "Failed to save blog";
      setApiError(errorMessage);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 px-4 flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>{isEditing ? "Edit Blog" : "Create New Blog"}</CardTitle>
        </CardHeader>
        <CardContent>
          {apiError && <FormErrorAlert message={apiError} />}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="blogTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter blog title"
                        className="w-full"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="blogImage"
                render={({  }) => (
                  <FormItem>
                    <FormLabel>Image</FormLabel>
                    <FormControl>
                      <Input
                        id="image"
                        type="file"
                        accept=".jpg,.jpeg,.png"
                        onChange={handleImageChange}
                        className="w-full"
                      />
                    </FormControl>
                    {imagePreview && (
                      <div className="mt-4 flex justify-center items-center h-60 w-full border rounded-md p-2 overflow-hidden">
                        <Image
                          src={imagePreview}
                          alt="Blog Preview"
                          width={500}
                          height={500}
                          className="object-contain rounded-md"
                          style={{
                            maxHeight: '100%',
                            maxWidth: '100%',
                            display: 'block',
                            margin: 'auto'
                          }}
                        />
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="blogDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <div className="h-80">
                        <ReactQuill
                          theme="snow"
                          value={field.value}
                          onChange={field.onChange}
                          className="h-full"
                          modules={{
                            toolbar: [
                              [{ header: [1, 2, 3, 4, 5, 6, false] }],
                              ["bold", "italic", "underline", "strike"],
                              [{ list: "ordered" }, { list: "bullet" }],
                              [{ indent: "-1" }, { indent: "+1" }],
                              [{ align: [] }],
                              ["link", "image"],
                              ["clean"],
                            ],
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-4 mt-20">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/classes/blogs")}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditing ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    <>{isEditing ? "Update" : "Create"}</>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddBlogPageContent;
