import { transporter } from '@/utils/email';
import { createNotificationTemplate } from '@/utils/emailTemplates';
import { sendError, sendSuccess } from '@/utils/response';
import { Request, Response } from 'express';

export const sendExamApplicantEmail = async (req: Request, res: Response): Promise<any> => {
    const { email, examId, exam_name } = req.body;

    if (!email || !examId || !exam_name) {
        return sendError(res, "All fields are required", 400);
    }

    try {
        const emailHtml = createNotificationTemplate(
            `You have been added to the exam ${exam_name}`,
            `Congratulations! ${email} <br/> You have been added to the exam ${exam_name}. Please login to your account to view the exam details. https://uest.in/uwhiz`
        );

        await transporter.sendMail({
            from: process.env.EMAIL_USER,
            to: email,
            subject: `Successfully applied for ${exam_name}`,
            html: emailHtml,
        });

        const adminNotificationHtml = createNotificationTemplate(
            `${email} has been added to the exam ${exam_name}`,
            `A new application has been received for the exam ${exam_name}. <br/><br/>
            <strong>Applicant Email:</strong> ${email}<br/>
            <strong>Exam:</strong> ${exam_name}<br/>
            <strong>Application Time:</strong> ${new Date().toLocaleString()}<br/><br/>
            Please review the application in the admin dashboard.`
        );

        const adminEmail = process.env.ADMIN_EMAIL ;

        await transporter.sendMail({
            from: process.env.EMAIL_USER,
            to: adminEmail,
            subject: `New Exam Application - ${exam_name}`,
            html: adminNotificationHtml,
        });

        return sendSuccess(res, { email, examId, exam_name }, 'Email sent successfully');

    } catch (error: any) {
        return sendError(res, `Failed to send emails: ${error.message}`, 500);
    }
};