import { axiosInstance } from '@/lib/axios';

export const sendExamApplicantEmail = async (
  examId: number,
  exam_name: string,
  email: string
): Promise<any> => {
  try {
    const response = await axiosInstance.post(
      '/examApplicantEmail/send-exam-applicant-email',
      {
        examId,
        exam_name,
        email,
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to send exam applicant email');
  }
};
