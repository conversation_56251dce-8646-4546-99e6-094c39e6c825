'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Eye, EyeOff } from 'lucide-react';
import Image from 'next/image';


const schema = z.object({
  username: z.string().min(1, { message: 'Username is required' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

type FormData = z.infer<typeof schema>;

export default function SMWaterparkLoginForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({ resolver: zodResolver(schema) });

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      const ADMIN = {
        username: 'smadmin',
        password: 'sm@2024'
      };

      if (data.username !== ADMIN.username || data.password !== ADMIN.password) {
        toast.error('Login failed', {
          description: 'Invalid credentials. Please check your username and password.'
        });
        setLoading(false);
        return;
      }

      const token = `sm_admin_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const adminData = {
        username: ADMIN.username,
        parkType: 'SM',
        role: 'admin',
        loginTime: new Date().toISOString()
      };

      localStorage.setItem('sm_admin_token', token);
      localStorage.setItem('sm_admin_data', JSON.stringify(adminData));

      toast.success('Login successful', {
        description: `Welcome to SM Water Park Dashboard!`
      });
      router.push('/smwaterpark/dashboard');
    } catch (err: any) {
      console.error('Login error:', err);
      toast.error('Login failed', {
        description: 'Something went wrong. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-svh w-full items-center justify-center p-6 md:p-10">
      <div className={cn('w-full max-w-sm')}>
        <Card>
          <CardHeader className="flex flex-col items-center gap-4">
            <CardTitle className="text-xl font-semibold text-center">SM Water Park</CardTitle>
            <Image
              src="/sm_water_park.png"
              alt="SM Water Park Logo"
              width={120}
              height={120}
              className="rounded-md object-contain"
            />
            <CardDescription className="text-center text-muted-foreground">
              Admin Dashboard Login
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
              <div className="grid gap-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  type="text"
                  {...register('username')}
                  placeholder="Enter your username"
                />
                {errors.username && <p className="text-sm text-red-500">{errors.username.message}</p>}
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    {...register('password')}
                    placeholder="Enter your password"
                    className="pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500">{errors.password.message}</p>
                )}
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? 'Logging in...' : 'Login to Dashboard'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}