import { Request, Response } from 'express';
import * as examApplicationService from '../services/examApplicationService';

// Apply for an exam
export const applyForExam = async (req: Request, res: Response): Promise<void> => {
  const { examId, classId } = req.body;

  if (!examId || !classId || isNaN(Number(examId))) {
    res.status(400).json({
      message: 'examId and classId are required, and examId must be a valid number',
    });
    return;
  }

  try {
    const { application, joinedClassesCount } = await examApplicationService.applyForExam(
      Number(examId),
      classId
    );
    res.status(201).json({
      message: 'Successfully applied for the exam',
      application,
      joinedClassesCount,
    });
  } catch (error: any) {
    res.status(400).json({
      message: 'Error applying for exam',
      error: error.message,
    });
  }
};

// Get all exam applications (for admin)
export const getAllExamApplications = async (req: Request, res: Response): Promise<void> => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  if (page < 1 || limit < 1) {
    res.status(400).json({
      message: 'Page and limit must be positive numbers',
    });
    return;
  }

  try {
    const result = await examApplicationService.getAllExamApplications(page, limit);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(400).json({
      message: 'Error fetching exam applications',
      error: error.message,
    });
  }
};
