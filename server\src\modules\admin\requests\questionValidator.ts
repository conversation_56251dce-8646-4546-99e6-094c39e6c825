import { z } from 'zod';

export const questionSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  optionOne: z.string().min(1, 'Option one is required'),
  optionTwo: z.string().min(1, 'Option two is required'),
  optionThree: z.string().min(1, 'Option three is required'),
  optionFour: z.string().min(1, 'Option four is required'),
  correctAns: z.string().min(1, 'Correct answer is required'),
  examId: z.number().positive('Valid exam ID is required'),
});

export const paginationSchema = z.object({
  examId: z.number().positive('Valid exam ID is optional').optional(),
  page: z.number().min(1, 'Page must be at least 1').optional().default(1),
  limit: z
    .number()
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .optional()
    .default(10),
  search: z.string().optional().default(''),
});

export type QuestionInput = z.infer<typeof questionSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;
