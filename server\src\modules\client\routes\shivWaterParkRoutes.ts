import { Router } from 'express';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import {
  generateShivWaterParkTicketController,
  getShivWaterParkTicketByCodeController,
  getAllShivWaterParkTicketsController
} from '../controllers/ticketController';

const shivWaterParkRouter = Router();



shivWaterParkRouter.post('/generate', studentAuthMiddleware, generateShivWaterParkTicketController);

shivWaterParkRouter.get('/ticket/:ticketCode', getShivWaterParkTicketByCodeController);

shivWaterParkRouter.get('/admin/all', getAllShivWaterParkTicketsController);

export default shivWaterParkRouter;