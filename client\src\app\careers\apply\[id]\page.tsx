"use client";
import { use, useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { format, parse } from "date-fns";
import { Plus, Trash2, ChevronLeft, MapPin } from "lucide-react";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import Link from "next/link";
import { fetchJobById, saveCareerDetails } from "@/services/careerService";
import { toast } from "sonner";
import { AppDatePicker } from "@/app-components/AppDatePicker";
import { useRouter } from "next/navigation";

// Zod schema with YYYY-MM-DD string dates
const formSchema = z.object({
  first_name: z.string().min(1, "First name is required").max(200),
  middle_name: z.string().min(1, "Middle name is required").max(200),
  last_name: z.string().min(1, "Last name is required").max(200),
  contact_number: z
    .string()
    .min(10, "Contact number must be 10 digits")
    .max(10)
    .regex(/^\d+$/, "Contact number must be numeric"),
  email: z.string().email("Invalid email address").max(200),
  date: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .refine(
      (val) => {
        const date = parse(val, "yyyy-MM-dd", new Date());
        return date <= new Date();
      },
      { message: "Application date cannot be in the future" }
    ),
  message: z.string().max(1000).optional().nullable(),
  address: z.string().min(1, "Address is required").max(500),
  date_of_birth: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .refine(
      (val) => {
        const date = parse(val, "yyyy-MM-dd", new Date());
        return date < new Date();
      },
      { message: "Date of birth must be in the past" }
    ),
  marital_status: z.enum(["Single", "Married"], {
    errorMap: () => ({ message: "Select a valid marital status" }),
  }),
  qualification: z.string().min(1, "Qualification is required").max(200),
  specialization: z.string().min(1, "Specialization is required").max(200),
  hobbies: z.string().min(1, "Hobbies are required").max(500),
  current_salary: z
    .number()
    .min(0, "Current salary must be non-negative")
    .refine((val) => !isNaN(val), { message: "Invalid number" }),
  expected_salary: z
    .number()
    .min(0, "Expected salary must be non-negative")
    .refine((val) => !isNaN(val), { message: "Invalid number" }),
  work_experiences: z
    .array(
      z.object({
        work_experience: z.string().min(1, "Work experience is required"),
        previous_company: z.string().min(1, "Previous company is required"),
        designation: z.string().min(1, "Designation is required"),
        start_duration: z
          .string()
          .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
          .refine(
            (val) => {
              const date = parse(val, "yyyy-MM-dd", new Date());
              return date >= new Date(1900, 0, 1);
            },
            { message: "Invalid start date" }
          ),
        end_duration: z
          .string()
          .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
          .refine(
            (val) => {
              const date = parse(val, "yyyy-MM-dd", new Date());
              return date >= new Date(1900, 0, 1);
            },
            { message: "Invalid end date" }
          ),
      })
    )
    .optional(),
  upload_file: z
    .instanceof(File, { message: "File is required" })
    .refine((file) => file.type === "application/pdf", {
      message: "File must be a PDF",
    })
    .refine((file) => file.size <= 2 * 1024 * 1024, {
      message: "File must be less than 2MB",
    }),
});

interface Jobs {
  id: string;
  job_title: string;
  description: string;
}

export default function JobApplicationForm({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [jobdata, setJobData] = useState<Jobs>({
    id: "",
    job_title: "",
    description: "",
  });

  useEffect(() => {
    async function fetchJobsById() {
      const job = await fetchJobById(id);
      setJobData(job);
    }

    fetchJobsById();
  }, [id]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      first_name: "",
      middle_name: "",
      last_name: "",
      contact_number: "",
      email: "",
      date: format(new Date(), "yyyy-MM-dd"), // Current date in YYYY-MM-DD
      message: "",
      address: "",
      date_of_birth: "",
      marital_status: "Single",
      qualification: "",
      specialization: "",
      hobbies: "",
      current_salary: 0,
      expected_salary: 0,
      work_experiences: [],
      upload_file: undefined,
    },
  });
  const { reset } = form;
  const router = useRouter();

  const {
    fields: workExperiences,
    append,
    remove,
  } = useFieldArray({
    control: form.control,
    name: "work_experiences",
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    try {
      const formData = new FormData();
      Object.entries(values).forEach(([key, value]) => {
        if (key === "work_experiences" && value) {
          formData.append(key, JSON.stringify(value));
        } else if (key === "upload_file" && value instanceof File) {
          formData.append(key, value);
        } else if (value !== undefined && value !== null) {
          formData.append(key, String(value));
        }
      });
      formData.append("title", jobdata.job_title);

      const response = await saveCareerDetails(formData);

      if (response.success) {
        toast(response.success);
        reset();

        router.push('/careers');
      } else {
        toast.error(response.error);
      }

    } catch (error) {
      console.error("Submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <Header />
      <div className="min-h-screen bg-white dark:bg-black text-black dark:text-white">
        <section className="py-16 bg-black dark:bg-black text-white border-b border-zinc-700">
          <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
            <Link
              href="/careers"
              className="inline-flex items-center text-gray-400 hover:text-[#FD904B] font-medium mb-6 transition-colors"
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Careers
            </Link>
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {jobdata.job_title}
            </h1>
            <div className="flex flex-wrap items-center gap-4 text-gray-300">
              <div className="flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-[#FD904B]" />
                Morbi
              </div>
              <span className="bg-[#FD904B] text-white px-3 py-1 rounded-full text-sm font-medium">
                Full-time
              </span>
            </div>
          </div>
        </section>
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mt-10 py-10">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-8 bg-white dark:bg-black rounded-lg p-8 shadow-xl border border-gray-200 dark:border-gray-700"
            >
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        First Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="John"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="middle_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Middle Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="Michael"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Last Name
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="Doe"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="contact_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Contact Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="tel"
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="1234567890"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="<EMAIL>"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black dark:text-white">
                      Address
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                        placeholder="123 Main St, Morbi"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="date_of_birth"
                  render={({ field }) => (
                    <AppDatePicker field={field} label="Birthdate" />
                  )}
                />
                <FormField
                  control={form.control}
                  name="marital_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Marital Status
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600">
                          <SelectItem value="Single">Single</SelectItem>
                          <SelectItem value="Married">Married</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black dark:text-white">
                      Additional Message (Optional)
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                        placeholder="Tell us why you're a great fit..."
                        value={field.value ?? ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Education and Skills */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="qualification"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Qualification
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="B.Sc. Computer Science"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="specialization"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Specialization
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="Web Development"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="hobbies"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black dark:text-white">
                      Hobbies
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                        placeholder="Reading, hiking, coding"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Salary Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="current_salary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Current Salary
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="50000"
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="expected_salary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white">
                        Expected Salary
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="number"
                          className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                          placeholder="60000"
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Work Experience */}
              <div>
                <h2 className="text-xl font-semibold text-black dark:text-white mb-4">
                  Work Experience (Optional)
                </h2>
                {workExperiences.map((field, index) => (
                  <div
                    key={field.id}
                    className="space-y-4 mb-6 border-b border-gray-200 dark:border-gray-600 pb-4"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name={`work_experiences.${index}.work_experience`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-black dark:text-white">
                              Experience #{index + 1}
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                                placeholder="Senior Developer"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name={`work_experiences.${index}.previous_company`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-black dark:text-white">
                              Company
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                                placeholder="Tech Corp"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name={`work_experiences.${index}.designation`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-black dark:text-white">
                              Designation
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                                placeholder="Lead Engineer"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`work_experiences.${index}.start_duration`}
                          render={({ field }) => (
                            <AppDatePicker field={field} label="From" />
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`work_experiences.${index}.end_duration`}
                          render={({ field }) => (
                            <AppDatePicker field={field} label="to" />
                          )}
                        />

                      </div>
                    </div>
                    <Button
                      type="button"
                      size="sm"
                      onClick={() => remove(index)}
                      className="mt-2 bg-red-600 hover:bg-red-700 text-white"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    append({
                      work_experience: "",
                      previous_company: "",
                      designation: "",
                      start_duration: "",
                      end_duration: "",
                    })
                  }
                  className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600 hover:bg-[#FD904B] hover:text-white dark:hover:text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Work Experience
                </Button>
              </div>

              {/* File Upload */}
              <FormField
                control={form.control}
                name="upload_file"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black dark:text-white">
                      Upload Resume (PDF, max 2MB)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        accept="application/pdf"
                        className="bg-white dark:bg-black text-black dark:text-white border-gray-300 dark:border-gray-600"
                        onChange={(e) =>
                          field.onChange(e.target.files?.[0] || null)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Submit Button */}
              <div className="text-right">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-[#FD904B] text-white hover:bg-[#e67e22]"
                >
                  {isSubmitting ? "Submitting..." : "Submit Application"}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
      <Footer />
    </>
  );
}