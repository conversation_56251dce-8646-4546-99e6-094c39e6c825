'use client';

import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import SharedChat from '@/app-components/SharedChat';

export default function ClassesChat() {
    const [username, setUsername] = useState('');
    const [userId, setUserId] = useState('');

    const { user, isAuthenticated } = useSelector((state: RootState) => state.user);

    useEffect(() => {
        if (isAuthenticated && user) {
            const userName = `${user.firstName} ${user.lastName}` || user.email.split('@')[0];
            setUsername(userName);
            setUserId(user.id);
        }
    }, [isAuthenticated, user]);
    
    return (
        <SharedChat
            userType="class"
            isAuthenticated={isAuthenticated}
            username={username}
            userId={userId}
            loginPath="/"
        />
    );
}