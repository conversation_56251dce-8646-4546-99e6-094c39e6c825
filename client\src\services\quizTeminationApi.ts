import { axiosInstance } from '@/lib/axios';

export const saveTerminatedStudent = async (data: any) => {
  try {
    const response = await axiosInstance.post('/quizTermination', data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to save termination log: ${error.response?.data?.message || error.message}`,
    };
  }
};