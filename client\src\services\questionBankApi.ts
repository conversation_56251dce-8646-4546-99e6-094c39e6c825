import {axiosInstance} from "../lib/axios";
import { QuestionBankInput } from "../lib/types";

//Get all Question Bank Detail
export const getQuestionBank = async (
  page: number = 1,
  limit: number = 10,
  filters: {
    medium?: string;
    standard?: string;
    level?: string;
    subject?: string;
  } = {}
): Promise<any> => {
  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(filters.medium && { medium: filters.medium }),
      ...(filters.standard && { standard: filters.standard }),
      ...(filters.level && { level: filters.level }),
      ...(filters.subject && { subject: filters.subject }),
    }).toString();

    const response = await axiosInstance.get(`/questionBank?${queryParams}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    console.log("Get Question Bank Response:", response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch question bank: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//Get all Subject From Constant Table
export const getSubject = async (): Promise<any> => {
  try {
    const response = await axiosInstance.get("/constant/Subject");
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch subject data: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

//Get all Standard From Constant Table
export const getClassroom = async (): Promise<any> => {
  try {
    const response = await axiosInstance.get("/constant/classroom");
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch classroom Data: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const createQuestionBank = async (
  data: QuestionBankInput
): Promise<any> => {
  try {
    const response = await axiosInstance.post("/questionBank", data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const updateQuestionBank = async (
  id: string,
  data: QuestionBankInput
): Promise<any> => {
  try {
    const response = await axiosInstance.put(`/questionBank/${id}`, data, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};

export const deleteQuestionBank = async (id: string): Promise<any> => {
  try {
    const response = await axiosInstance.delete(`/questionBank/${id}`, {
      headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return { success: true, data: response.data };
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete question: ${
        error.response?.data?.message || error.message
      }`,
    };
  }
};
