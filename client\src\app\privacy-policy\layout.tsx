import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy | UEST',
  description:
    'Read our Privacy Policy to understand how we collect, use, and protect your personal information.',
  openGraph: {
    title: 'Privacy Policy | UEST',
    description: 'Learn about our commitment to your privacy and data protection.',
    url: '/privacy-policy',
  },
};

export default function PrivacyPolicyLayout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
