import express from "express";
import { createReviews, getReviewsByClassId, getAverageRating, deleteReviews, getReviews } from "../controllers/reviewsController";
import { studentAuthMiddleware } from "@/middlewares/studentAuth";
import { authMiddleware } from "@/middlewares/adminAuth";

const router = express.Router();

router.get('/', getReviews);
router.get('/class/:classId', getReviewsByClassId);
router.get('/average/:classId', getAverageRating);
router.post('/', studentAuthMiddleware,createReviews);
router.delete('/:id',studentAuthMiddleware, deleteReviews);
router.delete('/admin/:id',authMiddleware, deleteReviews);

export default router;