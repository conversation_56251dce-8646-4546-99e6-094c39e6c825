import { axiosInstance } from '@/lib/axios';
import { ExamInput } from '@/lib/types';

export const getExams = async (page: number = 1, limit: number = 10, applicantId?: string) => {
  try {
    const response = await axiosInstance.get(`/exams?page=${page}&limit=${limit}${applicantId ? `&applicantId=${applicantId}` : ''}`, {
      headers: {
        'Server-Select': 'uwhizServer',
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch Exam: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const getExamsById = async (examId: number, applicantId?: string) => {
  try {
    const response = await axiosInstance.get(
      `/exams/${examId}${applicantId ? `?applicantId=${applicantId}` : ''}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch Exam: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const createExam = async (data: ExamInput) => {
  try {
    const response = await axiosInstance.post('/exams', data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to create exam: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const updateExam = async (id: number, data: Partial<ExamInput>) => {
  try {
    const response = await axiosInstance.put(`/exams/${id}`, data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to update Exam: ${error.response?.data?.message || error.message}`,
    };
  }
};

export const deleteExam = async (id: number) => {
  try {
    const response = await axiosInstance.delete(`/exams/${id}`,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to delete Exam: ${error.response?.data?.message || error.message}`,
    };
  }
};
