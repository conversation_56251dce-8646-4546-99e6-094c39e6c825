'use client';

import { useState, useEffect } from 'react';
import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import {
  Shield,
  Book,
  UserCircle,
  Link2,
  Ban,
  Settings,
  ScrollText,
  RefreshCw,
} from 'lucide-react';
import { motion } from 'framer-motion';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

function TermsAndConditionsContent() {
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  return (
    <>
      <Header />
      <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-16 bg-background min-h-screen">
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <div className="flex justify-center mb-6">
            <Shield className="w-16 h-16 text-customOrange" />
          </div>
          <h1 className="text-4xl sm:text-5xl font-extrabold text-foreground tracking-tight">
            Terms & <span className="text-customOrange">Conditions</span>
          </h1>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            Please review our Terms and Conditions carefully to understand the rules for using our
            Service.
          </p>
        </motion.header>

        <motion.div
          className="space-y-8"
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
        >
          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <Book className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Introduction
              </h2>
            </div>
            <div className="space-y-4 text-muted-foreground leading-relaxed">
              <p>
                Your access to and use of the Service is conditioned on your acceptance of and
                compliance with these Terms. These Terms apply to all visitors, users, and others
                who access or use the Service.
              </p>
              <p>
                By accessing or using the Service, you agree to be bound by these Terms. If you
                disagree with any part of the terms, you may not access the Service.
              </p>
            </div>
          </motion.section>

          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <UserCircle className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Accounts
              </h2>
            </div>
            <div className="space-y-4 text-muted-foreground leading-relaxed">
              <p>
                When you create an account with us, you must provide us with information that is
                accurate, complete, and current at all times. Failure to do so constitutes a breach
                of the Terms, which may result in immediate termination of your account on our
                Service.
              </p>
              <p>
                You are responsible for safeguarding the password that you use to access the Service
                and for any activities or actions under your password, whether your password is with
                our Service or a third-party service.
              </p>
              <p>
                You agree not to disclose your password to any third party. You must notify us
                immediately upon becoming aware of any breach of security or unauthorized use of
                your account.
              </p>
            </div>
          </motion.section>

          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <Link2 className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Links to Other Websites
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              Our Service may contain links to third-party websites or services that are not owned
              or controlled by us.
            </p>
            <p className="text-muted-foreground leading-relaxed mt-3">
              We have no control over, and assume no responsibility for, the content, privacy
              policies, or practices of any third-party websites or services. You further
              acknowledge and agree that we shall not be responsible or liable, directly or
              indirectly, for any damage or loss caused or alleged to be caused by or in connection
              with the use of or reliance on any such content, goods, or services available on or
              through any such websites or services.
            </p>
            <p className="text-muted-foreground leading-relaxed mt-3">
              We strongly advise you to read the terms and conditions and privacy policies of any
              third-party websites or services that you visit.
            </p>
          </motion.section>

          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <Ban className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Termination
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              We may terminate or suspend access to our Service immediately, without prior notice or
              liability, for any reason whatsoever, including without limitation if you breach the
              Terms.
            </p>
            <p className="text-muted-foreground leading-relaxed mt-3">
              All provisions of the Terms which by their nature should survive termination shall
              survive termination, including, without limitation, ownership provisions, warranty
              disclaimers, indemnity, and limitations of liability.
            </p>
            <p className="text-muted-foreground leading-relaxed mt-3">
              Upon termination, your right to use the Service will immediately cease. If you wish to
              terminate your account, you may simply discontinue using the Service.
            </p>
          </motion.section>

          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <Settings className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                General Conditions
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>You agree not to reproduce, duplicate, copy, sell, resell, or exploit any portion of the Service, use of the Service, or access to the Service or any content on the website through which the service is provided, without express written permission from us.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>You will receive timely support (Monday to Friday, 10 AM to 6 PM) from our technical support team via call or WhatsApp.</div>
              </motion.li>
            </ul>
          </motion.section>

          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <ScrollText className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Cancellation and Termination of Services
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>Since we offer non-tangible, irrevocable goods, we do not issue refunds after the enrollment is made, which you acknowledge prior to purchasing the service.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>
                  You may cancel our services at any time by notifying us (via email at{' '}
                  <motion.a
                    href="mailto:<EMAIL>"
                    className="text-customOrange hover:text-foreground underline transition-colors"
                    aria-label="Email <NAME_EMAIL>"
                    whileHover={{ scale: 1.05 }}
                  >
                    <EMAIL>
                  </motion.a>
                  ) that you no longer wish to use our services or website.
                </div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>If you cancel your subscription before the current paid period ends, your cancellation will take effect immediately, and you will not be charged after that.</div>
              </motion.li>
            </ul>
          </motion.section>

          <motion.section
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
          >
            <div className="flex items-center gap-3 mb-6">
              <RefreshCw className="w-8 h-8 text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Updates & Customization
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <li>
                By using our service, you consent to our website’s update policy and app’s update
                policy, which are generally for future enhancements and bug fixes.
              </li>
              <li>
                This is a Software as a Service (SaaS) product, and future updates are free of cost.
                Your customization timeline will be decided mutually based on your upcoming
                requirements.
              </li>
              <li>
                However, if you want major customization or instant updates, you must choose our
                Enterprise Customization Plan, which will be based on a real-time discussion about
                modules, timelines, and additional charges.
              </li>
            </ul>
          </motion.section>
        </motion.div>
      </main>
      <Footer />

      {/* Scroll to top button */}
      {showScrollTop && (
        <motion.button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 p-3 rounded-full bg-customOrange text-white shadow-lg z-50"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 10l7-7m0 0l7 7m-7-7v18"
            />
          </svg>
        </motion.button>
      )}
    </>
  );
}

export default function TermsAndConditionsPage() {
  return <TermsAndConditionsContent />;
}
