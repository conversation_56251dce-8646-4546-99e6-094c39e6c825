'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Copy, Users, UserCheck, Calendar, ExternalLink, Share2, IndianRupee, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';
import { axiosInstance } from '@/lib/axios';
import { format } from 'date-fns';
import { ColumnDef } from '@tanstack/react-table';
import { DynamicTable } from '@/app-components/DynamicTable';

interface ReferralData {
  totalReferrals: number;
  studentsReferred: number;
  classesReferred: number;
  referralCode: string | null;
  links: {
    studentLink: string;
    classLink: string;
  } | null;
  history: Array<{
    id: string;
    referredUserName: string;
    referredUserEmail: string;
    referredUserType: 'STUDENT' | 'CLASS';
    createdAt: string;
    earnings?: Array<{
      id: string;
      earningType: 'REGISTRATION' | 'UWHIZ_APPLICATION';
      amount: number;
      paymentStatus: 'PAID' | 'UNPAID';
      examId?: string;
    }>;
  }>;
  earnings?: {
    totalEarnings: number;
    registrationEarnings: number;
    uwhizEarnings: number;
    paidEarnings: number;
    unpaidEarnings: number;
  };
}

// Define the type for history items
type ReferralHistoryItem = {
  id: string;
  referredUserName: string;
  referredUserEmail: string;
  referredUserType: 'STUDENT' | 'CLASS';
  createdAt: string;
  earnings?: Array<{
    id: string;
    earningType: 'REGISTRATION' | 'UWHIZ_APPLICATION';
    amount: number;
    paymentStatus: 'PAID' | 'UNPAID';
    examId?: string;
  }>;
};

export default function ReferralDashboard() {
  const [referralData, setReferralData] = useState<ReferralData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState({
    startDate: '',
    endDate: '',
  });

  // Column definitions for the referral history table
  const columns: ColumnDef<ReferralHistoryItem>[] = [
    {
      accessorKey: 'referredUserName',
      header: 'Name',
      cell: ({ row }) => (
        <span className="font-medium">{row.original.referredUserName}</span>
      ),
    },
    {
      accessorKey: 'referredUserEmail',
      header: 'Email',
      cell: ({ row }) => (
        <span className="text-gray-700">{row.original.referredUserEmail}</span>
      ),
    },
    {
      accessorKey: 'referredUserType',
      header: 'Type',
      cell: ({ row }) => (
        <Badge variant={row.original.referredUserType === 'STUDENT' ? 'default' : 'secondary'}>
          {row.original.referredUserType}
        </Badge>
      ),
    },
    {
      accessorKey: 'earnings',
      header: () => (
        <div className="flex items-center gap-1">
          <IndianRupee className="h-4 w-4" />
          Earnings Status
        </div>
      ),
      cell: ({ row }) => {
        const { earnings } = row.original;
        return earnings && earnings.length > 0 ? (
          <div className="space-y-1">
            {earnings.map((earning) => (
              <div key={earning.id} className="flex items-center justify-between bg-gray-50 p-2 rounded text-sm">
                <div>
                  <span className="font-medium">₹{earning.amount}</span>
                  <span className="text-gray-500 ml-1">
                    ({earning.earningType === 'REGISTRATION' ? 'Registration' : 'U-whiz'})
                  </span>
                </div>
                <Badge
                  variant={earning.paymentStatus === 'PAID' ? 'default' : 'secondary'}
                  className={earning.paymentStatus === 'PAID' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}
                >
                  {earning.paymentStatus === 'PAID' ? 'Paid' : 'Pending'}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <span className="text-gray-400 text-sm">No earnings yet</span>
        );
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Date',
      cell: ({ row }) => (
        <span className="text-gray-700">
          {format(new Date(row.original.createdAt), 'MMM dd, yyyy HH:mm')}
        </span>
      ),
    },
  ];



  const fetchReferralData = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get('/referral/dashboard');
      if (response.data.success) {
        setReferralData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching referral data:', error);
      toast.error('Failed to load referral data');
    } finally {
      setLoading(false);
    }
  };

  const generateReferralLink = async () => {
    try {
      const response = await axiosInstance.post('/referral/generate-link');
      if (response.data.success) {
        setReferralData(prev => prev ? {
          ...prev,
          referralCode: response.data.data.referralCode,
          links: response.data.data.links,
        } : null);
        toast.success('Referral links generated successfully!');
      }
    } catch (error) {
      console.error('Error generating referral link:', error);
      toast.error('Failed to generate referral links');
    }
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${type} link copied to clipboard!`);
  };

  const fetchHistory = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (dateFilter.startDate) params.append('startDate', dateFilter.startDate);
      if (dateFilter.endDate) params.append('endDate', dateFilter.endDate);

      const response = await axiosInstance.get(`/referral/history?${params.toString()}`);
      if (response.data.success) {
        setReferralData(prev => prev ? {
          ...prev,
          history: response.data.data.history,
        } : null);
      }
    } catch (error) {
      console.error('Error fetching history:', error);
      toast.error('Failed to load referral history');
    }
  }, [dateFilter.startDate, dateFilter.endDate]);

  useEffect(() => {
    fetchReferralData();
  }, []);

  useEffect(() => {
    if (dateFilter.startDate && dateFilter.endDate) {
      fetchHistory();
    }
  }, [dateFilter, fetchHistory]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Referral Dashboard</h1>
        <p className="text-gray-600">Track your referrals and earn rewards</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Referrals</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {referralData?.totalReferrals || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Students Referred</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {referralData?.studentsReferred || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Classes Referred</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {referralData?.classesReferred || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Earnings Cards */}
      {referralData?.earnings && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
              <IndianRupee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                ₹{referralData.earnings.totalEarnings}
              </div>
              <p className="text-xs text-muted-foreground">
                Registration + U-whiz
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Registration Earnings</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                ₹{referralData.earnings.registrationEarnings}
              </div>
              <p className="text-xs text-muted-foreground">
                ₹10 per student
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">U-whiz Earnings</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ₹{referralData.earnings.uwhizEarnings}
              </div>
              <p className="text-xs text-muted-foreground">
                ₹25 per application
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Payment Status</CardTitle>
              <IndianRupee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="text-sm">
                  <span className="text-green-600 font-semibold">Paid: ₹{referralData.earnings.paidEarnings}</span>
                </div>
                <div className="text-sm">
                  <span className="text-orange-600 font-semibold">Pending: ₹{referralData.earnings.unpaidEarnings}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Referral Links Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            My Referral Links
          </CardTitle>
          <CardDescription>
            Share these links to refer new students and classes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {referralData?.links ? (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium">Student Registration Link</label>
                <div className="flex gap-2">
                  <Input
                    value={referralData.links.studentLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(referralData.links!.studentLink, 'Student')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(referralData.links!.studentLink, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Classes Registration Link</label>
                <div className="flex gap-2">
                  <Input
                    value={referralData.links.classLink}
                    readOnly
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(referralData.links!.classLink, 'Classes')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(referralData.links!.classLink, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No referral links generated yet</p>
              <Button onClick={generateReferralLink} className="bg-orange-500 hover:bg-orange-600">
                Generate Referral Links
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* History Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Referral History
          </CardTitle>
          <CardDescription>
            Track all your successful referrals
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Date Filter */}
          <div className="flex gap-4 mb-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <Input
                type="date"
                value={dateFilter.startDate}
                onChange={(e) => setDateFilter(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <Input
                type="date"
                value={dateFilter.endDate}
                onChange={(e) => setDateFilter(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => setDateFilter({ startDate: '', endDate: '' })}
              >
                Clear Filter
              </Button>
            </div>
          </div>

          {/* History Table */}
          <DynamicTable
            columns={columns}
            data={referralData?.history || []}
            fetchData={() => Promise.resolve()}
            totalItems={referralData?.history.length || 0}
            isLoading={loading}
            hidePagination={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}
