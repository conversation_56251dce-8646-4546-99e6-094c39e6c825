import { Router } from 'express';
import {
  createThought,
  getAllThoughts,
  getThoughtById,
  updateThought,
  deleteThought,
  updateThoughtStatus
} from '../controllers/classesThoughtController';
import { authClientMiddleware } from '@/middlewares/clientAuth'
import { authMiddleware } from '@/middlewares/adminAuth';

const thoughtRouter = Router();

thoughtRouter.post('/', authClientMiddleware, createThought);
thoughtRouter.get('/', getAllThoughts); 
thoughtRouter.get('/:id',authClientMiddleware, getThoughtById);
thoughtRouter.put('/:id', authClientMiddleware, updateThought);
thoughtRouter.patch('/:id/status', updateThoughtStatus);
thoughtRouter.delete('/:id',authClientMiddleware, deleteThought);
thoughtRouter.delete('/admin/:id',authMiddleware, deleteThought);
 
export default thoughtRouter;