"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Loader, Clock } from "lucide-react";
import { toast } from "sonner";
import { getStudentDetail } from "@/services/studentDetailServiceApi";
import { GoogleLoginButton } from "@/components/ui/GoogleLoginButton";
import { uwhizMockQuestionForStudent } from "@/services/uwhizMockExamApi";
import { isAttemptAllowed, saveExamAttempt } from "./restictExamAttempt";
import Image from "next/image";
import examLogo from '../../../public/uwhizExam.png';

interface Question {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: string;
  subject: string;
  level: string;
  timePerQuestion: number;
  exam_name: string;
}

interface UserAnswer {
  questionId: string;
  selectedAnswer: string;
}

interface QuizHeaderProps {
  examName: string;
}

const QuizHeader: React.FC<QuizHeaderProps> = React.memo(
  ({ examName }) => {
    return (
      <header className="fixed top-0 left-0 right-0 z-20 py-2 px-4 sm:px-6 sm:py-3 flex flex-col sm:flex-row items-center justify-between bg-black text-white shadow-md">
        <div className="flex items-center justify-center gap-3">
          <Image
            height={60} 
            width={60}
            src={examLogo.src}
            alt="Uwhiz Logo"
            quality={100}
            className="object-contain sm:h-20 sm:w-20"
          />
          <h1 className="text-lg sm:text-2xl font-bold tracking-tight">{examName.toUpperCase()}</h1>
        </div>
      </header>
    );
  }
);
QuizHeader.displayName = "QuizHeader";

export default function QuizPage() {
  const router = useRouter();

  // State for dialogs and student profile
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [studentId, setStudentId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [tabSwitchCount, setTabSwitchCount] = useState(0);
  const [inputViolationCount, setInputViolationCount] = useState(0);
  const [focusViolationCount, setFocusViolationCount] = useState(0);
  const [showWarning, setShowWarning] = useState(false);
  const [showTermination, setShowTermination] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState<number>(30);
  const [userAnswers, setUserAnswers] = useState<UserAnswer[]>([]);
  const [isQuizCompleted, setIsQuizCompleted] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isAnswerSubmitted, setIsAnswerSubmitted] = useState(false);
  const [score, setScore] = useState<number | null>(null);
  const [playSound, setPlaySound] = useState(false);

  // Check if user is allowed to attempt the exam
  useEffect(() => {
    if (studentId) {
      const { allowed, remainingHours } = isAttemptAllowed(studentId);
      if (!allowed && remainingHours) {
        toast.error(`You can attempt the exam again after ${remainingHours} hours.`);
        router.push("/uwhiz");
        return;
      }
    }
  }, [router, studentId]);

  // Fetch student ID and profile
  useEffect(() => {
    const fetchStudentData = async () => {
      let fetchedStudentId: string | null = null;
      try {
        const data = localStorage.getItem("student_data");
        fetchedStudentId = data ? JSON.parse(data).id : null;
        setStudentId(fetchedStudentId);
      } catch (error) {
        console.error("Error retrieving studentId:", error);
        setStudentId(null);
      }

      if (!fetchedStudentId) {
        setIsLoginDialogOpen(true);
        setIsLoading(false);
        return;
      }

      try {
        const response = await getStudentDetail(fetchedStudentId);
        const { classroom, medium } = response.data;

        if (!classroom || !medium) {
          setIsProfileDialogOpen(true);
          setIsLoading(false);
          return;
        }

        try {
          const apiQuestions = await uwhizMockQuestionForStudent(fetchedStudentId, medium, classroom);
          const formattedQuestions: Question[] = apiQuestions.map((q: any) => ({
            id: q.id,
            question: q.question,
            optionOne: q.optionOne,
            optionTwo: q.optionTwo,
            optionThree: q.optionThree,
            optionFour: q.optionFour,
            correctAnswer: q.correctAnswer,
            subject: q.subject,
            level: q.level,
            timePerQuestion: q.level === "HARD" ? 60 : q.level === "MEDIUM" ? 45 : 30,
            exam_name: "Uwhiz - Mock Exam",
          }));
          setQuestions(formattedQuestions);
          setTimeLeft(formattedQuestions[0]?.timePerQuestion || 30);
          setIsDialogOpen(true);
        } catch (error) {
          console.error("Error fetching questions:", error);
          toast.error("Failed to load questions. Please try again.");
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
        setIsProfileDialogOpen(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStudentData();
  }, [router]);

  // Enter full-screen mode
  const enterFullScreen = () => {
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen().catch((err) => console.error("Failed to enter fullscreen:", err));
    }
  };

  // Handle start quiz
  const handleStartQuiz = () => {
    setIsDialogOpen(false);
    enterFullScreen();
    if (questions.length > 0) {
      setTimeLeft(questions[currentQuestionIndex]?.timePerQuestion || 30);
    }
  };

  // Handle Google login
  const handleGoToLogin = () => {
    setIsLoginDialogOpen(false);
    try {
      const data = localStorage.getItem("student_data");
      if (data) {
        const fetchedStudentId = JSON.parse(data).id;
        setStudentId(fetchedStudentId);
      }
    } catch (error) {
      console.error("Error parsing student_data after login:", error);
      setIsLoginDialogOpen(true);
    }
  };

  // Handle profile completion
  const handleGoToProfile = () => {
    setIsProfileDialogOpen(false);
    router.push("/student/profile?quiz=true");
  };

  // Handle keyboard restrictions
  const handleKeyDown = useCallback(
    async (event: KeyboardEvent) => {
      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen) return;

      const restrictedKeys = ["Alt", "Control", "Tab", "Shift", "Enter"];
      const functionKeys = [
        "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12",
      ];
      const isDevToolsShortcut =
        (event.ctrlKey && event.shiftKey && (event.key === "I" || event.key === "J" || event.key === "C")) ||
        (event.metaKey && event.altKey && event.key === "I") ||
        event.key === "F12";
      const isCopyShortcut =
        (event.ctrlKey || event.metaKey) && (event.key === "c" || event.key === "C");

      if (
        restrictedKeys.includes(event.key) ||
        functionKeys.includes(event.key) ||
        isDevToolsShortcut ||
        isCopyShortcut
      ) {
        event.preventDefault();
        if (isCopyShortcut) {
          toast.warning("Copying is disabled during the quiz.");
          return;
        }
        setInputViolationCount((prev) => prev + 1);
        const violationType = isDevToolsShortcut
          ? "DevTools shortcut"
          : functionKeys.includes(event.key)
            ? `Function key "${event.key}"`
            : `Restricted key "${event.key}"`;
        if (inputViolationCount === 0) {
          setShowWarning(true);
          toast.warning(`${violationType} detected. Using restricted actions again will terminate the quiz.`);
        } else if (inputViolationCount === 1) {
          setShowWarning(true);
          toast.warning(`${violationType} detected. Using restricted actions again will terminate the quiz.`);
        } else if (inputViolationCount === 2) {
          setShowTermination(true);
          toast.error("Quiz terminated due to multiple restricted actions.");
        }
      }
    },
    [inputViolationCount, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen]
  );

  // Handle right-click restriction
  const handleContextMenu = useCallback(
    async (event: MouseEvent) => {
      if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen) return;
      event.preventDefault();
      toast.warning("Right-click is disabled during the quiz.");
    },
    [isDialogOpen, isLoginDialogOpen, isProfileDialogOpen]
  );

  // Handle tab switch
  const handleVisibilityChange = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen) return;
    if (document.hidden) {
      setTabSwitchCount((prev) => prev + 1);
      if (tabSwitchCount === 0) {
        setShowWarning(true);
        toast.warning("Tab switch detected.");
      } else if (tabSwitchCount === 1) {
        setShowWarning(true);
        toast.warning("Again Tab switch detected. Switching tabs again will terminate the quiz.");
      } else if (tabSwitchCount === 2) {
        setShowTermination(true);
        toast.error("Quiz terminated due to multiple tab switches.");
      }
    }
  }, [tabSwitchCount, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen]);

  // Handle window blur
  const handleWindowBlur = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen) return;
    setFocusViolationCount((prev) => prev + 1);
    if (focusViolationCount === 0) {
      setShowWarning(true);
      toast.warning("Window focus lost. Losing focus again will terminate the quiz.");
    } else if (focusViolationCount === 1) {
      setShowWarning(true);
      toast.warning("Window focus lost. Losing focus again will terminate the quiz.");
    } else if (focusViolationCount === 2) {
      setShowTermination(true);
      toast.error("Quiz terminated due to multiple focus violations.");
    }
  }, [focusViolationCount, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen]);

  // Handle full-screen exit
  const handleFullScreenChange = useCallback(async () => {
    if (isDialogOpen || isLoginDialogOpen || isProfileDialogOpen) return;
    if (!document.fullscreenElement) {
      setFocusViolationCount((prev) => prev + 1);
      if (focusViolationCount === 0) {
        setShowWarning(true);
        toast.warning("You have exited full-screen mode.");
        enterFullScreen();
      } else if (focusViolationCount === 1) {
        setShowWarning(true);
        toast.warning("Again you have exited full-screen mode. Exiting again will terminate the quiz.");
        enterFullScreen();
      } else if (focusViolationCount === 2) {
        setShowTermination(true);
        toast.error("Quiz terminated due to multiple full-screen exits.");
      }
    }
  }, [focusViolationCount, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen]);

  // Handle navigation to home
  const handleGoHome = async () => {
    setShowTermination(false);
    const isFullScreen = document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement;
    if (isFullScreen) {
      try {
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).mozCancelFullScreen) {
          await (document as any).mozCancelFullScreen();
        }
        router.push("/uwhiz");
      } catch (err: any) {
        toast.warning("Could not exit full-screen mode automatically. Please press Esc to exit manually.", err);
        router.push("/uwhiz");
      }
    } else {
      router.push("/uwhiz");
    }
  };

  // Event listeners
  useEffect(() => {
    if (!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen) {
      document.addEventListener("visibilitychange", handleVisibilityChange);
      document.addEventListener("keydown", handleKeyDown);
      document.addEventListener("contextmenu", handleContextMenu);
      window.addEventListener("blur", handleWindowBlur);
      document.addEventListener("fullscreenchange", handleFullScreenChange);
    }
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("contextmenu", handleContextMenu);
      window.removeEventListener("blur", handleWindowBlur);
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, [
    handleVisibilityChange,
    handleKeyDown,
    handleContextMenu,
    handleWindowBlur,
    handleFullScreenChange,
    isDialogOpen,
    isLoginDialogOpen,
    isProfileDialogOpen,
  ]);

  // Format time
  const formatTime = useCallback((seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }, []);

  // Exam name
  const examName = useMemo(() => {
    return questions.length > 0 ? questions[0].exam_name : "Uwhiz - Mock Exam";
  }, [questions]);

  // Progress
  const progress = useMemo(() => {
    return questions.length > 0
      ? ((currentQuestionIndex + 1) / questions.length) * 100
      : 0;
  }, [currentQuestionIndex, questions]);

  // Timer logic
  useEffect(() => {
    if (
      questions.length > 0 &&
      timeLeft > 0 &&
      !isDialogOpen &&
      !isLoginDialogOpen &&
      !isProfileDialogOpen &&
      !showTermination
    ) {
      const startTime = Date.now();
      const initialTime = timeLeft;
      const timer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        const newTime = initialTime - elapsed;
        setTimeLeft(() => {
          const updatedTime = newTime <= 0 ? 0 : newTime;
          if (updatedTime <= 5 && updatedTime > 0) {
            setPlaySound(true);
          } else {
            setPlaySound(false);
          }
          if (updatedTime <= 0) {
            handleNextQuestion();
            clearInterval(timer);
            return 0;
          }
          return updatedTime;
        });
      }, 1000);
      return () => {
        clearInterval(timer);
      };
    }
  }, [
    questions,
    currentQuestionIndex,
    isDialogOpen,
    isLoginDialogOpen,
    isProfileDialogOpen,
    showTermination,
  ]);

  // Reset timer
  useEffect(() => {
    if (
      questions.length > 0 &&
      !isDialogOpen &&
      !isLoginDialogOpen &&
      !isProfileDialogOpen &&
      !showTermination
    ) {
      const newTime = questions[currentQuestionIndex]?.timePerQuestion || 30;
      setTimeLeft(newTime);
      setPlaySound(false);
    }
  }, [currentQuestionIndex, questions, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showTermination]);

  //play Audio calculate remaing time
  useEffect(() => {
    if (questions.length > 0 && timeLeft > 0 && !isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && !showTermination) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          const newTime = prev - 1;
          if (newTime <= 5 && newTime > 0) {
            setPlaySound(true);
          } else {
            setPlaySound(false);
          }
          if (newTime <= 0) {
            handleNextQuestion();
            clearInterval(timer);
            return 0;
          }
          return newTime;
        });
      }, 1000);
      return () => {
        clearInterval(timer);
      };
    }
  }, [timeLeft, questions, currentQuestionIndex, isDialogOpen, isLoginDialogOpen, isProfileDialogOpen, showTermination]);

  // Add this effect to play sound when playSound is true
  useEffect(() => {
    if (playSound) {
      const audio = new Audio('/clock-ticking-sound-effect.mp3');
      audio.loop = true;
      audio.play().catch((error) => console.error("Error playing sound:", error));
      return () => {
        audio.pause();
        audio.currentTime = 0;
      };
    }
  }, [playSound]);

  // Handle answer selection
  const selectAnswer = (optionKey: string) => {
    setSelectedAnswer(optionKey);
    setIsAnswerSubmitted(true);
    setUserAnswers((prev) => {
      const existingAnswer = prev.find(
        (ans) => ans.questionId === questions[currentQuestionIndex].id
      );
      if (existingAnswer) {
        return prev.map((ans) =>
          ans.questionId === questions[currentQuestionIndex].id
            ? { ...ans, selectedAnswer: optionKey }
            : ans
        );
      }
      return [
        ...prev,
        { questionId: questions[currentQuestionIndex].id, selectedAnswer: optionKey },
      ];
    });
  };

  // Calculate score
  const calculateScore = useCallback(() => {
    let correctCount = 0;
    userAnswers.forEach((answer) => {
      const question = questions.find((q) => q.id === answer.questionId);
      if (question && answer.selectedAnswer === question.correctAnswer) {
        correctCount += 1;
      }
    });
    setScore(correctCount);
  }, [userAnswers, questions]);

  // Handle next question
  const handleNextQuestion = useCallback(() => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setIsAnswerSubmitted(false);
    } else {
      calculateScore();
      setIsQuizCompleted(true);
      if (studentId) {
        saveExamAttempt(studentId);
      }
    }
  }, [currentQuestionIndex, questions, calculateScore, studentId]);

  // Save attempt
  useEffect(() => {
    if (isQuizCompleted && studentId) {
      saveExamAttempt(studentId);
    }
  }, [isQuizCompleted, studentId]);


  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <p className="text-lg sm:text-xl font-medium mr-4">Loading...</p>
        <Loader className="w-6 h-6 sm:w-8 sm:h-8 animate-spin text-customOrange" />
      </div>
    );
  }

  if (questions.length === 0 && !isLoginDialogOpen && !isProfileDialogOpen) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <p className="text-lg sm:text-xl font-medium mr-4">Loading questions...</p>
        <Loader className="w-6 h-6 sm:w-8 sm:h-8 animate-spin text-customOrange" />
      </div>
    );
  }

  if (isQuizCompleted) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100 text-gray-900">
        <div className="text-center p-6 bg-white rounded-lg shadow-xl max-w-md w-full">
          <h1 className="text-3xl sm:text-4xl font-bold text-customOrange mb-4">Quiz Completed!</h1>
          <p className="text-lg sm:text-xl mb-4">Your responses have been recorded.</p>
          <p className="text-lg sm:text-xl font-semibold mb-6">
            Your Score: {score} / {questions.length}
          </p>
          <Button
            className="bg-customOrange text-white px-6 py-3 rounded-full hover:bg-customOrange text-base sm:text-lg transition-all"
            onClick={handleGoHome}
          >
            Go To Home
          </Button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];

  // Button styles
  const getButtonClass = (optionKey: string) => {
    if (!isAnswerSubmitted) {
      return `w-full font-medium rounded-lg py-4 sm:py-5 text-base sm:text-lg text-gray-700 hover:bg-orange-100 hover:border-orange-500 transition-all duration-200 flex items-center justify-start gap-4 px-4 sm:px-6 shadow-sm border border-gray-200 bg-white ${selectedAnswer === optionKey ? "bg-orange-100 border-orange-500" : ""
        }`;
    }
    if (selectedAnswer === optionKey) {
      return `w-full font-medium rounded-lg py-4 sm:py-5 text-base sm:text-lg flex items-center justify-start gap-4 px-4 sm:px-6 shadow-sm border ${selectedAnswer === currentQuestion.correctAnswer
          ? "bg-green-100 border-green-500 text-green-700"
          : "bg-red-100 border-red-500 text-red-700"
        }`;
    }
    if (optionKey === currentQuestion.correctAnswer) {
      return "w-full font-medium rounded-lg py-4 sm:py-5 text-base sm:text-lg bg-green-100 border-green-500 text-green-700 flex items-center justify-start gap-4 px-4 sm:px-6 shadow-sm border";
    }
    return "w-full font-medium rounded-lg py-4 sm:py-5 text-base sm:text-lg text-gray-700 flex items-center justify-start gap-4 px-4 sm:px-6 shadow-sm border border-gray-200 bg-white";
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-100 text-gray-900">
      {/* Login dialog */}
      {isLoginDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-xl sm:text-2xl font-bold mb-4">Login Required</h2>
            <p className="mb-6 text-sm sm:text-base text-gray-600">
              Please log in as a student to access the quiz.
            </p>
            <GoogleLoginButton uwhiz={true} handleApplyNow={handleGoToLogin} />
          </div>
        </div>
      )}

      {/* Profile dialog */}
      {isProfileDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-xl sm:text-2xl font-bold mb-4">Complete Your Profile</h2>
            <p className="mb-6 text-sm sm:text-base text-gray-600">
              Your profile is incomplete. Please complete your profile to proceed.
            </p>
            <Button
              onClick={handleGoToProfile}
              className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange w-full text-sm sm:text-base transition-all"
            >
              Complete Profile
            </Button>
          </div>
        </div>
      )}

      {/* Start quiz dialog */}
      {isDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-3/4 md:w-1/2 max-h-[80vh] flex flex-col">
            <h2 className="text-xl sm:text-2xl font-bold mb-4">Start Quiz</h2>
            <p className="font-semibold mb-4 text-sm sm:text-base text-gray-600">
              Note: This is a mock exam for testing purposes only. Marks will be calculated at the end.
            </p>
            <div className="flex-1 overflow-y-auto pr-2 mb-6 text-sm sm:text-base">
              <p className="font-semibold mb-2">Instructions (English):</p>
              <ul className="list-disc list-inside mb-4 text-gray-600">
                <li>Do not switch tabs during the quiz.</li>
                <li>Do not use restricted keys (Alt, Ctrl, Tab, Shift, Enter, Function keys).</li>
                <li>Do not open Developer Tools.</li>
                <li>Do not right-click during the quiz.</li>
                <li>Do not exit full-screen mode.</li>
                <li>Do not interact with other windows or applications.</li>
                <li>Do not change the screen or minimize the quiz window.</li>
                <li>Do not receive or make calls during the quiz.</li>
                <li>Do not use split screen or floating windows on your device.</li>
              </ul>
              <p className="font-semibold mb-2">સૂચનાઓ (ગુજરાતી):</p>
              <ul className="list-disc list-inside text-gray-600">
                <li>ક્વિઝ દરમિયાન ટેબ બદલશો નહીં.</li>
                <li>પ્રતિબંધિત કીઓ (ઓલ્ટ, કંટ્રોલ, ટેબ, શિફ્ટ, એન્ટર, ફંક્શન કીઓ) નો ઉપયોગ કરશો નહીં.</li>
                <li>ડેવલપર ટૂલ્સ ખોલશો નહીં.</li>
                <li>ક્વિઝ દરમિયાન જમણું-ક્લિક કરશો નહીં.</li>
                <li>ફુલ-સ્ક્રીન મોડમાંથી બહાર નીકળશો નહીં.</li>
                <li>અન્ય વિન્ડોઝ અથવા એપ્લિકેશન્સ સાથે સંપર્ક કરશો નહીં.</li>
                <li>સ્ક્રીન બદલશો નહીં અથવા ક્વિઝ વિન્ડો નાની કરશો નહીં.</li>
                <li>ક્વિઝ દરમિયાન કૉલ રિસીવ કરશો નહીં અથવા કૉલ કરશો નહીં.</li>
                <li>તમારા ડિવાઇસ પર સ્પ્લિટ સ્ક્રીન અથવા ફ્લોટિંગ વિન્ડોઝનો ઉપયોગ કરશો નહીં.</li>
              </ul>
            </div>
            <Button
              onClick={handleStartQuiz}
              className="bg-customOrange text-white px-4 py-2 rounded-full hover:bg-customOrange text-sm sm:text-base w-full transition-all"
            >
              Start Quiz
            </Button>
          </div>
        </div>
      )}

      {/* Warning dialog */}
      {showWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-xl sm:text-2xl font-bold mb-4 text-customOrange">Warning</h2>
            <p className="mb-6 text-sm sm:text-base text-gray-600">
              You have performed a restricted action. Repeating this will terminate the quiz.
            </p>
            <Button
              onClick={() => setShowWarning(false)}
              className="bg-blue-500 text-white px-4 py-2 rounded-full hover:bg-blue-600 text-sm sm:text-base w-full transition-all"
            >
              OK
            </Button>
          </div>
        </div>
      )}

      {/* Termination dialog */}
      {showTermination && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-11/12 sm:w-96">
            <h2 className="text-xl sm:text-2xl font-bold mb-4 text-red-500">Quiz Terminated</h2>
            <p className="mb-6 text-sm sm:text-base text-gray-600">
              Your quiz has been terminated due to multiple restricted actions.
            </p>
            <Button
              onClick={handleGoHome}
              className="bg-red-500 text-white px-4 py-2 rounded-full hover:bg-red-600 text-sm sm:text-base w-full transition-all"
            >
              Go to Home
            </Button>
          </div>
        </div>
      )}

      {!isDialogOpen && !isLoginDialogOpen && !isProfileDialogOpen && (
        <>
          <QuizHeader examName={examName} />
          {/* Progress Bar */}
          <div className="fixed top-[60px] sm:top-[80px] left-0 right-0 z-10 w-full h-1.5 bg-gray-200">
            <div
              className="h-1.5 bg-customOrange rounded-r-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pt-[80px] sm:pt-[100px] pb-[48px] sm:pb-[64px] min-h-screen">
            {/* Main Content Wrapper */}
            <div className="flex flex-col items-center justify-center w-full max-w-3xl">
              {/* Timer above question */}
              <div className="mt-2 sm:mt-4 mb-4 sm:mb-6 flex items-center gap-2 bg-gray-800/80 px-4 sm:px-6 py-2 rounded-full shadow-lg">
                <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-customOrange animate-pulse" />
                <span className="text-lg sm:text-2xl font-bold text-customOrange">
                  {formatTime(timeLeft)}
                </span>
              </div>
              {/* Question Card */}
              <div className="w-full text-center flex flex-col items-center">
                <div className="flex justify-center mb-3 sm:mb-4">
                  <span className="text-xs sm:text-base font-semibold text-customOrange bg-orange-100 px-2 sm:px-3 py-1 rounded-full shadow-sm">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </span>
                </div>
                <div className="bg-white p-4 sm:p-8 rounded-lg shadow-xl mb-4 sm:mb-6 w-full max-h-[60vh] sm:max-h-[70vh] overflow-y-auto">
                  <h2 className="text-lg sm:text-2xl md:text-3xl font-bold text-gray-800 mb-4 sm:mb-6">
                    {currentQuestion.question}
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <Button
                      variant="outline"
                      className={getButtonClass("optionOne")}
                      onClick={() => selectAnswer("optionOne")}
                      disabled={showTermination || isAnswerSubmitted}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold">
                        A
                      </span>
                      {currentQuestion.optionOne}
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionTwo")}
                      onClick={() => selectAnswer("optionTwo")}
                      disabled={showTermination || isAnswerSubmitted}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold">
                        B
                      </span>
                      {currentQuestion.optionTwo}
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionThree")}
                      onClick={() => selectAnswer("optionThree")}
                      disabled={showTermination || isAnswerSubmitted}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold">
                        C
                      </span>
                      {currentQuestion.optionThree}
                    </Button>
                    <Button
                      variant="outline"
                      className={getButtonClass("optionFour")}
                      onClick={() => selectAnswer("optionFour")}
                      disabled={showTermination || isAnswerSubmitted}
                    >
                      <span className="w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-gray-200 text-gray-600 font-semibold">
                        D
                      </span>
                      {currentQuestion.optionFour}
                    </Button>
                  </div>
                </div>
                {/* Next Button */}
                <Button
                  className="bg-customOrange text-white px-6 sm:px-8 py-2 sm:py-3 rounded-full hover:bg-customOrange text-sm sm:text-lg font-semibold shadow-lg transform hover:scale-105 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={handleNextQuestion}
                  disabled={showTermination || !isAnswerSubmitted}
                >
                  {currentQuestionIndex === questions.length - 1 ? "Finish" : "Next Question"}
                </Button>
              </div>
            </div>
            {/* Footer */}
            <footer className="fixed bottom-0 left-0 right-0 bg-black text-white py-2 px-4 sm:px-6 flex items-center justify-center gap-1.5 sm:gap-2 text-xs sm:text-base">
              <span>Sponsored by</span>
              <Image
                src="/nalanda.png"
                alt="Nalanda Logo"
                height={20}
                width={20}
                className="object-contain sm:h-6 sm:w-6"
              />
              <span className="font-semibold">Nalanda Vidhyalay</span>
            </footer>
          </div>
        </>
      )}
    </div>
  );
}