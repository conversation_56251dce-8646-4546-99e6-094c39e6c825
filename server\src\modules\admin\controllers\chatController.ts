import { Request, Response } from "express";
import {
    getAllClasses,
    getAllStudents,
    getConversationBetweenUsers,
    getStudentsChattedWithClass,
    getStudentsChattedWithClassPaginated,
    PaginationParams
} from "../services/chatServices";

export const getClasses = async (req: Request, res: Response): Promise<void> => {
    try {
        const { page, limit, search } = req.query;

        if (page && limit) {
            const paginationParams: PaginationParams = {
                page: parseInt(page as string, 10) || 1,
                limit: parseInt(limit as string, 10) || 10,
                search: search as string
            };

            const result = await getAllClasses(paginationParams);
            res.json({
                success: true,
                ...result
            });
        } else {
            const classes = await getAllClasses();
            res.json({
                success: true,
                data: classes
            });
        }
    } catch (error) {
        console.error("Error fetching classes:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch classes"
        });
    }
};

export const getStudents = async (req: Request, res: Response): Promise<void> => {
    try {
        const { page, limit, search } = req.query;

        if (page && limit) {
            const paginationParams: PaginationParams = {
                page: parseInt(page as string, 10) || 1,
                limit: parseInt(limit as string, 10) || 10,
                search: search as string
            };

            const result = await getAllStudents(paginationParams);
            res.json({
                success: true,
                ...result
            });
        } else {
            const students = await getAllStudents();
            res.json({
                success: true,
                data: students
            });
        }
    } catch (error) {
        console.error("Error fetching students:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch students"
        });
    }
};

export const getConversationBetween = async (req: Request, res: Response): Promise<void> => {
    try {
        const { classId, studentId } = req.params;
        const conversation = await getConversationBetweenUsers(classId, studentId);
        res.json({
            success: true,
            data: conversation
        });
    } catch (error) {
        console.error("Error fetching conversation:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch conversation"
        });
    }
};

export const getStudentsForClass = async (req: Request, res: Response): Promise<void> => {
    try {
        const { classId } = req.params;
        const { page, limit, search } = req.query;

        const classDetails = await getAllClasses();
        const selectedClass = Array.isArray(classDetails)
            ? classDetails.find((c: any) => c.id === classId)
            : classDetails.data?.find((c: any) => c.id === classId);

        if (!selectedClass) {
            res.status(404).json({
                success: false,
                error: "Class not found"
            });
            return;
        }

        if (page && limit) {
            const paginationParams: PaginationParams = {
                page: parseInt(page as string, 10) || 1,
                limit: parseInt(limit as string, 10) || 10,
                search: search as string
            };

            const result = await getStudentsChattedWithClassPaginated(classId, paginationParams);
            res.json({
                success: true,
                ...result
            });
        } else {
            const studentNames = await getStudentsChattedWithClass(classId);
            const allStudents = await getAllStudents();
            const chattedStudents = Array.isArray(allStudents)
                ? allStudents.filter((student: any) => {
                    const fullName = `${student.firstName} ${student.lastName}`;
                    return studentNames.includes(fullName);
                })
                : [];

            res.json({
                success: true,
                data: chattedStudents
            });
        }
    } catch (error) {
        console.error("Error fetching students for class:", error);
        res.status(500).json({
            success: false,
            error: "Failed to fetch students for class"
        });
    }
};
