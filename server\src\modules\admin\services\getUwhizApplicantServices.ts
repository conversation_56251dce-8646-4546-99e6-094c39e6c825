import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const getUserDetails = async (examType:any, applicantIds:any) => {
  let users : any = [];

  if (examType === 'CLASSES') {
    users = await prisma.classes.findMany({
      where: { id: { in: applicantIds } },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        createdAt: true,
      },
    });
  } else if (examType === 'STUDENTS') {
    users = await prisma.student.findMany({
      where: { id: { in: applicantIds } },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
        contact:true,
        createdAt: true,
      },
    });
  }

  return users;
};
