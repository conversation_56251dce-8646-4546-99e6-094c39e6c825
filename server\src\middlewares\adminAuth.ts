import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { sendError } from '@/utils/response';

export async function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<any> {
  const token = req.cookies.admin_jwt;

  if (!token) {
    return sendError(res, 'Unauthorized: No token provided', 401);
  }

  try {
    jwt.verify(token, process.env.JWT_SECRET || 'secret123');
    next();
  } catch (err) {
    console.log(err);
    return sendError(res, 'Unauthorized: Invalid token', 401);
  }
}
