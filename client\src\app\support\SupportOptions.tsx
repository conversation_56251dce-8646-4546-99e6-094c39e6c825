'use client';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { MdEmail, MdPhone, MdSupportAgent } from 'react-icons/md';

const SupportOptions = () => {
  const supportCards = [
    {
      icon: <MdEmail className="text-4xl" />,
      title: 'Email Support',
      description:
        'If you prefer to send an email, our support team is available 24/7 to respond to your questions.',
      action: 'Write us :',
      contact: '<EMAIL>',
      isLink: false,
    },
    {
      icon: <MdPhone className="text-4xl" />,
      title: 'On-Call Support',
      description:
        'If you need immediate assistance, our support team is available 24/7 to answer any questions you may have.',
      action: 'Call us :',
      contact: '+91 96 877 877 88',
      isLink: false,
    },
    {
      icon: <MdSupportAgent className="text-4xl" />,
      title: 'Ask Expert',
      description: 'If you have specific request, submit a support form, our expert will call you.',
      action: 'Contact :',
      contact: 'Link',
      isLink: true,
    },
  ];

  return (
    <section className="py-20 px-4  bg-white dark:bg-black ">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-blue-50 dark:bg-blue-900/30 text-customOrange px-4 py-2 rounded-full text-sm font-medium inline-block mb-4"
          >
            Help & Support
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl md:text-4xl font-bold bg-gradient-to-r bg-clip-text"
          >
            Here, You&apos;ll Find The Answers To Your Questions
          </motion.h2>
        </motion.div>

        {/* Cards Container */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {supportCards.map((card, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.2 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
              className="rounded-2xl p-6 shadow-lg border border-gray-100 dark:border-gray-700 relative overflow-hidden group"
            >
              {/* Decorative Element */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-customOrange/10 dark:bg-customOrange/5 rounded-bl-full transform translate-x-10 -translate-y-10" />
              
              {/* Icon */}
              <motion.div
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.2 + 0.3 }}
                className="bg-customOrange/10 dark:bg-customOrange/20 text-customOrange p-4 rounded-2xl inline-block mb-4"
              >
                {card.icon}
              </motion.div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                {card.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{card.description}</p>

              {/* Contact Info */}
              <div className="flex items-center space-x-2 text-sm">
                <span className="text-gray-500 dark:text-gray-400">{card.action}</span>
                {card.isLink ? (
                  <Link 
                    href="#" 
                    className="text-customOrange hover:text-[#e07d3e] font-medium transition-colors"
                  >
                    {card.contact}
                  </Link>
                ) : (
                  <span className="text-gray-900 dark:text-gray-100 font-medium">
                    {card.contact}
                  </span>
                )}
              </div>

              {/* Hover Effect Border */}
              <motion.div 
                className="absolute bottom-0 left-0 h-1 bg-customOrange transform origin-left"
                initial={{ scaleX: 0 }}
                whileHover={{ scaleX: 1 }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SupportOptions;
