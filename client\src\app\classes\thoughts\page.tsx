"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader2, Pencil, Plus, Trash2 } from "lucide-react";
import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from '@/lib/useAuth';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { getThought, createThought, updateThought, deleteThought } from '@/services/classesThoughtApi';
import { truncateThought } from '@/lib/utils';
import { DynamicTable } from '@/app-components/DynamicTable';
import { ColumnDef } from "@tanstack/react-table";
import {ThoughtTableMeta, Thought, ThoughtFormValues} from "@/lib/types"

const thoughtFormSchema = z.object({
  thoughts: z
    .string()
    .min(50, "Thought must be at least 50 characters")
    .max(180, "Thought cannot exceed 180 characters"),
  classId: z.string().min(1, "Class ID is required"),
});

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Define columns for the table
const columns: ColumnDef<Thought>[] = [
  {
    accessorKey: "thoughts",
    header: "Thought",
    cell: ({ row }) => (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="cursor-pointer">{truncateThought(row.getValue("thoughts") as string)}</span>
          </TooltipTrigger>
          <TooltipContent
            className="max-w-[300px] p-3 dark:bg-white dark:text-gray-800  bg-gray-800 text-white rounded-lg shadow-md border border-gray-600 text-sm leading-relaxed whitespace-normal break-words"
            side="bottom"
          >
            <p>{row.getValue("thoughts")}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    ),
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    cell: ({ row }) => formatDateTime(row.getValue("createdAt") as string),
  },
  {
    id: "actions",
    header: () => <div className="text-right">Actions</div>,
    cell: ({ row, table }) => {
      const thought = row.original;
      const meta = table.options.meta as ThoughtTableMeta | undefined;
      return (
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => meta?.onEdit?.(thought)}
            aria-label={`Edit thought: ${truncateThought(thought.thoughts)}`}
            disabled={!meta?.onEdit}
          >
            <Pencil className="h-4 w-4 dark:text-white" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="text-red-500 hover:bg-red-50"
            onClick={() => meta?.onDelete?.(thought.id)}
            disabled={meta?.deletingId === thought.id || !meta?.onDelete}
            aria-label={`Delete thought: ${truncateThought(thought.thoughts)}`}
          >
            {meta?.deletingId === thought.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      );
    },
  },
];

const ThoughtsPage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [thoughts, setThoughts] = useState<Thought[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingThought, setEditingThought] = useState<Thought | null>(null);
  const [deletingThoughtId, setDeletingThoughtId] = useState<string | null>(null);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const router = useRouter();
  const { user } = useAuth();
  const userData = useSelector((state: RootState) => state.user.user);
  const effectiveClassId = userData?.id?.toString() || '';

  const form = useForm<ThoughtFormValues>({
    resolver: zodResolver(thoughtFormSchema),
    defaultValues: {
      thoughts: "",
      classId: effectiveClassId,
    },
  });

  useEffect(() => {
    form.reset({ thoughts: "", classId: effectiveClassId });
  }, [effectiveClassId, form]);

  const fetchThoughts = useCallback(async (pageIndex: number) => {
    if (!effectiveClassId) {
      console.warn('No effectiveClassId provided for fetching thoughts');
      setIsLoading(false);
      return;
    }
    try {
      setIsLoading(true);
      const PAGE_SIZE = 10;
      const response = await getThought(undefined, effectiveClassId, pageIndex + 1, PAGE_SIZE);
      setThoughts((response.thoughts || []) as Thought[]);
      setTotalItems(response.total || 0);
    } catch (error: any) {
      console.error('Fetch thoughts error:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        classId: effectiveClassId,
      });
      toast.error(error.response?.data?.message || 'Failed to fetch thoughts');
      setThoughts([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
    }
  }, [effectiveClassId]);

  useEffect(() => {
    if (user && effectiveClassId) {
      fetchThoughts(0);
    } else {
      console.warn('User or classId missing', { user, effectiveClassId });
      toast.error('You must be logged in with a valid class ID');
      router.replace('/?authError=1');
    }
  }, [user, effectiveClassId, fetchThoughts, router]);

  const handleDeleteClick = (id: string) => {
    setDeletingThoughtId(id);
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!deletingThoughtId) return;

    try {
      setIsSubmitting(true);
      await deleteThought(deletingThoughtId);
      toast.success('Thought deleted successfully');
      await fetchThoughts(0);
    } catch (error: any) {
      console.error('Delete thought error:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      toast.error(error.response?.data?.message || 'Failed to delete thought');
    } finally {
      setDeletingThoughtId(null);
      setIsConfirmDialogOpen(false);
      setIsSubmitting(false);
    }
  };

  const onSubmit = async (data: ThoughtFormValues) => {
    try {
      if (!user) {
        toast.error('You must be logged in');
        router.replace('/?authError=1');
        return;
      }

      setIsSubmitting(true);
      if (editingThought) {
        await updateThought(editingThought.id, { thoughts: data.thoughts });
        toast.success('Thought updated successfully');
      } else {
        await createThought(data);
        toast.success('Thought created successfully');
      }

      form.reset({ thoughts: '', classId: effectiveClassId });
      setIsDialogOpen(false);
      setEditingThought(null);
      await fetchThoughts(0); 
    } catch (error: any) {
      console.error('Submit thought error:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
      });
      toast.error(error.response?.data?.message || `Failed to ${editingThought ? 'update' : 'create'} thought`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (thought: Thought) => {
    setEditingThought(thought);
    form.reset({
      thoughts: thought.thoughts,
      classId: thought.class?.id || effectiveClassId,
    });
    setIsDialogOpen(true);
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      setEditingThought(null);
      form.reset({ thoughts: '', classId: effectiveClassId });
    }
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Thoughts</h1>
        <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Thought
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingThought ? 'Edit Thought' : 'Create Thought'}
              </DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="thoughts"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Thought</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter thought (50-180 characters)" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex justify-end gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    {editingThought ? 'Update' : 'Create'}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <DynamicTable<Thought>
        columns={columns}
        data={thoughts}
        fetchData={fetchThoughts}
        totalItems={totalItems}
        pageSize={10}
        isLoading={isLoading}
        onEdit={handleEdit}
        onDelete={handleDeleteClick}
        deletingId={deletingThoughtId}
      />

      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the thought.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-500 hover:bg-red-600"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ThoughtsPage;
