<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    html {
        height: 100%;
        overflow-x: hidden;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #ffffff;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0;
        position: relative;
        overflow-x: hidden;
    }



    .ticket-container {
        background: #ffffff;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 100%;
        width: 100%;
        position: relative;
        border: 1px solid #e0e0e0;
        display: flex;
        flex-direction: row;
        height: 450px;
        min-height: 450px;
        max-height: 450px;
    }

    .ticket-header {
        background: #000000;
        color: #ffffff;
        padding: 30px 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
        flex: 0 0 250px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 20px;
    }

    .park-name {
        font-size: clamp(1.4rem, 3vw, 1.6rem);
        font-weight: 800;
        margin-bottom: 6px;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 1;
        letter-spacing: -0.5px;
        line-height: 1.1;
        color: #fd904b;
    }

    .park-tagline {
        font-size: clamp(0.7rem, 2vw, 0.8rem);
        opacity: 0.9;
        position: relative;
        z-index: 1;
        font-weight: 400;
        text-align: center;
        line-height: 1.3;
        color: #ffffff;
    }

    .ticket-body {
        padding: 16px;
        background: rgba(255, 255, 255, 0.9);
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        height: 450px;
        overflow: hidden;
    }

    .ticket-left, .ticket-right {
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
    }

    .ticket-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 12px;
        flex: 1;
        overflow: hidden;
    }

    .ticket-code {
        background: #fd904b;
        color: #000000;
        padding: 12px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(253, 144, 75, 0.3);
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: auto;
        flex-shrink: 0;
        touch-action: manipulation;
    }

    .ticket-bottom {
        display: flex;
        flex-direction: column;
        gap: 16px;
        height: 100%;
    }

    .info-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 12px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(253, 144, 75, 0.2);
        flex: 1;
        min-height: 0;
    }

    .info-title {
        font-size: clamp(0.8rem, 2.5vw, 0.85rem);
        font-weight: 700;
        color: #000000;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        letter-spacing: -0.3px;
    }

    .info-title::before {
        content: '🎫';
        margin-right: 6px;
        font-size: 0.9rem;
    }

    .info-item {
        margin-bottom: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid rgba(253, 144, 75, 0.2);
    }

    .info-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .info-label, .info-value {
        font-size: clamp(0.7rem, 2vw, 0.75rem);
        font-weight: 500;
        color: #666666;
    }

    .info-value {
        color: #000000;
        font-weight: 600;
    }

    .ticket-code:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 25px rgba(253, 144, 75, 0.4);
    }

    .code-label {
        font-size: clamp(0.6rem, 1.8vw, 0.65rem);
        opacity: 0.9;
        margin-bottom: 4px;
        font-weight: 500;
        letter-spacing: 1px;
    }

    .code-value {
        font-size: clamp(0.9rem, 2.5vw, 1rem);
        font-weight: 700;
        letter-spacing: 1.5px;
        font-family: 'Inter', monospace;
    }

    .terms-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 14px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(253, 144, 75, 0.2);
        flex: 1;
        margin-bottom: 5px;
        min-height: 0;
    }

    .terms-title {
        font-size: clamp(0.85rem, 2.5vw, 0.9rem);
        font-weight: 700;
        color: #000000;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        letter-spacing: -0.3px;
    }

    .terms-title::before {
        content: '📋';
        margin-right: 8px;
        font-size: 1.1rem;
    }

    .terms-list {
        list-style: none;
        color: #666666;
        line-height: 1.5;
    }

    .terms-list li {
        margin-bottom: 4px;
        padding-left: 14px;
        position: relative;
        font-size: clamp(0.65rem, 2vw, 0.7rem);
    }

    .terms-list li::before {
        content: '•';
        color: #fd904b;
        font-weight: bold;
        position: absolute;
        left: 0;
    }

    .watermark {
        position: absolute;
        top: 50%;
        right: -80px;
        transform: translateY(-50%) rotate(45deg);
        font-size: clamp(1.5rem, 4vw, 2rem);
        color: rgba(253, 144, 75, 0.1);
        font-weight: 800;
        pointer-events: none;
        letter-spacing: 2px;
    }

    .qr-section {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: auto;
        flex-shrink: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(253, 144, 75, 0.2);
    }

    .qr-title {
        font-size: clamp(0.85rem, 2.5vw, 0.95rem);
        font-weight: 700;
        color: #000000;
        text-align: center;
        margin: 0 0 8px 0;
        letter-spacing: 0.5px;
    }

    .qr-title::before {
        content: '🔗';
        margin-right: 8px;
    }

    .qr-items-container {
        display: flex;
        justify-content: space-between;
        gap: 16px;
    }

    .qr-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        flex: 1;
        padding: 8px;
        transition: all 0.3s ease;
    }

    .qr-item:hover {
        transform: translateY(-2px);
    }

    .qr-item img {
        border-radius: 6px;
        border: 2px solid #ffffff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .qr-label {
        font-size: clamp(0.65rem, 2vw, 0.7rem);
        font-weight: 600;
        color: #333333;
        text-align: center;
        letter-spacing: -0.2px;
    }

    /* Tablet and below */
    @media (max-width: 768px) {
        body {
            padding: 10px;
        }

        .ticket-container {
            flex-direction: column;
            max-width: 420px;
            min-height: auto;
            height: auto;
            max-height: none;
        }

        .ticket-header {
            flex: none;
            padding: 20px 16px;
        }

        .ticket-body {
            grid-template-columns: 1fr;
            gap: 12px;
            padding: 16px;
            height: auto;
        }

        .ticket-left, .ticket-right {
            height: auto;
        }

        .ticket-info {
            margin-bottom: 8px;
        }

        .info-section {
            padding: 10px;
        }

        .terms-section {
            max-height: none;
        }

        .ticket-code {
            padding: 10px;
        }

        .qr-section {
            padding: 12px;
            gap: 10px;
        }

        .qr-title {
            font-size: clamp(0.8rem, 2.5vw, 0.85rem);
        }

        .qr-items-container {
            gap: 12px;
        }

        .qr-item {
            padding: 6px;
        }

        .qr-item img {
            height: 70px;
            width: 70px;
        }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
        body {
            padding: 8px;
        }

        .ticket-container {
            max-width: 100%;
            border-radius: 12px;
        }

        .ticket-header {
            padding: 16px 12px;
        }

        .park-name {
            font-size: clamp(1.2rem, 4vw, 1.4rem);
        }

        .park-tagline {
            font-size: clamp(0.65rem, 2.5vw, 0.7rem);
        }

        .ticket-body {
            padding: 12px;
        }

        .info-section, .terms-section {
            padding: 8px;
        }

        .info-title, .terms-title {
            font-size: clamp(0.75rem, 2.5vw, 0.8rem);
        }

        .info-label, .info-value {
            font-size: clamp(0.65rem, 2vw, 0.7rem);
        }

        .terms-list li {
            font-size: clamp(0.6rem, 2vw, 0.65rem);
        }

        .ticket-code {
            padding: 8px;
        }

        .code-label {
            font-size: clamp(0.55rem, 2vw, 0.6rem);
        }

        .code-value {
            font-size: clamp(0.85rem, 2.5vw, 0.9rem);
        }

        .qr-section {
            padding: 10px;
            gap: 8px;
        }

        .qr-title {
            font-size: clamp(0.75rem, 2.5vw, 0.8rem);
        }

        .qr-items-container {
            gap: 10px;
        }

        .qr-item {
            padding: 4px;
        }

        .qr-item img {
            height: 60px;
            width: 60px;
        }

        .qr-label {
            font-size: clamp(0.6rem, 2vw, 0.65rem);
        }
    }

    /* Extra small devices */
    @media (max-width: 360px) {
        .ticket-container {
            border-radius: 10px;
        }

        .ticket-header {
            padding: 12px 10px;
        }

        .ticket-body {
            padding: 10px;
        }

        .info-section, .terms-section {
            padding: 6px;
        }

        .terms-section {
            max-height: none;
        }
    }

    /* Print styles - maintain exact format for PDF in landscape */
    @media print {
        @page {
            size: landscape;
            margin: 0;
            height: 440px;
        }

        /* Remove browser headers and footers */
        @page {
            margin: 0;
        }

        /* Hide browser print headers/footers */
        html {
            margin: 0 !important;
            padding: 0 !important;
        }

        * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        html, body {
            background: #ffffff !important;
            padding: 0 !important;
            margin: 0 !important;
            min-height: auto !important;
            height: auto !important;
            overflow: visible !important;
        }



        .ticket-container {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px) !important;
            width: 100% !important;
            max-width: 100% !important;
            height: 450px !important;
            min-height: 450px !important;
            max-height: 450px !important;
            display: flex !important;
            flex-direction: row !important;
            page-break-inside: avoid !important;
            margin: 0 !important;
            position: relative !important;
            transform: none !important;
        }

        .ticket-header {
            background: #000000 !important;
            color: #ffffff !important;
            padding: 24px 20px !important;
            text-align: center !important;
            flex: 0 0 250px !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: center !important;
        }



        .park-name {
            color: #fd904b !important;
            font-size: 1.6rem !important;
            font-weight: 800 !important;
            margin-bottom: 6px !important;
        }

        .park-tagline {
            color: #ffffff !important;
            font-size: 0.8rem !important;
            opacity: 0.9 !important;
        }

        .ticket-body {
            padding: 16px !important;
            background: rgba(255, 255, 255, 0.9) !important;
            flex: 1 !important;
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            gap: 16px !important;
            height: 450px !important;
        }

        .ticket-left, .ticket-right {
            display: flex !important;
            flex-direction: column !important;
            height: 100% !important;
        }

        .info-section {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            padding: 12px !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            border: 1px solid rgba(253, 144, 75, 0.2) !important;
            flex: 1 !important;
        }

        .info-title {
            color: #000000 !important;
            font-size: 0.85rem !important;
            font-weight: 700 !important;
            margin-bottom: 10px !important;
        }

        .info-title::before {
            content: '🎫' !important;
            margin-right: 6px !important;
        }

        .info-item {
            margin-bottom: 6px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 4px 0 !important;
            border-bottom: 1px solid rgba(253, 144, 75, 0.2) !important;
        }

        .info-label {
            color: #666666 !important;
            font-size: 0.75rem !important;
            font-weight: 500 !important;
        }

        .info-value {
            color: #000000 !important;
            font-size: 0.75rem !important;
            font-weight: 600 !important;
        }

        .ticket-code {
            background: #fd904b !important;
            color: #000000 !important;
            padding: 12px !important;
            border-radius: 10px !important;
            text-align: center !important;
            box-shadow: 0 4px 20px rgba(253, 144, 75, 0.3) !important;
            margin-top: auto !important;
        }

        .code-label {
            font-size: 0.65rem !important;
            opacity: 0.9 !important;
            margin-bottom: 4px !important;
            font-weight: 500 !important;
            letter-spacing: 1px !important;
        }

        .code-value {
            font-size: 1rem !important;
            font-weight: 700 !important;
            letter-spacing: 1.5px !important;
            font-family: 'Inter', monospace !important;
        }

        .terms-section {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            padding: 14px !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            border: 1px solid rgba(253, 144, 75, 0.2) !important;
            flex: 1 !important;
            margin-bottom: 5px !important;
        }

        .terms-title {
            color: #000000 !important;
            font-size: 0.9rem !important;
            font-weight: 700 !important;
            margin-bottom: 10px !important;
        }

        .terms-title::before {
            content: '📋' !important;
            margin-right: 8px !important;
        }

        .terms-list {
            list-style: none !important;
            color: #666666 !important;
            line-height: 1.5 !important;
        }

        .terms-list li {
            margin-bottom: 4px !important;
            padding-left: 14px !important;
            position: relative !important;
            font-size: 0.7rem !important;
        }

        .terms-list li::before {
            content: '•' !important;
            color: #fd904b !important;
            font-weight: bold !important;
            position: absolute !important;
            left: 0 !important;
        }

        .watermark {
            position: absolute !important;
            top: 50% !important;
            right: -80px !important;
            transform: translateY(-50%) rotate(45deg) !important;
            font-size: 2rem !important;
            color: rgba(253, 144, 75, 0.1) !important;
            font-weight: 800 !important;
            pointer-events: none !important;
            letter-spacing: 2px !important;
        }

        .qr-section {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            padding: 12px !important;
            border-radius: 10px !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
            border: 1px solid rgba(253, 144, 75, 0.2) !important;
            margin-top: auto !important;
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;
        }

        .qr-title {
            font-size: 0.8rem !important;
            font-weight: 700 !important;
            color: #000000 !important;
            text-align: center !important;
            margin: 0 !important;
            letter-spacing: 0.5px !important;
        }

        .qr-title::before {
            content: '🔗' !important;
            margin-right: 6px !important;
        }

        .qr-items-container {
            display: flex !important;
            justify-content: space-between !important;
            gap: 12px !important;
        }

        .qr-item {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            gap: 6px !important;
            flex: 1 !important;
            padding: 6px !important;
        }

        .qr-item img {
            border-radius: 6px !important;
            border: 2px solid #ffffff !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
            height: 60px !important;
            width: 60px !important;
        }

        .qr-label {
            font-size: 0.65rem !important;
            font-weight: 600 !important;
            color: #333333 !important;
            text-align: center !important;
        }
    }

        @media print {
      .no-print {
        display: none;
      }
    }
    </style>
</head>
<body>
    <div class="ticket-container">
        <div class="watermark">SM</div>

        <div class="ticket-header">
            <img src="sm_water_park.png" height="100" />
            <h1 class="park-name">🌊 SM WATER PARK</h1>
            <p class="park-tagline">Dive into Adventure • Make a Splash • Create Memories</p>
            <img src="logo_black.png" width="200" />
        </div>

        <div class="ticket-body">
            <div class="ticket-left">
                <div class="ticket-info">
                    <div class="info-section">
                        <div class="info-title">Visitor Details</div>
                        <div class="info-item">
                            <span class="info-label">Guest Name:</span>
                            <span class="info-value" id="guestName">Student Name</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Email:</span>
                            <span class="info-value" id="email"><EMAIL></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Ticket Type:</span>
                            <span class="info-value">Adult - Full Day</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <div class="info-title">Visit Information</div>
                        <div class="info-item">
                            <span class="info-label">Visit Date:</span>
                            <span class="info-value">June 09, 2025 & June 12, 2025</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Valid Time:</span>
                            <span class="info-value">9:00 AM - 6:00 PM</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Gate Entry:</span>
                            <span class="info-value">Main Entrance</span>
                        </div>
                    </div>
                </div>

                <div class="ticket-code">
                    <div class="code-label">TICKET CODE</div>
                    <div class="code-value" id="ticketCode">LOADING...</div>
                    <script>
                        (function() {
                            const urlParams = new URLSearchParams(window.location.search);
                            const ticketCode = urlParams.get('ticketCode');
                            const element = document.getElementById('ticketCode');
                            if (element && ticketCode) {
                                element.textContent = ticketCode;
                                console.log('SM Ticket Code Updated:', ticketCode);
                            } else {
                                console.log('SM Ticket Code Not Found in URL');
                                if (element) element.textContent = 'NO-CODE';
                            }
                        })();
                    </script>
                </div>
            </div>

            <div class="ticket-right">
                <div class="terms-section">
                    <div class="terms-title">Terms & Conditions</div>
                    <ul class="terms-list">
                        <li>All participants must come with their parents — no exceptions.</li>
                        <li>Entry is permitted only once and exclusively for <b>Uwhiz participants.</b></li>
                        <li>This ticket is valid only for the date mentioned above.</li>
                        <li>Entry is subject to park capacity and availability.</li>
                        <li>Children below 3 years of age are allowed free entry.</li>
                        <li>Outside food and beverages are strictly prohibited inside the park.</li>
                        <li>Proper swimming costumes are mandatory for all water rides.</li>
                        <li>Park management reserves the right to refuse entry</li>
                    </ul>
                </div>

                <div class="qr-section">
                    <h2 class="qr-title">Connect with Us</h2>
                    <div class="qr-items-container">
                        <div class="qr-item">
                            <img src="./app-qr-code.png" alt="App QR Code" height="80" width="80"/>
                            <span class="qr-label">📱 Mobile App</span>
                        </div>
                        <div class="qr-item">
                            <img src="./web-qr-code.png" alt="Web QR Code" height="80" width="80"/>
                            <span class="qr-label">🌐 Website</span>
                        </div>
                    </div>
                    <button class="no-print" style="background-color: #fd904b; padding: 10px; border: none;"  onclick="window.print()">Download Ticket</button>

                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to get student data from localStorage
        function getStudentData() {
            try {
                const data = localStorage.getItem("student_data");
                return data ? JSON.parse(data) : null;
            } catch {
                return null;
            }
        }

        // Function to populate ticket data
        function populateTicketData() {
            const studentData = getStudentData();

            console.log('SM Water Park - Student Data from localStorage:', studentData);

            if (studentData) {
                // Update guest name - combine firstName and lastName
                const firstName = studentData.firstName || 'Student';
                const lastName = studentData.lastName || 'Name';
                const guestName = `${firstName} ${lastName}`;
                const guestNameElement = document.getElementById('guestName');
                if (guestNameElement) {
                    guestNameElement.textContent = guestName;
                    console.log('SM Water Park - Updated guest name:', guestName);
                }

                // Update email
                const email = studentData.email || '<EMAIL>';
                const emailElement = document.getElementById('email');
                if (emailElement) {
                    emailElement.textContent = email;
                    console.log('SM Water Park - Updated email:', email);
                }
            } else {
                console.log('SM Water Park - No student data found in localStorage');
            }

            // Get ticket code from URL parameters or use placeholder
            const urlParams = new URLSearchParams(window.location.search);
            const providedTicketCode = urlParams.get('ticketCode');

            // Use provided ticket code from database, fallback to placeholder if not provided
            const ticketCode = providedTicketCode;
            const ticketCodeElement = document.getElementById('ticketCode');
            if (ticketCodeElement && ticketCode) {
                ticketCodeElement.textContent = ticketCode;
                console.log('SM Water Park - Updated ticket code:', ticketCode);
            }
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Populate ticket data first
            populateTicketData();

            // Add hover effects to info sections
            const infoSections = document.querySelectorAll('.info-section');
            infoSections.forEach(section => {
                section.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });

                section.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click effect to ticket code
            const ticketCodeElement = document.querySelector('.ticket-code');
            const ticketCodeValue = document.getElementById('ticketCode').textContent;
            ticketCodeElement.addEventListener('click', function() {
                navigator.clipboard.writeText(ticketCodeValue).then(function() {
                    const originalText = ticketCodeElement.innerHTML;
                    ticketCodeElement.innerHTML = `<div class="code-label">COPIED!</div><div class="code-value">${ticketCodeValue}</div>`;
                    setTimeout(() => {
                        ticketCodeElement.innerHTML = originalText;
                    }, 1500);
                });
            });
        });
    </script>
</body>
</html>