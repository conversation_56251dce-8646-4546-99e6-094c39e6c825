import prisma from "@/config/prismaClient";
import { getStudentReferralCode, getReferralDiscount } from "../../client/services/referralService";


export const validateAndDeductCoins = async (modelId:any, modelType:any, coinsRequired:any) => {
  try {
    if (!['CLASS', 'STUDENT', 'SCHOOL'].includes(modelType)) {
      throw new Error('Invalid modelType');
    }

    // Check for referral discount if it's a student
    let finalCoinsRequired = coinsRequired;
    let discountApplied = 0;

    if (modelType === 'STUDENT') {
      const referralCode = await getStudentReferralCode(modelId);
      if (referralCode) {
        const discountInfo = await getReferralDiscount(referralCode);
        if (discountInfo.hasDiscount) {
          discountApplied = Math.round((coinsRequired * discountInfo.discountPercentage) / 100);
          finalCoinsRequired = coinsRequired - discountApplied;
        }
      }
    }

    const userCoins = await prisma.uestCoins.findUnique({
      where: {
        modelId_modelType: {
          modelId,
          modelType,
        },
      },
    });

    if (!userCoins) {
      throw new Error('Required Coin for Applying in Exam: ₹' + finalCoinsRequired);
    }

    if (userCoins.coins < finalCoinsRequired) {
      throw new Error('Insufficient coins:'+'\nRequired Coin for Applying in Exam:  ₹' + finalCoinsRequired + ' But You Have Only: ₹' + userCoins.coins);
    }

    const result = await prisma.$transaction([
      prisma.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId,
            modelType,
          },
        },
        data: {
          coins: {
            decrement: finalCoinsRequired,
          },
        },
      }),
      prisma.uestCoinTransaction.create({
        data: {
          modelId,
          modelType,
          amount: finalCoinsRequired,
          type: 'DEBIT',
          reason: discountApplied > 0
            ? `Deducted for exam application (${discountApplied} discount applied)`
            : `Deducted for exam application`,
          createdAt: new Date(),
        },
      }),
    ]);

    return {
      success: true,
      remainingCoins: result[0].coins,
      discountApplied,
      originalAmount: coinsRequired,
      finalAmount: finalCoinsRequired
    };
  } catch (error:any) {
    throw new Error(`${error.message}`);
  }
};

