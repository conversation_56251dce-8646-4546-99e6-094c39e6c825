'use client';

import { useEffect, useState } from 'react';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useParams } from 'next/navigation';
import axiosInstance from '@/lib/axios';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

type Ranking = {
  rank: number;
  userId: number;
  name: string;
  score: number;
  attempted: number;
  totalQuestions: number;
};

const columns: ColumnDef<Ranking>[] = [
  { accessorKey: 'rank', header: 'Rank' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'score', header: 'Correct Answers' },
  { accessorKey: 'attempted', header: 'Attempted Answers' },
  { accessorKey: 'totalQuestions', header: 'Total Questions' },
  {
    header: 'Accuracy',
    cell: ({ row }) => {
      const { score, attempted } = row.original;
      const accuracy = attempted ? ((score / attempted) * 100).toFixed(1) : '0.0';
      return `${accuracy}%`;
    },
  },
];

export default function ExamRankingPage() {
  const [data, setData] = useState<Ranking[]>([]);
  const params = useParams();
  const examId = params.examId;

  useEffect(() => {
    if (!examId) return;

    axiosInstance
      .get<Ranking[]>(`/ranking/${examId}`)
      .then((res) => setData(res.data))
      .catch((err) => console.error('Error fetching rankings:', err));
  }, [examId]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Exam Rankings</h1>

      <div className="rounded-md border">
        <Table>
          <TableHeader className="sticky top-0 bg-muted z-10">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-sm font-medium">
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-2">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center py-4">
                  No ranking data available.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
