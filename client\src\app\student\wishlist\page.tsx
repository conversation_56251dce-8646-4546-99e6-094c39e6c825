"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { FaHeart, FaGraduationCap, FaUser } from "react-icons/fa";
import { IoShieldCheckmark } from "react-icons/io5";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { getWishlist, removeFromWishlist } from "@/services/studentWishlistServices";
import { isStudentAuthenticated } from "@/lib/utils";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface WishlistItem {
  id: string;
  savedClass: {
    id: string;
    firstName: string;
    lastName: string;
    className: string;
    ClassAbout?: {
      profilePhoto?: string;
      classesLogo?: string;
      catchyHeadline?: string;
    };
    status?: {
      status: string;
    };
  };
}

interface WishlistResponse {
  items: WishlistItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

const WishlistPage = () => {
  const [wishlistData, setWishlistData] = useState<WishlistResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();

  const fetchWishlist = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getWishlist(currentPage, 10);
      setWishlistData(response.data);
    } catch (error: any) {
      toast.error(error.message || "Failed to fetch wishlist");
    } finally {
      setLoading(false);
    }
  }, [currentPage]);

  useEffect(() => {
    if (!isStudentAuthenticated()) {
      router.push("/");
      toast.error("Please login as a student to view your wishlist");
      return;
    }

    fetchWishlist();
  }, [currentPage, router, fetchWishlist]);

  const handleRemoveFromWishlist = async (wishlistItemId: string) => {
    try {
      await removeFromWishlist(wishlistItemId);
      toast.success("Removed from wishlist");
      fetchWishlist();
    } catch (error: any) {
      toast.error(error.message || "Failed to remove from wishlist");
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <>
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Wishlist</h1>
            <p className="text-sm text-gray-500 mt-1">Classes you&apos;ve saved for later</p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="text-xs border-orange-200 text-orange-600 hover:bg-orange-50"
              onClick={() => router.push("/verified-classes")}
            >
              <FaGraduationCap className="mr-1 h-3 w-3" />
              Browse More Classes
            </Button>
          </div>
        </div>
        <div className="h-px bg-gray-200 w-full mb-6"></div>

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[...Array(8)].map((_, index) => (
              <Card key={index} className="overflow-hidden border border-gray-200 h-full flex flex-col p-0 rounded-md">
                <div className="relative h-40 bg-gray-100 rounded-t-md overflow-hidden">
                  <Skeleton className="h-full w-full" />
                </div>
                <CardContent className="p-2 py-1.5 flex-grow">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-3 w-1/2 mt-1 mb-0.5" />
                  <Skeleton className="h-3 w-2/3" />
                </CardContent>
                <CardFooter className="p-2 flex justify-between gap-2 mt-0">
                  <Skeleton className="h-7 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : wishlistData?.items.length === 0 ? (
          <div className="text-center py-12">
            <FaGraduationCap className="mx-auto h-16 w-16 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">No items in wishlist</h3>
            <p className="mt-1 text-sm text-gray-500">
              You haven&apos;t added any classes to your wishlist yet.
            </p>
            <div className="mt-6">
              <Button
                onClick={() => router.push("/verified-classes")}
                className="bg-orange-500 hover:bg-orange-600"
              >
                Browse Classes
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {wishlistData?.items.map((item) => {
                const { savedClass } = item;
                const fullName = `${savedClass.firstName} ${savedClass.lastName}`;
                const logoImg = savedClass.ClassAbout?.classesLogo
                  ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${savedClass.ClassAbout.classesLogo}`
                  : "/teacher-profile.jpg";

                return (
                  <Card key={item.id} className="overflow-hidden hover:shadow-md transition-shadow border border-gray-200 h-full flex flex-col p-0 rounded-md">
                    <div className="relative h-40 bg-gray-50 rounded-t-md overflow-hidden">
                      <Image
                        src={logoImg}
                        alt={fullName}
                        fill
                        className="object-cover"
                      />
                      {savedClass.status?.status === "APPROVED" && (
                        <div className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm">
                          <IoShieldCheckmark className="text-green-500 h-4 w-4" />
                        </div>
                      )}
                    </div>
                    <CardContent className="p-2 py-1.5 flex-grow">
                      <div className="flex items-center gap-2 mb-1">
                        <FaUser className="text-[#ff914d] h-4 w-4 flex-shrink-0" />
                        <h3 className="text-xl font-bold line-clamp-1">{fullName}</h3>
                        <p className="text-xs text-muted-foreground line-clamp-1">
                         ( {savedClass.className || ""} )
                        </p>
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5 mb-0.5">
                        {savedClass.ClassAbout?.catchyHeadline || "Professional Educator"}
                      </p>
                      <div className="flex items-center gap-2">

                      </div>
                    </CardContent>
                    <CardFooter className="p-2 flex justify-between gap-2 mt-0">
                      <Button
                        variant="default"
                        size="sm"
                        className="flex-1 bg-orange-500 hover:bg-orange-600 text-xs h-7"
                        onClick={() => router.push(`/classes-details/${savedClass.id}`)}
                      >
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        className="w-7 h-7 p-0 flex items-center justify-center"
                        onClick={() => handleRemoveFromWishlist(item.id)}
                      >
                        <FaHeart className="text-orange-500 h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>

            {/* Pagination */}
            {wishlistData && wishlistData.totalPages > 1 && (
              <Pagination className="mt-8">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      size="default"
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {Array.from({ length: wishlistData.totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        size="default"
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      size="default"
                      onClick={() => handlePageChange(Math.min(wishlistData.totalPages, currentPage + 1))}
                      className={currentPage === wishlistData.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </>
        )}
      </main>
      <Footer />
    </>
  );
};

export default WishlistPage;
