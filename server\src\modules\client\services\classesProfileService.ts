import prisma from '@/config/prismaClient';
import {
  Classes,
  ClassesAbout,
  ClassesEducation,
  ClassesExpereince,
  ClassesCertificates,
  TuitionClass,
  ClassApprovalStatus,
} from '@prisma/client';
import {
  EducationData,
  ExperienceData,
  CertificateData,
  TuitionClassData,
  UpdateProfilePayload,
  CreateClassProfileData,
  UpdateProfileImagesData,
  UpdateDescriptionData,
} from '@/utils/interface';

export const createClassProfile = (data: CreateClassProfileData): Promise<ClassesAbout> => {
  return prisma.classesAbout.create({
    data: data,
  });
};

export const findClassProfileByEmail = (classId: string): Promise<ClassesAbout | null> => {
  return prisma.classesAbout.findUnique({ where: { classId } });
};

export const findClassProfileByUsername = (classId: string): Promise<ClassesAbout | null> => {
  return prisma.classesAbout.findUnique({ where: { classId } });
};

export const updateClassProfile = (
  id: string,
  data: Partial<{
    username: string;
    firstName: string;
    lastName: string;
    className: string;
    email: string;
    birthDate: Date;
    contactNo: string;
    catchyHeadline: string;
    tutorBio: string;
    profilePhoto: string;
    schoolLogo: string;
    videoUrl: string;
  }>
): Promise<ClassesAbout> => {
  return prisma.classesAbout.update({
    where: { id },
    data,
  });
};

export const findClassProfileById = (id: string): Promise<ClassesAbout | null> => {
  return prisma.classesAbout.findUnique({ where: { id } });
};

export const checkUniqueEmail = async (email: string, classId: string): Promise<Classes | null> => {
  return prisma.classes.findFirst({
    where: {
      email,
      id: {
        not: classId,
      },
    },
  });
};

export const updateClassesProfileService = async (
  payload: UpdateProfilePayload,
  classId: string
): Promise<void> => {
  const { username, firstName, lastName, className, contactNo, birthDate, email } = payload;

  await prisma.classes.update({
    where: { id: classId },
    data: {
      username,
      firstName: firstName,
      lastName: lastName,
      className: className,
      contactNo: contactNo,
      email,
    },
  });

  await prisma.classesAbout.upsert({
    where: { classId },
    update: {
      birthDate: new Date(birthDate),
    },
    create: {
      classId,
      birthDate: new Date(birthDate),
    },
  });
};

export const updateProfileImages = async (
  classId: string,
  data: UpdateProfileImagesData
): Promise<ClassesAbout> => {
  return prisma.classesAbout.update({
    where: { classId },
    data,
  });
};

export const updateDescriptionQuery = async (
  classId: string,
  data: UpdateDescriptionData
): Promise<ClassesAbout> => {
  return prisma.classesAbout.update({
    where: { classId },
    data,
  });
};

export const saveEducation = async (
  classId: string,
  educationData: EducationData[],
  filePaths: string[]
): Promise<{ count: number }> => {
  const payload = educationData.map((edu, i) => ({
    classId,
    university: edu.university,
    degree: edu.degree,
    degreeType: edu.degreeType,
    passoutYear: edu.passoutYear,
    certificate: filePaths[i],
    isDegree: true,
  }));

  return prisma.classesEducation.createMany({ data: payload });
};

export const saveNoDegreeRecord = async (classId: string): Promise<ClassesEducation> => {
  await prisma.classesEducation.deleteMany({
    where: { classId },
  });
  return prisma.classesEducation.create({
    data: {
      classId,
      isDegree: false,
    },
  });
};

export const clearNoDegreeStatus = async (classId: string): Promise<{ count: number }> => {
  return prisma.classesEducation.deleteMany({
    where: {
      classId,
      isDegree: false,
    },
  });
};

export const saveExperience = async (
  classId: string,
  experienceData: ExperienceData[],
  filePaths: string[]
): Promise<{ count: number }> => {
  const payload = experienceData.map((exe, i) => ({
    classId,
    title: exe.title,
    from: exe.from,
    to: exe.to,
    certificateUrl: filePaths[i],
    isExperience: true,
  }));

  return prisma.classesExpereince.createMany({ data: payload });
};

export const saveNoExperienceRecord = async (classId: string): Promise<ClassesExpereince> => {
  await prisma.classesExpereince.deleteMany({
    where: { classId },
  });
  return prisma.classesExpereince.create({
    data: {
      classId,
      isExperience: false,
    },
  });
};

export const clearNoExperienceStatus = async (classId: string): Promise<{ count: number }> => {
  return prisma.classesExpereince.deleteMany({
    where: {
      classId,
      isExperience: false,
    },
  });
};

export const saveCertificate = async (
  classId: string,
  certificateData: CertificateData[],
  filePaths: string[]
): Promise<{ count: number }> => {
  const payload = certificateData.map((cert, i) => ({
    classId,
    title: cert.title,
    certificateUrl: filePaths[i],
    isCertificate: true,
  }));

  return prisma.classesCertificates.createMany({ data: payload });
};

export const saveNoCertificateRecord = async (classId: string): Promise<ClassesCertificates> => {
  await prisma.classesCertificates.deleteMany({
    where: { classId },
  });
  return prisma.classesCertificates.create({
    data: {
      classId,
      isCertificate: false,
    },
  });
};

export const clearNoCertificateStatus = async (classId: string): Promise<{ count: number }> => {
  return prisma.classesCertificates.deleteMany({
    where: {
      classId,
      isCertificate: false,
    },
  });
};

export const checkUniqueUserName = async (
  username: string,
  classId: string
): Promise<Classes | null> => {
  return prisma.classes.findFirst({
    where: {
      username,
      NOT: {
        id: classId,
      },
    },
  });
};

export const sendClassForReview = async (classId: string): Promise<{ message: string }> => {
  const existingClass = await prisma.classes.findUnique({
    where: { id: classId },
  });

  if (!existingClass) {
    throw new Error('Class not found');
  }

  await prisma.classesStatus.upsert({
    where: { classId },
    update: { status: 'PENDING' as ClassApprovalStatus },
    create: {
      classId,
      status: 'PENDING' as ClassApprovalStatus,
    },
  });

  return { message: 'Class profile sent for review' };
};

export const createTuitionClass = async (
  classId: string,
  tuitionClassData: TuitionClassData
): Promise<TuitionClass[]> => {
  const { tuitionDetails } = tuitionClassData;

  if (!Array.isArray(tuitionDetails) || tuitionDetails.length === 0) {
    throw new Error('No tuition class details provided');
  }

  try {
    return await Promise.all(
      tuitionDetails.map(async (detail) => {
        return await prisma.tuitionClass.create({
          data: {
            classId,
            boardType: Array.isArray(detail.boardType)
              ? JSON.stringify(detail.boardType)
              : detail.boardType,
            subject: Array.isArray(detail.subject)
              ? JSON.stringify(detail.subject)
              : detail.subject,
            education: detail.education,
            medium: Array.isArray(detail.medium) ? JSON.stringify(detail.medium) : detail.medium,
            section: Array.isArray(detail.section)
              ? JSON.stringify(detail.section)
              : detail.section,
            coachingType: Array.isArray(detail.coachingType)
              ? JSON.stringify(detail.coachingType)
              : detail.coachingType,
            details: Array.isArray(detail.details)
              ? JSON.stringify(detail.details)
              : detail.details,
          },
        });
      })
    );
  } catch (error) {
    console.error('Failed to create tuition classes:', error);
    throw error;
  }
};

export const deleteEducationRecord = async (id: string): Promise<ClassesEducation> => {
  return prisma.classesEducation.delete({
    where: { id },
  });
};

export const deleteExperienceRecord = async (id: string): Promise<ClassesExpereince> => {
  return prisma.classesExpereince.delete({
    where: { id },
  });
};

export const deleteCertificateRecord = async (id: string): Promise<ClassesCertificates> => {
  return prisma.classesCertificates.delete({
    where: { id },
  });
};

export const deleteTuitionClassRecord = async (id: string): Promise<TuitionClass> => {
  return prisma.tuitionClass.delete({
    where: { id },
  });
};