"use client";
import * as React from "react";
import { useState, useEffect, memo } from "react";
import {
  ActivitySquareIcon,
  BadgeCheck,
  Briefcase,
  ClipboardList,
  FileText,
  GraduationCap,
  ImageIcon,
  PenLine,
  QuoteIcon,
  UserCheck,
  Users,
  Share2,
  MessageSquare,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Image from "next/image";
import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { MdClass } from "react-icons/md";
import { axiosInstance } from "@/lib/axios";

const fetchEligibleStatus = async (classId: string) => {
  try {
    const response = await axiosInstance.get(`/can-classes-apply/${classId}`);
    const data = response.data;
    return data.some((item: any) => item.hasEligible === true);
  } catch (error: any) {
    console.error("Error fetching eligible status:", error.message);
    return false;
  }
};

const defaultNavMain = [
  {
    title: "Profile",
    icon: ClipboardList,
    children: [
      { title: "Profile", url: "/profile", icon: UserCheck },
      { title: "Descriptions", url: "/profile/description", icon: FileText },
      { title: "Photo & Logo", url: "/profile/photo-and-logo", icon: ImageIcon },
      { title: "Education", url: "/profile/education", icon: GraduationCap },
      { title: "Experience", url: "/profile/experience", icon: Briefcase },
      { title: "Certificates", url: "/profile/certificates", icon: BadgeCheck },
      { title: "Tution Class", url: "/profile/tution-class", icon: MdClass },
    ],
  },                                                                                                                                                                                                
  {
    title: "Testimonials",
    url: "/testimonials",
    icon: Users,
  },
  {
    title: "Thoughts",
    url: "/thoughts",
    icon: QuoteIcon,
  },
  {
    title: "Blogs",
    url: "/blogs",
    icon: PenLine,
  },
  {
    title: "Referrals",
    url: "/referral-dashboard",
    icon: Share2,
  },
  {
    title: "Chat",
    url: "/chat ",
    icon: MessageSquare,
  },
];

const MemoizedNavMain = memo(NavMain);

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const [navMain, setNavMain] = useState(defaultNavMain);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const updateNavMain = async () => {
      setIsLoading(true);
      const userData = JSON.parse(localStorage.getItem("user") || "{}");
      const classId = userData?.id;

      if (classId) {
        const isEligible = await fetchEligibleStatus(classId);
        const updatedNavMain = isEligible
          ? [
              ...defaultNavMain,
              {
                title: "QuestionBank",
                url: "/question-bank",
                icon: ActivitySquareIcon,
              },
            ]
          : defaultNavMain;

        setNavMain(updatedNavMain);
      } else {
        setNavMain(defaultNavMain);
      }
      setIsLoading(false);
    };

    updateNavMain();
  }, []);

  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="w-full flex justify-center">
              <SidebarMenuButton
                asChild
                className="data-[slot=sidebar-menu-button]:!p-1.5"
              >
                <a href="#">
                  <Image
                    src="/logo.png"
                    alt="App Logo"
                    width={120}
                    height={30}
                    className="rounded-md"
                  />
                </a>
              </SidebarMenuButton>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {isLoading ? (
          <div className="flex justify-center p-4">Loading...</div>
        ) : (
          <MemoizedNavMain items={navMain} />
        )}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}