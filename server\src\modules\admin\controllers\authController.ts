import { Request, Response } from 'express';
import { generateToken } from '@/utils/jwt';
import { comparePassword, findUserByEmail } from '../services/authService';
import { sendSuccess, sendError } from '@/utils/response';

export async function login(req: Request, res: Response): Promise<any> {
  try {
    const { email, password } = req.body;
    const user = await findUserByEmail(email);
    if (!user) return sendError(res, 'Invalid credentials', 401);

    const isMatch = await comparePassword(password, user.password);
    if (!isMatch) return sendError(res, 'Invalid credentials', 401);

    const token = generateToken({ id: user.id, email: user.email });

    res.cookie('admin_jwt', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000,
    });

    return sendSuccess(res, { email: user.email }, 'Login successful');
  } catch (err) {
    return sendError(res, err);
  }
}

export async function logout(req: Request, res: Response): Promise<any> {
  res.clearCookie('admin_jwt', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
  });

  return sendSuccess(res, null, 'Logged out successfully');
}
