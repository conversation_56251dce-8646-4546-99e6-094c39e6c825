import { Router } from 'express';
import {
  getAllClasses,
  getAllClassesforCount,
  getCategoryCountsController,
  getClassDetailsById,
  listApprovedTutors,
  patchClassStatus,
  updateClassByAdmin,
} from '../controllers/classesController';
import { authMiddleware } from '@/middlewares/adminAuth';

const classesRoutes = Router();

classesRoutes.get('/details/:id', getClassDetailsById);
classesRoutes.get('/details/:id/admin', authMiddleware, getClassDetailsById);
classesRoutes.get('/getAll', authMiddleware, getAllClasses);
classesRoutes.get('/getAllDisplayCount', authMiddleware, getAllClassesforCount);
classesRoutes.patch('/status/:classId', authMiddleware, patchClassStatus);
classesRoutes.put('/admin/:id', authMiddleware, updateClassByAdmin);
classesRoutes.get('/approved-tutors', listApprovedTutors);
classesRoutes.get('/category-counts', getCategoryCountsController);

export default classesRoutes;
