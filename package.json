{"scripts": {"prepare": "husky install", "lint:client": "cd client && npm run lint", "lint:admin": "cd admin && npm run lint", "lint:server": "cd server && npm run lint", "lint:all": "npm run lint:client && npm run lint:admin && npm run lint:server", "dev": "concurrently \"npm run client\" \"npm run admin\" \"npm run server\"", "client": "cd client && npm run dev", "admin": "cd admin && npm run dev", "server": "cd server && npm run dev"}, "devDependencies": {"husky": "^9.1.7"}, "dependencies": {"concurrently": "^9.1.2"}}