"use client";
import React, { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { MonthYearPicker } from "@/app-components/MonthYearPicker";
import { axiosInstance } from "@/lib/axios";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";
import { fetchClassDetails } from "@/store/thunks/classThunks";
import { useRouter } from "next/navigation";
import { Trash2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const experienceSchema = z.object({
  title: z.string().min(2, "Experience title is required"),
  file: z.custom<any>(
    (files) => files instanceof FileList && files.length > 0,
    {
      message: "Experience proof file is required",
    }
  ),
  from: z.string().min(1, "Start date is required"),
  to: z.string().min(1, "End date is required"),
});

const schema = z.object({
  noExperiences: z.boolean().optional(),
  experiences: z.array(experienceSchema).optional(),
});

type FormValues = z.infer<typeof schema>;

export function ExperienceForm() {
  const [noExperiences, setNoExperiences] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();

  const form = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      noExperiences: false,
      experiences: [
        {
          title: "",
          file: undefined,
          from: "",
          to: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "experiences",
  });

  const { user } : any = useSelector((state: RootState) => state.user);
  const onSubmit = async (data: FormValues) => {
    // If noExperiences is checked, use the dedicated handler
    if (data.noExperiences) {
      handleNoExperienceSubmit();
      return;
    }

    // If noExperiences is false, validate that experience data is provided
    if (!data.experiences || data.experiences.length === 0) {
      toast.error("Please add at least one experience record");
      return;
    }

    const formData = new FormData();
    formData.append("noExperience", "false");

    formData.append("experiences", JSON.stringify(data.experiences));

    data.experiences.forEach((exe) => {
      if (exe.file instanceof FileList) {
        formData.append("files", exe.file[0]);
      }
    });

    try {
      await axiosInstance.post(
        `/classes-profile/experience`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("Education uploaded successfully");
      dispatch(completeForm(FormId.EXPERIENCE));
      router.push("/classes/profile/certificates");
    } catch {
      toast.error("Something went wrong");
    }
  };

  const classData = useSelector((state: RootState) => state.class.classData);

  // Initialize form with existing data
  React.useEffect(() => {
    if (classData && !isInitialized) {
      // Check if user has isExperience set to false in any experience record
      const hasExperienceSetToFalse = classData.experience?.some((exp: any) => exp.isExperience === false);

      if (hasExperienceSetToFalse) {
        // User previously selected "I don't have any experience"
        setNoExperiences(true);
        form.setValue('noExperiences', true);

        // Clear any experience data that might be in the form
        form.setValue('experiences', []);

        // Show a message to the user
        toast.info("You have selected 'I don't have any experience'. You cannot add experience data unless you uncheck this option.");
      }

      setIsInitialized(true);
    }
  }, [classData, form, isInitialized]);

  // Handle submission when user checks "I don't have any experience"
  const handleNoExperienceSubmit = async () => {
    const formData = new FormData();
    formData.append("noExperience", "true");
    // We don't include any experience data when noExperience is true

    try {
      await axiosInstance.post(
        `/classes-profile/experience`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("No experience status saved");
      dispatch(completeForm(FormId.EXPERIENCE));
      router.push("/classes/profile/certificates");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  // Handle when user unchecks "I don't have any experience"
  const handleNoExperienceUncheck = async () => {
    const formData = new FormData();
    formData.append("noExperience", "false");
    // We don't include any experience data, just clearing the no-experience status

    try {
      await axiosInstance.post(
        `/classes-profile/experience`,
        formData,
        {
          headers: { "Content-Type": "multipart/form-data" },
        }
      );
      await dispatch(fetchClassDetails(user.id));
      toast.success("You can now add your experience details");
    } catch (error) {
      toast.error("Something went wrong");
      console.log(error);
    }
  };

  const handleDeleteExperience = async (experienceId: string, classId: string) => {
    try {
      await axiosInstance.delete(`/classes-profile/experience/${experienceId}`, {
        data: { classId }
      });
      toast.success("Experience deleted successfully");
      await dispatch(fetchClassDetails(classId));

      // Reset form to initial state after deletion
      form.reset({
        noExperiences: false,
        experiences: [
          {
            title: "",
            file: undefined,
            from: "",
            to: "",
          },
        ],
      });
    } catch {
      toast.error("Failed to delete experience");
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="noExperiences"
          render={({ field }) => (
            <FormItem className="flex items-center space-x-2">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={(checked) => {
                    field.onChange(checked);
                    setNoExperiences(!!checked);

                    // If checked, proceed to next form
                    if (checked) {
                      // Submit the form automatically when checkbox is checked
                      handleNoExperienceSubmit();
                    } else {
                      // If unchecked, clear the no-experience status in the database
                      handleNoExperienceUncheck();
                    }
                  }}
                />
              </FormControl>
              <FormLabel className="font-medium">
                I dont have any experience
              </FormLabel>
            </FormItem>
          )}
        />

        {classData?.experience?.length > 0 && !noExperiences && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Previous Experiences</h3>
            {classData.experience.map((exp : any, idx : any) => (
              <div
                key={idx}
                className="rounded-2xl border bg-muted/20 p-4 shadow-sm space-y-1"
              >
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <p className="font-medium">{exp.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(exp.from).toLocaleDateString()} -{" "}
                      {new Date(exp.to).toLocaleDateString()}
                    </p>
                    {exp.certificateUrl && (
                      <a
                        href={`${process.env.NEXT_PUBLIC_API_BASE_URL}uploads/classes/${classData.id}/experience/${exp.certificateUrl}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 underline"
                      >
                        View Uploaded Certificate
                      </a>
                    )}
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-red-500 cursor-pointer hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <DialogHeader>
                        <DialogTitle>Delete Experience</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to delete this experience? This action cannot be undone.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter className="gap-2">
                        <Button
                          variant="outline"
                          onClick={() => (document.querySelector('button[data-state="open"]') as any).click()}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => {
                            handleDeleteExperience(exp.id, classData.id);
                            (document.querySelector('button[data-state="open"]') as any).click();

                          }}
                        >
                          Delete
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            ))}
          </div>
        )}

        {!noExperiences &&
          fields.map((item, index) => (
            <div
              key={item.id}
              className="space-y-4 rounded-2xl border bg-muted/30 p-4 shadow-sm"
            >
              <FormField
                control={form.control}
                name={`experiences.${index}.title`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Experience Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g. Senior Teacher at XYZ"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`experiences.${index}.file`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Upload Proof (PDF/Image)</FormLabel>
                    <FormControl>
                      <Input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => field.onChange(e.target.files)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`experiences.${index}.from`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>From</FormLabel>
                    <FormControl>
                      <MonthYearPicker
                        date={field.value ? new Date(field.value) : undefined}
                        onChange={(date) =>
                          field.onChange(date?.toISOString() ?? "")
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`experiences.${index}.to`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>To</FormLabel>
                    <FormControl>
                      <MonthYearPicker
                        date={field.value ? new Date(field.value) : undefined}
                        onChange={(date) =>
                          field.onChange(date?.toISOString() ?? "")
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => remove(index)}
                >
                  Remove
                </Button>
              )}
            </div>
          ))}

        {!noExperiences && (
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              append({
                title: "",
                file: undefined,
                from: "",
                to: "",
              })
            }
            className="flex items-center gap-2"
          >
            Add More Experience
          </Button>
        )}

        <Button type="submit">
          Save Experience
        </Button>
      </form>
    </Form>
  );
}
