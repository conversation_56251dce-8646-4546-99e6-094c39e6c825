export interface EducationData {
  university: string;
  degree: string;
  degreeType: string;
  passoutYear: string;
}

export interface ExperienceData {
  title: string;
  from: string;
  to: string;
}

export interface CertificateData {
  title: string;
}

export interface TuitionClassDetail {
  boardType: string[] | string;
  subject: string[] | string;
  education: string;
  medium: string[] | string;
  section: string[] | string;
  coachingType: string[] | string;
  details?: string[] | string;
}

export interface TuitionClassData {
  tuitionDetails: TuitionClassDetail[];
}

export interface UpdateProfilePayload {
  username: string;
  firstName: string;
  lastName: string;
  className?: string;
  contactNo?: string;
  birthDate: string;
  email: string;
}

export interface CreateClassProfileData {
  username: string;
  firstName: string;
  lastName: string;
  className: string;
  email: string;
  birthDate: Date;
  contactNo: string;
  catchyHeadline?: string;
  tutorBio?: string;
  profilePhoto?: string;
  schoolLogo?: string;
  videoUrl?: string;
  classId: string;
}

export interface UpdateProfileImagesData {
  profilePhoto?: string;
  classesLogo?: string;
}

export interface UpdateDescriptionData {
  catchyHeadline: string;
  tutorBio: string;
}

// Define the Ranking interface
export interface Ranking {
  rank: number;
  userId: string;
  name: string;
  score: number;
  attempted: number;
  totalQuestions: number;
  classesName: string;
  classesLogo: string | null;
}

export interface CreateReviewInput {
  classId: string;
  message: string;
  rating: number;
  studentId?: string;
  className?: string;
  studentName?: string;
  modelType?: string;
  userType?: string;
}