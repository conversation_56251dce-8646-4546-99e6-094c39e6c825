// services/examService.ts
import { Exam } from '@prisma/client';
import prisma from '@/config/prismaClient';
import { ExamInputType, PaginationResponse } from '../interfaces/ExamInputType';

// Create a new exam
export const createExam = async (examData: ExamInputType): Promise<Exam> => {
  return prisma.exam.create({
    data: examData,
  });
};

// Get all exams with pagination and UwhizPriceRank
export const getAllExams = async (
  page: number = 1,
  limit: number = 10
): Promise<PaginationResponse> => {
  const safePage = Math.max(1, page);
  const safeLimit = Math.max(1, Math.min(100, limit));
  const skip = (safePage - 1) * safeLimit;

  const [exams, total] = await Promise.all([
    prisma.exam.findMany({
      skip,
      take: safeLimit,
      orderBy: { createdAt: 'desc' },
      include: {
        ExamApplication: {
          select: { id: true },
        },
        UwhizPriceRank: {
          select: {
            id: true,
            rank: true,
            price: true,
          },
          where: { rank: 1 },
        },
      },
    }),
    prisma.exam.count(),
  ]);

  // Transform exams to include joinedClassesCount and clean up response
  const transformedExams = exams.map((exam) => ({
    ...exam,
    joinedClassesCount: exam.ExamApplication.length,
    ExamApplication: undefined,
  }));

  return {
    exams: transformedExams,
    total,
    currentPage: safePage,
    totalPages: Math.ceil(total / safeLimit),
  };
};

export const getExamById = async (id: number): Promise<Exam | null> => {
  return prisma.exam.findUnique({
    where: { id },
  });
};

export const updateExam = async (id: number, data: Partial<any>): Promise<Exam> => {
  return prisma.exam.update({
    where: { id },
    data,
  });
};

// Delete an exam
export const deleteExam = async (id: number): Promise<Exam> => {
  try {
    const existingApplications = await prisma.examApplication.findFirst({
      where: { examId: id },
    });

    if (existingApplications) {
      throw new Error('Cannot delete exam because classes have applied for it');
    }

    return await prisma.exam.delete({
      where: { id },
    });
  } catch (error: any) {
    if (error.code === 'P2003') {
      throw new Error('Cannot delete exam due to foreign key constraints (e.g., applied classes)');
    }
    throw new Error(error.message || 'Failed to delete exam');
  }
};

export function countExams(): any {
  throw new Error('Function not implemented.');
}
