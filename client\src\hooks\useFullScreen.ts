import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { createQuizTerminationLog } from '@/services/quizTerminationLog';

export const useFullscreen = (classId: string, examId: number) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [escAttempts, setEscAttempts] = useState(0);
  const [showWarningDialog, setShowWarningDialog] = useState(false);
  const [showTerminationDialog, setShowTerminationDialog] = useState(false);
  const [lastViolationTime, setLastViolationTime] = useState<number | null>(null);
  const [isExitingForSubmit, setIsExitingForSubmit] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const checkMobile = () => {
    const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
    setIsMobile(mobile);
    return mobile;
  };

  const retryApiCall = async (fn: () => Promise<any>, retries = 3, delay = 1000) => {
    for (let i = 0; i < retries; i++) {
      try {
        return await fn();
      } catch (err) {
        if (i === retries - 1) throw err;
        await new Promise((resolve) => setTimeout(resolve, delay * 2 ** i));
      }
    }
  };

  const enterFullscreen = async () => {
    try {
      const docEl = document.documentElement as any;
      if (docEl.requestFullscreen) {
        await docEl.requestFullscreen();
      } else if (docEl.webkitRequestFullscreen) {
        await docEl.webkitRequestFullscreen();
      } else if (docEl.mozRequestFullScreen) {
        await docEl.mozRequestFullScreen();
      } else if (docEl.msRequestFullscreen) {
        await docEl.msRequestFullscreen();
      } else {
        if (isMobile) {
          toast.warning('Fullscreen not fully supported. Please maximize your browser window.');
          setIsFullscreen(true);
          return;
        }
        throw new Error('Fullscreen API not supported');
      }
      setIsFullscreen(true);
    } catch {
      toast.error('Failed to enter fullscreen mode. Quiz terminated.');
      setShowTerminationDialog(true);
    }
  };

  const exitFullscreen = async (forSubmit: boolean = false) => {
    try {
      if (document.fullscreenElement || (document as any).webkitFullscreenElement) {
        setIsExitingForSubmit(forSubmit);
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        }
        setTimeout(() => setIsExitingForSubmit(false), 0);
      } else if (isMobile && isFullscreen) {
        setIsFullscreen(false);
        setIsExitingForSubmit(false);
      }
    } catch (err) {
      console.error('Error exiting fullscreen:', err);
    }
  };

  useEffect(() => {
    checkMobile();

    const handleViolation = async (reason: string) => {
      const now = Date.now();
      if (lastViolationTime && now - lastViolationTime < 2000) {
        console.log('Debounced violation');
        return;
      }
      setLastViolationTime(now);
      try {
        await retryApiCall(() =>
          createQuizTerminationLog({
            classId,
            examId,
            reason,
            clientTime: new Date().toISOString(),
          })
        );
        setEscAttempts((prev) => {
          if (prev >= 2) return prev;
          return prev + 1;
        });
        if (escAttempts === 0) {
          setShowWarningDialog(true);
        } else if (escAttempts === 1 && !showTerminationDialog) {
          setShowTerminationDialog(true);
          setShowWarningDialog(false);
        }
      } catch {
        toast.error('Failed to log violation. Quiz terminated.');
        setShowTerminationDialog(true);
      }
    };

    const handleFullscreenChange = () => {
      const isNowFullscreen =
        !!document.fullscreenElement || !!(document as any).webkitFullscreenElement;
      setIsFullscreen(isNowFullscreen);
      if (!isNowFullscreen && !isExitingForSubmit && !showWarningDialog) {
        if (!isMobile || (isMobile && !isExitingForSubmit)) {
          handleViolation('Fullscreen exit attempt');
        }
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && !isMobile) {
        handleViolation('Tab switch or browser minimized');
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      const restrictedKeys = [
        'Alt',
        'Control',
        'Tab',
        'Escape',
        'F12',
        'ContextMenu',
        'Meta',
        'Shift',
      ];
      const restrictedCombos = [
        event.metaKey && event.key === 't',
        event.ctrlKey && event.key === 't',
        event.ctrlKey && event.key === 'i',
        event.ctrlKey && event.key === 'r',
      ];
      if (restrictedKeys.includes(event.key) || restrictedCombos.some(Boolean)) {
        event.preventDefault();
        if (!isMobile || event.key !== 'Escape') {
          // Allow Escape on mobile for browser UI
          handleViolation(`Restricted key pressed - ${event.key}`);
        }
      }
    };

    const handleContextMenu = (event: Event) => {
      event.preventDefault();
      handleViolation('Right-click attempt');
    };

    const handleMouseLeave = () => {
      if (!isMobile) {
        handleViolation('Mouse left window');
      }
    };

    const handleOrientationChange = () => {
      if (isMobile && isFullscreen) {
        // Re-enter fullscreen on orientation change for mobile
        enterFullscreen();
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    (document as any).addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('contextmenu', handleContextMenu);
    window.addEventListener('mouseleave', handleMouseLeave);
    window.addEventListener('orientationchange', handleOrientationChange);

    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      (document as any).removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('contextmenu', handleContextMenu);
      window.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    escAttempts,
    showWarningDialog,
    showTerminationDialog,
    lastViolationTime,
    isExitingForSubmit,
    classId,
    examId,
    isMobile,
  ]);

  return {
    isFullscreen,
    escAttempts,
    showWarningDialog,
    setShowWarningDialog,
    showTerminationDialog,
    setShowTerminationDialog,
    enterFullscreen,
    exitFullscreen,
  };
};
