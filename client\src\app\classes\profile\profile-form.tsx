"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useDispatch, useSelector } from "react-redux";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { axiosInstance } from "@/lib/axios";
import { RootState } from "@/store";
import { useEffect } from "react";
import { fetchClassDetails } from "@/store/thunks/classThunks";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns"; // make sure to import date-fns
import 'react-day-picker/dist/style.css';

const profileFormSchema = z.object({
  username: z
    .string()
    .min(2, "Username must be at least 2 characters.")
    .max(30),
  firstName: z.string().min(2, "First name must be at least 2 characters."),
  lastName: z.string().min(2, "Last name must be at least 2 characters."),
  className: z.string().min(2, "Class name must be at least 2 characters."),
  contactNo: z.string().min(10, "Contact must be at least 10 digits.").max(10, "Contact must be at least 10 digits."),
  birthDate: z.string({ required_error: 'Birthdate is required' }).nonempty('Birthdate cannot be empty'),  email: z.string({ required_error: 'Email is required' }).nonempty('Email cannot be empty').email('Please enter a valid email address'),
  isAdult: z.literal(true, {
    errorMap: () => ({ message: "You must confirm you are over 18." }),
  }),
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;
export function ProfileForm() {
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      username: "",
      firstName: "",
      lastName: "",
      className: "",
      birthDate: "",
      email: "",
      isAdult: true,
    },
    mode: "onChange",
  });

  const dispatch = useDispatch();
  const router = useRouter();

  const { user }: any = useSelector((state: RootState) => state.user);
  const classData = useSelector((state: RootState) => state.class.classData);

  async function onSubmit(data: ProfileFormValues) {
    try {
      await axiosInstance.post(`/classes-profile/about`, data);
      await dispatch(fetchClassDetails(user.id));
      dispatch(completeForm(FormId.PROFILE));
      toast.success("Profile updated successfully!");
      router.push("/classes/profile/description");
    } catch (error: any) {
      toast.error(error.response.data.message);
      console.error("Error submitting form:", error);
    }
  }

  const { reset } = form;

  useEffect(() => {
    if (classData || classData?.ClassAbout) {
      reset({
        username: classData?.username || "",
        firstName: classData?.firstName || "",
        lastName: classData?.lastName || "",
        className: classData?.className || "",
        birthDate: classData?.ClassAbout?.birthDate || "",
        email: classData?.email || "",
        contactNo: classData?.contactNo || "",
        isAdult: true,
      });
    }
  }, [classData, reset]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="john_doe" {...field} />
              </FormControl>
              <FormDescription>Should be unique</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>First Name</FormLabel>
              <FormControl>
                <Input placeholder="John" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Last Name</FormLabel>
              <FormControl>
                <Input placeholder="Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="className"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Class Name</FormLabel>
              <FormControl>
                <Input placeholder="Class Name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="birthDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Birthdate</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={"outline"}
                      className={`w-full pl-3 text-left font-normal bg-white border border-gray-300 hover:bg-gray-100 ${field.value ? "text-black dark:text-white" : "text-muted-foreground"
                        }`}
                    >
                      {field.value
                        ? format(new Date(field.value), "PPP") // Use new Date for string input
                        : "Pick a date"}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50 dark:text-black" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 shadow-lg" align="start">
                  <div className="w-full rounded-md border shadow-sm">
                    <Calendar
                      mode="single"
                      captionLayout="dropdown"
                      fromYear={1950}
                      toYear={new Date().getFullYear()}
                      selected={field.value ? new Date(field.value) : undefined} // Use new Date for string input
                      onSelect={(date) => {
                        if (date) {
                          const formatted = format(date, "yyyy-MM-dd"); // Format as yyyy-MM-dd
                          field.onChange(formatted);
                        }
                      }}
                      disabled={(date) => date > new Date()}
                      initialFocus
                      classNames={{
                        caption: "flex justify-center p-2",
                        dropdown: "mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white",
                        caption_label: "hidden",
                      }}
                    />
                  </div>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="contactNo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact No</FormLabel>
              <FormControl>
                <Input placeholder="Contact No" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isAdult"
          render={({ field }) => (
            <FormItem className="flex items-start gap-2">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div>
                <FormLabel>I confirm I’m over 18</FormLabel>
                <FormMessage />
              </div>
            </FormItem>
          )}
        />
        <Button type="submit">Update Profile</Button>
      </form>
    </Form>
  );
}
