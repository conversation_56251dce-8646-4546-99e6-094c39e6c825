import { Request, Response } from 'express';
import { validateAndDeductCoins } from '../services/uwhizCoinDeductionServices';

export const validateAndDeductCoinsController = async (req:Request, res:Response):Promise<any> => {
  const { modelId, modelType, coinsRequired } = req.body;

  if (!modelId || !modelType || !coinsRequired) {
    return res.status(400).json({ error: 'modelId, modelType, and coinsRequired are required' });
  }

  try {
    const result = await validateAndDeductCoins(modelId, modelType, coinsRequired);
    res.status(200).json(result);
  } catch (error:any) {
    res.status(500).json({ error: error.message });
  }
};

