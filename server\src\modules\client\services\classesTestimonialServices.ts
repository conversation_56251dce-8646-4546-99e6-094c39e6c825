import { Status } from '@prisma/client';
import prisma from '@/config/prismaClient';

interface CreateTestimonialInput {
  classId: string;
  message: string;
  rating: number;
  userId?: string;
}

export const getClassDetails = async (classId: string) => {
  return prisma.classes.findUnique({
    where: {
      id: classId
    },
    select: {
      id: true,
      className: true,
      firstName: true,
      lastName: true
    }
  });
};

export const createTestimonial = async (data: CreateTestimonialInput) => {

  const { ...testimonialData } = data;

  return prisma.testimonial.create({
    data: {
      ...testimonialData,
      status: Status.PENDING,
    },
  });
};

export const getTestimonialsByClassId = async (classId: string) => {
  return prisma.testimonial.findMany({
    where: {
      classId
    },
    include: {
      class: {
        select: {
          id: true,
          className: true,
          firstName: true,
          lastName: true,
          ClassAbout: {
            select: {
              classesLogo: true,
              profilePhoto: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    }
  });
};

export const updateTestimonialStatus = async (
  id: string,
  status: Status
) => {
  return prisma.testimonial.update({
    where: { id },
    data: { status },
  });
};

export const getAllTestimonials = async (page: number = 1, limit: number = 10, status?: Status) => {
  const skip = (page - 1) * limit;

  const whereClause = status ? { status } : {};

  const [testimonials, total] = await Promise.all([
    prisma.testimonial.findMany({
      skip,
      take: limit,
      where: whereClause,
      include: {
        class: {
          select: {
            id: true,
            className: true,
            firstName: true,
            lastName: true,
            ClassAbout: {
              select: {
                classesLogo: true,
                profilePhoto: true,
                tutorBio: true
              }
            }
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.testimonial.count({ where: whereClause }),
  ]);

  const formattedTestimonials = testimonials.map(testimonial => ({
    ...testimonial,
    class: {
      ...testimonial.class,
      id: testimonial.class.id, // Explicitly include the class ID
      fullName: `${testimonial.class.firstName} ${testimonial.class.lastName}`,
      classesLogo: testimonial.class.ClassAbout?.classesLogo || null,
      profilePhoto: testimonial.class.ClassAbout?.profilePhoto || null,
      tutorBio: testimonial.class.ClassAbout?.tutorBio || null
    }
  }));

  return {
    testimonials: formattedTestimonials,
    total,
    pages: Math.ceil(total / limit),
    currentPage:page
  };
};

export const getApprovedTestimonials = async (limit: number = 10) => {
  const testimonials = await prisma.testimonial.findMany({
    where: {
      status: Status.APPROVED
    },
    take: limit,
    include: {
      class: {
        select: {
          id: true,
          className: true,
          firstName: true,
          lastName: true,
          ClassAbout: {
            select: {
              classesLogo: true,
              profilePhoto: true,
              tutorBio: true
            }
          }
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });


  const formattedTestimonials = testimonials.map(testimonial => ({
    ...testimonial,
    class: {
      ...testimonial.class,
      id: testimonial.class.id, // Explicitly include the class ID
      fullName: `${testimonial.class.firstName} ${testimonial.class.lastName}`,
      classesLogo: testimonial.class.ClassAbout?.classesLogo || null,
      profilePhoto: testimonial.class.ClassAbout?.profilePhoto || null,
      tutorBio: testimonial.class.ClassAbout?.tutorBio || null
    }
  }));

  return formattedTestimonials;
};

export const deletetestimonials = async (id: string) => {
  return prisma.testimonial.delete({
    where: { id },
  });
};