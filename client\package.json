{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo -p 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.39.1", "@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.6.1", "@tanstack/react-table": "^8.21.3", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.7.3", "jose": "^6.0.10", "jspdf": "^3.0.1", "lucide-react": "^0.487.0", "next": "15.2.4", "next-auth": "^5.0.0-beta.28", "next-themes": "^0.4.6", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.1", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-quill-new": "^3.4.6", "react-redux": "^9.2.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "swiper": "^11.2.6", "tailwind-merge": "^3.1.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}