import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import { createShivWaterParkTicket, createSMWaterParkTicket, getAllShivWaterParkTickets, getAllSMWaterParkTickets, getShivWaterParkTicketByCode, getSMWaterParkTicketByCode } from '../services/ticketService';

export const generateSMWaterParkTicketController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const ticket = await createSMWaterParkTicket(studentId);

    const message = ticket.createdAt.getTime() === ticket.updatedAt.getTime()
      ? 'New SM Water Park ticket generated successfully'
      : 'Your existing SM Water Park ticket retrieved successfully';

    return sendSuccess(res, ticket, message);
  } catch (error: any) {
    console.error('Error generating SM Water Park ticket:', error);
    return sendError(res, error.message || 'Failed to generate SM Water Park ticket', 500);
  }
};

export const getSMWaterParkTicketByCodeController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ticketCode } = req.params;

    if (!ticketCode) {
      return sendError(res, 'Ticket code is required', 400);
    }

    const ticket = await getSMWaterParkTicketByCode(ticketCode);

    if (!ticket) {
      return sendError(res, 'SM Water Park ticket not found', 404);
    }

    return sendSuccess(res, ticket, 'SM Water Park ticket retrieved successfully');
  } catch (error: any) {
    console.error('Error getting SM Water Park ticket by code:', error);
    return sendError(res, error.message || 'Failed to get SM Water Park ticket', 500);
  }
};

export const getAllSMWaterParkTicketsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await getAllSMWaterParkTickets(page, limit, search);

    return sendSuccess(res, result, 'All SM Water Park tickets retrieved successfully');
  } catch (error: any) {
    console.error('Error getting all SM Water Park tickets:', error);
    return sendError(res, error.message || 'Failed to get SM Water Park tickets', 500);
  }
};

export const generateShivWaterParkTicketController = async (req: Request, res: Response): Promise<any> => {
  try {
    const studentId = req.student?.id;
    if (!studentId) {
      return sendError(res, 'Unauthorized', 401);
    }

    const ticket = await createShivWaterParkTicket(studentId);

    const message = ticket.createdAt.getTime() === ticket.updatedAt.getTime()
      ? 'New Shiv Water Park ticket generated successfully'
      : 'Your existing Shiv Water Park ticket retrieved successfully';

    return sendSuccess(res, ticket, message);
  } catch (error: any) {
    console.error('Error generating Shiv Water Park ticket:', error);
    return sendError(res, error.message || 'Failed to generate Shiv Water Park ticket', 500);
  }
};

export const getShivWaterParkTicketByCodeController = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ticketCode } = req.params;

    if (!ticketCode) {
      return sendError(res, 'Ticket code is required', 400);
    }

    const ticket = await getShivWaterParkTicketByCode(ticketCode);

    if (!ticket) {
      return sendError(res, 'Shiv Water Park ticket not found', 404);
    }

    return sendSuccess(res, ticket, 'Shiv Water Park ticket retrieved successfully');
  } catch (error: any) {
    console.error('Error getting Shiv Water Park ticket by code:', error);
    return sendError(res, error.message || 'Failed to get Shiv Water Park ticket', 500);
  }
};

export const getAllShivWaterParkTicketsController = async (req: Request, res: Response): Promise<any> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const search = req.query.search as string;

    const result = await getAllShivWaterParkTickets(page, limit, search);

    return sendSuccess(res, result, 'All Shiv Water Park tickets retrieved successfully');
  } catch (error: any) {
    console.error('Error getting all Shiv Water Park tickets:', error);
    return sendError(res, error.message || 'Failed to get Shiv Water Park tickets', 500);
  }
};