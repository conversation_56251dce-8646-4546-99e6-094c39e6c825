import { axiosInstance } from '@/lib/axios';

export const getReviewsByClassId = async (classId: string, page: number = 1, limit: number = 5) => {
  try {
    const response = await axiosInstance.get(`/reviews/class/${classId}`, {
      params: { page, limit }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(`Failed to fetch reviews for class: ${error.message}`);
  }
};

export const getAverageRating = async (classId: string) => {
  try {
    const response = await axiosInstance.get(`/reviews/average/${classId}`);
    return response.data.averageRating;
  } catch (error: any) {
    throw new Error(`Failed to get average rating: ${error.message}`);
  }
};

export const createReview = async (data: {
  classId: string;
  rating: number;
  message: string;
  studentName?: string;
  studentId?: string;
}) => {
  try {
    const response = await axiosInstance.post('/reviews', data);
    return response.data;
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message;
    const alreadyReviewed = error.response?.data?.alreadyReviewed || false;

    const customError = new Error(errorMessage);
    (customError as any).alreadyReviewed = alreadyReviewed;

    throw customError;
  }
};

export const deleteReview = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/reviews/${id}`);
    return response.data;
  } catch (error: any) {
    throw new Error(`Failed to delete review: ${error.message}`);
  }
};
