'use client';

import { useParams } from 'next/navigation'; 
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useState, useEffect } from 'react';

interface Exam {
    id: number;
    exam_name: string;
}

const ExamPreferencesPage = () => {
    const params = useParams();
    const examId = params?.examId;

    const [exam, setExam] = useState<Exam | null>(null);

    const [activeTab, setActiveTab] = useState('subject');

    useEffect(() => {
        if (examId) {
            // API call Real implementation
            const dummyExam: Exam = {
                id: Number(examId),
                exam_name: `Exam ${examId}`,
            };
            setExam(dummyExam);
        }
    }, [examId]);

    if (!exam) {
        return <div className="flex justify-center items-center h-screen">Loading...</div>;
    }

    return (
        <div className="min-h-screen p-6">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-gray-800">
                    Preferences for Exam: {exam.exam_name} (ID: {exam.id})
                </h1>
            </div>

            {/* Tabs Section */}
            <Tabs
                defaultValue="subject"
                onValueChange={(value) => setActiveTab(value)}
                className="w-full"
            >
                {/* Tabs List */}
                <TabsList className="flex w-full bg-gray-900 rounded-t-lg border-b border-gray-700 py-6 px-2">
                    <TabsTrigger
                        value="subject"
                        className="flex-1 py-3 px-4 text-gray-300 font-medium text-sm transition-all 
               data-[state=active]:text-orange-500 data-[state=active]:border-b-2 
               data-[state=active]:border-orange-500 data-[state=active]:font-semibold 
               hover:text-orange-400 hover:bg-gray-800 rounded-t-md"
                    >
                        Subject Preferences
                    </TabsTrigger>
                    <TabsTrigger
                        value="level"
                        className="flex-1 py-3 px-4 text-gray-300 font-medium text-sm transition-all 
               data-[state=active]:text-orange-500 data-[state=active]:border-b-2 
               data-[state=active]:border-orange-500 data-[state=active]:font-semibold 
               hover:text-orange-400 hover:bg-gray-800 rounded-t-md"
                    >
                        Level Preferences
                    </TabsTrigger>
                    <TabsTrigger
                        value="medium"
                        className="flex-1 py-3 px-4 text-gray-300 font-medium text-sm transition-all 
               data-[state=active]:text-orange-500 data-[state=active]:border-b-2 
               data-[state=active]:border-orange-500 data-[state=active]:font-semibold 
               hover:text-orange-400 hover:bg-gray-800 rounded-t-md"
                    >
                        Medium Preferences
                    </TabsTrigger>
                    <TabsTrigger
                        value="classroom"
                        className="flex-1 py-3 px-4 text-gray-300 font-medium text-sm transition-all 
               data-[state=active]:text-orange-500 data-[state=active]:border-b-2 
               data-[state=active]:border-orange-500 data-[state=active]:font-semibold 
               hover:text-orange-400 hover:bg-gray-800 rounded-t-md"
                    >
                        Classroom Preferences
                    </TabsTrigger>
                    <TabsTrigger
                        value="question-group"
                        className="flex-1 py-3 px-4 text-gray-300 font-medium text-sm transition-all 
               data-[state=active]:text-orange-500 data-[state=active]:border-b-2 
               data-[state=active]:border-orange-500 data-[state=active]:font-semibold 
               hover:text-orange-400 hover:bg-gray-800 rounded-t-md"
                    >
                        Question Group Preferences
                    </TabsTrigger>
                </TabsList>

                {/* Active Tab Header */}
                <div className="mt-6 mb-4">
                    <h2 className="text-2xl font-semibold text-gray-700">
                        {activeTab === 'subject' && 'Subject Preferences'}
                        {activeTab === 'level' && 'Level Preferences'}
                        {activeTab === 'medium' && 'Medium Preferences'}
                        {activeTab === 'classroom' && 'Classroom Preferences'}
                        {activeTab === 'question-group' && 'Question Group Preferences'}
                    </h2>
                </div>

                {/* Tabs Content  */}
                <TabsContent value="subject" className="bg-white rounded-lg p-4 shadow">
                    <p>Subject Preferences content will go here (Table and Modal will be added later).</p>
                </TabsContent>
                <TabsContent value="level" className="bg-white rounded-lg p-4 shadow">
                    <p>Level Preferences content will go here (Table and Modal will be added later).</p>
                </TabsContent>
                <TabsContent value="medium" className="bg-white rounded-lg p-4 shadow">
                    <p>Medium Preferences content will go here (Table and Modal will be added later).</p>
                </TabsContent>
                <TabsContent value="classroom" className="bg-white rounded-lg p-4 shadow">
                    <p>Classroom Preferences content will go here (Table and Modal will be added later).</p>
                </TabsContent>
                <TabsContent value="question-group" className="bg-white rounded-lg p-4 shadow">
                    <p>Question Group Preferences content will go here (Table and Modal will be added later).</p>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default ExamPreferencesPage;