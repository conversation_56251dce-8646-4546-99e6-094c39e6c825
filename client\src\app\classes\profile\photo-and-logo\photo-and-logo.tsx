"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { completeForm, FormId } from "@/store/slices/formProgressSlice";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { axiosInstance } from "@/lib/axios";
import { RootState } from "@/store";
import { fetchClassDetails } from "@/store/thunks/classThunks";

const allowedImageTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];

const schema = z.object({
  photo: z
    .instanceof(File)
    .refine((file) => file?.size < 2 * 1024 * 1024, {
      message: "Photo must be less than 2MB",
    })
    .refine((file) => file ? allowedImageTypes.includes(file.type) : true, {
      message: "Only image files (JPEG, PNG, GIF, WEBP) are allowed",
    })
    .optional(),
  logo: z
    .instanceof(File)
    .refine((file) => file?.size < 1 * 1024 * 1024, {
      message: "Logo must be less than 1MB",
    })
    .refine((file) => file ? allowedImageTypes.includes(file.type) : true, {
      message: "Only image files (JPEG, PNG, GIF, WEBP) are allowed",
    })
    .optional(),
});

type FormData = z.infer<typeof schema>;

export default function PhotoLogoUpload() {
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [existingPhoto, setExistingPhoto] = useState<string | null>(null);
  const [existingLogo, setExistingLogo] = useState<string | null>(null);
  const [cropModalOpen, setCropModalOpen] = useState<"photo" | "logo" | null>(null);
  const [imageToCrop, setImageToCrop] = useState<string | null>(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: { photo: undefined, logo: undefined },
  });

  const dispatch = useDispatch();
  const router = useRouter();

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: "photo" | "logo"
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!allowedImageTypes.includes(file.type)) {
        toast.error("Only image files (JPEG, PNG, GIF, WEBP) are allowed");
        e.target.value = ""; // Clear the input
        return;
      }
      const reader = new FileReader();
      reader.onload = () => {
        setImageToCrop(reader.result as string);
        setCropModalOpen(fieldName);
      };
      reader.readAsDataURL(file);
    }
  };

  const getCroppedImg = async (
    imageSrc: string,
    pixelCrop: { x: number; y: number; width: number; height: number }
  ): Promise<File> => {
    const image = new window.Image();
    image.src = imageSrc;
    await new Promise((resolve) => (image.onload = resolve));

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d")!;
    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.drawImage(
      image,
      pixelCrop.x,
      pixelCrop.y,
      pixelCrop.width,
      pixelCrop.height,
      0,
      0,
      pixelCrop.width,
      pixelCrop.height
    );

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(new File([blob], "cropped-image.jpg", { type: "image/jpeg" }));
        }
      }, "image/jpeg");
    });
  };

  const handleCropComplete = (croppedArea: any, croppedAreaPixels: any) => {
    setCroppedAreaPixels(croppedAreaPixels);
  };

  const handleCropSave = async () => {
    if (imageToCrop && croppedAreaPixels) {
      try {
        const croppedFile = await getCroppedImg(imageToCrop, croppedAreaPixels);
        const previewUrl = URL.createObjectURL(croppedFile);

        if (cropModalOpen === "photo") {
          form.setValue("photo", croppedFile, { shouldValidate: true });
          setPhotoPreview(previewUrl);
        } else if (cropModalOpen === "logo") {
          form.setValue("logo", croppedFile, { shouldValidate: true });
          setLogoPreview(previewUrl);
        }

        setCropModalOpen(null);
        setImageToCrop(null);
        setCrop({ x: 0, y: 0 });
        setZoom(1);
      } catch (error) {
        console.error("Error cropping image:", error);
        toast.error("Failed to crop image");
      }
    }
  };

  const { user }: any = useSelector((state: RootState) => state.user);

  const onSubmit = async (data: FormData) => {
    try {
      const formData = new FormData();
      if (data.photo) formData.append("profilePhoto", data.photo);
      if (data.logo) formData.append("classesLogo", data.logo);

      await axiosInstance.post(`/classes-profile/images`, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      await dispatch(fetchClassDetails(user.id));

      toast.success("Photos uploaded successfully!");
      dispatch(completeForm(FormId.PHOTO_LOGO));
      router.push("/classes/profile/education");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Failed to upload files");
    }
  };

  const classData = useSelector((state: RootState) => state.class.classData);
  useEffect(() => {
    if (classData || classData?.ClassAbout) {
      if (classData?.ClassAbout?.profilePhoto) {
        setExistingPhoto(
          process.env.NEXT_PUBLIC_API_BASE_URL + classData?.ClassAbout?.profilePhoto || null
        );
      }
      if (classData?.ClassAbout?.classesLogo) {
        setExistingLogo(
          process.env.NEXT_PUBLIC_API_BASE_URL + classData?.ClassAbout?.classesLogo || null
        );
      }
    }
  }, [classData]);

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="photo"
            render={() => (
              <FormItem>
                <FormLabel>Profile Photo</FormLabel>
                <FormControl>
                  <Input
                    type="file"
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    onChange={(e) => handleFileChange(e, "photo")}
                  />
                </FormControl>
                {photoPreview || existingPhoto ? (
                  <Image
                    src={photoPreview || existingPhoto!}
                    alt="Profile Preview"
                    width={120}
                    height={120}
                    className="rounded-full mt-2 border"
                  />
                ) : null}
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="logo"
            render={() => (
              <FormItem>
                <FormLabel>Classes Logo</FormLabel>
                <FormControl>
                  <Input
                    type="file"
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    onChange={(e) => handleFileChange(e, "logo")}
                  />
                </FormControl>
                {logoPreview || existingLogo ? (
                  <Image
                    src={logoPreview || existingLogo!}
                    alt="Logo Preview"
                    width={120}
                    height={120}
                    className="rounded-md mt-2 border bg-white"
                  />
                ) : null}
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit">Upload</Button>
        </form>
      </Form>

      {cropModalOpen && imageToCrop && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-[90%] max-w-[500px]">
            <h2 className="text-lg font-semibold mb-4">Crop Image</h2>
            <div className="relative w-full h-[300px]">
              <Cropper
                image={imageToCrop}
                crop={crop}
                zoom={zoom}
                aspect={cropModalOpen === "photo" ? 1 : 4 / 3}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={handleCropComplete}
              />
            </div>
            <div className="mt-4">
              <input
                type="range"
                min={1}
                max={3}
                step={0.1}
                value={zoom}
                onChange={(e) => setZoom(Number(e.target.value))}
                className="w-full"
              />
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setCropModalOpen(null)}>
                Cancel
              </Button>
              <Button onClick={handleCropSave}>Save Crop</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}