import prisma from '@/config/prismaClient';
import { UserType } from '@prisma/client';

// Generate unique referral code
export const generateReferralCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Create or get referral link for user
export const createOrGetReferralLink = async (userId: string, userType: UserType) => {
  // Check if user already has a referral link
  let referralLink = await prisma.referralLink.findFirst({
    where: {
      userId,
      userType,
      isActive: true,
    },
  });

  // If no link exists, create one
  if (!referralLink) {
    let code = generateReferralCode();

    // Ensure code is unique
    while (await prisma.referralLink.findUnique({ where: { code } })) {
      code = generateReferralCode();
    }

    referralLink = await prisma.referralLink.create({
      data: {
        userId,
        userType,
        code,
      },
    });
  }

  return referralLink;
};

// Get referral link by code
export const getReferralLinkByCode = async (code: string) => {
  return prisma.referralLink.findUnique({
    where: { code },
    include: {
      referrals: true,
    },
  });
};

// Check if referral code provides discount
export const getReferralDiscount = async (code: string) => {
  // Define discount codes and their discount percentages
  const discountCodes: { [key: string]: number } = {
    'RBNEWS25': 20, // 20% discount
    'RBNEWS2005': 20, // 20% discount
  };

  if (discountCodes[code]) {
    return {
      hasDiscount: true,
      discountPercentage: discountCodes[code],
    };
  }

  return {
    hasDiscount: false,
    discountPercentage: 0,
  };
};

// Get student's referral code (to check for discounts during exam application)
export const getStudentReferralCode = async (studentId: string) => {
  const referral = await prisma.referral.findFirst({
    where: {
      referredUserId: studentId,
      referredUserType: 'STUDENT',
    },
    include: {
      referralLink: true,
    },
    orderBy: {
      createdAt: 'desc', // Get the most recent referral
    },
  });

  return referral?.referralLink?.code || null;
};

// Create referral record
export const createReferral = async (
  referralLinkId: string,
  referredUserId: string,
  referredUserType: UserType,
  referredUserName: string,
  referredUserEmail: string
) => {
  const referral = await prisma.referral.create({
    data: {
      referralLinkId,
      referredUserId,
      referredUserType,
      referredUserName,
      referredUserEmail,
    },
  });

  // Create earning record for student registration (₹10)
  // Create earnings for all referrers (CLASS, ADMIN, and STUDENT)
  if (referredUserType === 'STUDENT') {
    await createReferralEarning(
      referredUserId,
      null, // No exam ID for registration
      referral.id,
      'REGISTRATION',
      10
    );
  }

  return referral;
};

// Create referral earning record
export const createReferralEarning = async (
  studentId: string,
  examId: string | null,
  referralId: string,
  earningType: 'REGISTRATION' | 'UWHIZ_APPLICATION',
  amount: number
) => {
  return prisma.referralEarning.create({
    data: {
      studentId,
      examId,
      referralId,
      earningType,
      amount,
      paymentStatus: 'UNPAID',
    },
  });
};

// Create U-whiz application earning
export const createUwhizApplicationEarning = async (
  studentId: string,
  examId: string
) => {
  // Find the referral record for this student
  const referral = await prisma.referral.findFirst({
    where: {
      referredUserId: studentId,
      referredUserType: 'STUDENT',
    },
    orderBy: {
      createdAt: 'desc', // Get the most recent referral
    },
  });

  if (!referral) {
    return null; // No referral found for this student
  }

  // Check if earning already exists for this student and exam
  const existingEarning = await prisma.referralEarning.findFirst({
    where: {
      studentId,
      examId,
      earningType: 'UWHIZ_APPLICATION',
    },
  });

  if (existingEarning) {
    return existingEarning; // Already exists
  }

  // Create new U-whiz earning
  return createReferralEarning(
    studentId,
    examId,
    referral.id,
    'UWHIZ_APPLICATION',
    25
  );
};

// Get referral dashboard data
export const getReferralDashboardData = async (userId: string, userType: UserType) => {
  const referralLink = await prisma.referralLink.findFirst({
    where: {
      userId,
      userType,
      isActive: true,
    },
    include: {
      referrals: {
        include: {
          earnings: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
    },
  });

  if (!referralLink) {
    return {
      totalReferrals: 0,
      studentsReferred: 0,
      classesReferred: 0,
      referralCode: null,
      history: [],
      earnings: {
        totalEarnings: 0,
        registrationEarnings: 0,
        uwhizEarnings: 0,
        paidEarnings: 0,
        unpaidEarnings: 0,
      },
    };
  }

  const totalReferrals = referralLink.referrals.length;
  const studentsReferred = referralLink.referrals.filter(r => r.referredUserType === 'STUDENT').length;
  const classesReferred = referralLink.referrals.filter(r => r.referredUserType === 'CLASS').length;

  // Calculate earnings
  const allEarnings = referralLink.referrals.flatMap(r => r.earnings);
  const registrationEarnings = allEarnings
    .filter(e => e.earningType === 'REGISTRATION')
    .reduce((sum, e) => sum + e.amount, 0);
  const uwhizEarnings = allEarnings
    .filter(e => e.earningType === 'UWHIZ_APPLICATION')
    .reduce((sum, e) => sum + e.amount, 0);
  const totalEarnings = registrationEarnings + uwhizEarnings;
  const paidEarnings = allEarnings
    .filter(e => e.paymentStatus === 'PAID')
    .reduce((sum, e) => sum + e.amount, 0);
  const unpaidEarnings = allEarnings
    .filter(e => e.paymentStatus === 'UNPAID')
    .reduce((sum, e) => sum + e.amount, 0);

  return {
    totalReferrals,
    studentsReferred,
    classesReferred,
    referralCode: referralLink.code,
    history: referralLink.referrals,
    earnings: {
      totalEarnings,
      registrationEarnings,
      uwhizEarnings,
      paidEarnings,
      unpaidEarnings,
    },
  };
};

// Get referral history with date filter
export const getReferralHistory = async (
  userId: string,
  userType: UserType,
  startDate?: Date,
  endDate?: Date
) => {
  const referralLink = await prisma.referralLink.findFirst({
    where: {
      userId,
      userType,
      isActive: true,
    },
  });

  if (!referralLink) {
    return [];
  }

  const whereClause: any = {
    referralLinkId: referralLink.id,
  };

  if (startDate && endDate) {
    whereClause.createdAt = {
      gte: startDate,
      lte: endDate,
    };
  }

  return prisma.referral.findMany({
    where: whereClause,
    orderBy: {
      createdAt: 'desc',
    },
  });
};

// Get all referral links for admin with pagination and filters
export const getAllReferralLinks = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    userType?: string;
    paymentStatus?: string;
    startDate?: string;
    endDate?: string;
  }
) => {
  // Calculate pagination
  const skip = (page - 1) * limit;

  // Build where clause for filtering
  const whereClause: any = {
    isActive: true,
  };

  // Add date range filter
  if (filters?.startDate || filters?.endDate) {
    whereClause.createdAt = {};
    if (filters.startDate) {
      whereClause.createdAt.gte = new Date(filters.startDate);
    }
    if (filters.endDate) {
      // Add 1 day to end date to include the entire end date
      const endDate = new Date(filters.endDate);
      endDate.setDate(endDate.getDate() + 1);
      whereClause.createdAt.lt = endDate;
    }
  }

  // Add user type filter
  if (filters?.userType) {
    whereClause.userType = filters.userType;
  }

  const allReferralLinks = await prisma.referralLink.findMany({
    where: whereClause,
    include: {
      referrals: {
        include: {
          earnings: true,
        },
      },
    },
  });

  // Sort by total referrals count (highest first), then by creation date
  const sortedLinks = allReferralLinks.sort((a, b) => {
    const aReferrals = a.referrals.length;
    const bReferrals = b.referrals.length;

    if (aReferrals !== bReferrals) {
      return bReferrals - aReferrals; // Descending order by referral count
    }

    // If referral counts are equal, sort by creation date (newest first)
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  // Apply client-side filtering for name and email (after enrichment)
  let filteredLinks = sortedLinks;

  if (filters?.firstName || filters?.lastName || filters?.email) {
    // We need to enrich all links first to filter by name/email
    const allEnrichedLinks = await Promise.all(
      sortedLinks.map(async (link) => {
        let userEmail = link.userId;
        let userName = '';

        try {
          if (link.userType === 'STUDENT') {
            const student = await prisma.student.findUnique({
              where: { id: link.userId },
              select: { email: true, firstName: true, lastName: true },
            });
            if (student) {
              userEmail = student.email;
              userName = `${student.firstName} ${student.lastName}`;
            }
          } else if (link.userType === 'CLASS') {
            const classUser = await prisma.classes.findUnique({
              where: { id: link.userId },
              select: { email: true, firstName: true, lastName: true },
            });
            if (classUser) {
              userEmail = classUser.email;
              userName = `${classUser.firstName} ${classUser.lastName}`;
            }
          } else if (link.userType === 'ADMIN') {
            if (link.userId.includes('|')) {
              const [email, name] = link.userId.split('|');
              userEmail = email;
              userName = name;
            } else {
              userEmail = link.userId;
              userName = 'Staff';
            }
          }
        } catch (error) {
          console.log('Error fetching user details:', error);
        }

        return {
          ...link,
          userEmail,
          userName,
        };
      })
    );

    // Apply name and email filters
    filteredLinks = allEnrichedLinks.filter((link) => {
      const nameParts = link.userName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const matchesFirstName = !filters.firstName ||
        firstName.toLowerCase().includes(filters.firstName.toLowerCase());
      const matchesLastName = !filters.lastName ||
        lastName.toLowerCase().includes(filters.lastName.toLowerCase());
      const matchesEmail = !filters.email ||
        link.userEmail.toLowerCase().includes(filters.email.toLowerCase());

      return matchesFirstName && matchesLastName && matchesEmail;
    });
  }

  // Apply payment status filter based on earnings amounts
  if (filters?.paymentStatus) {
    // First, we need to calculate earnings for all links to filter them
    const linksWithEarnings = await Promise.all(
      filteredLinks.map(async (link) => {
        const allEarnings = link.referrals.flatMap(r => r.earnings);
        const paidEarnings = allEarnings
          .filter(e => e.paymentStatus === 'PAID')
          .reduce((sum, e) => sum + e.amount, 0);
        const unpaidEarnings = allEarnings
          .filter(e => e.paymentStatus === 'UNPAID')
          .reduce((sum, e) => sum + e.amount, 0);

        return {
          ...link,
          calculatedPaidEarnings: paidEarnings,
          calculatedUnpaidEarnings: unpaidEarnings,
        };
      })
    );

    // Filter based on payment status and earnings amounts
    if (filters.paymentStatus === 'PAID') {
      // Show only links with paid earnings > 0
      filteredLinks = linksWithEarnings.filter(link => link.calculatedPaidEarnings > 0);
    } else if (filters.paymentStatus === 'UNPAID') {
      // Show only links with unpaid earnings > 0
      filteredLinks = linksWithEarnings.filter(link => link.calculatedUnpaidEarnings > 0);
    }
  }

  // Apply pagination after filtering
  const paginatedLinks = filteredLinks.slice(skip, skip + limit);

  // Get user details for each referral link
  const enrichedLinks = await Promise.all(
    paginatedLinks.map(async (link) => {
      let userEmail = link.userId; // Default fallback
      let userName = '';

      try {
        if (link.userType === 'STUDENT') {
          const student = await prisma.student.findUnique({
            where: { id: link.userId },
            select: { email: true, firstName: true, lastName: true },
          });
          if (student) {
            userEmail = student.email;
            userName = `${student.firstName} ${student.lastName}`;
          }
        } else if (link.userType === 'CLASS') {
          const classUser = await prisma.classes.findUnique({
            where: { id: link.userId },
            select: { email: true, firstName: true, lastName: true },
          });
          if (classUser) {
            userEmail = classUser.email;
            userName = `${classUser.firstName} ${classUser.lastName}`;
          }
        } else if (link.userType === 'ADMIN') {
          // Parse staff identifier: email|name
          if (link.userId.includes('|')) {
            const [email, name] = link.userId.split('|');
            userEmail = email;
            userName = name;
          } else {
            // Fallback for old format (just email)
            userEmail = link.userId;
            userName = 'Staff';
          }
        }
      } catch (error) {
        console.log('Error fetching user details:', error);
      }

      // Calculate earnings for this referral link
      const allEarnings = link.referrals.flatMap(r => r.earnings);
      const totalEarnings = allEarnings.reduce((sum, e) => sum + e.amount, 0);
      const paidEarnings = allEarnings
        .filter(e => e.paymentStatus === 'PAID')
        .reduce((sum, e) => sum + e.amount, 0);
      const unpaidEarnings = allEarnings
        .filter(e => e.paymentStatus === 'UNPAID')
        .reduce((sum, e) => sum + e.amount, 0);

      return {
        ...link,
        userEmail,
        userName,
        earnings: {
          totalEarnings,
          paidEarnings,
          unpaidEarnings,
        },
      };
    })
  );

  const totalCount = filteredLinks.length;
  const totalPages = Math.ceil(totalCount / limit);

  return {
    referralLinks: enrichedLinks,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};

// Create staff referral link (Admin only)
export const createStaffReferralLink = async (staffEmail: string, staffName: string) => {
  let code = generateReferralCode();

  // Ensure code is unique
  while (await prisma.referralLink.findUnique({ where: { code } })) {
    code = generateReferralCode();
  }

  // Store staff name in a format that can be retrieved later
  // We'll use a JSON format: email|name
  const staffIdentifier = `${staffEmail}|${staffName}`;

  return prisma.referralLink.create({
    data: {
      userId: staffIdentifier, // Store both email and name
      userType: 'ADMIN',
      code,
    },
  });
};

// Deactivate referral link
export const deactivateReferralLink = async (linkId: string) => {
  return prisma.referralLink.update({
    where: { id: linkId },
    data: { isActive: false },
  });
};

// Get referred users for a specific referral link (Admin only) with pagination
export const getReferredUsersByLinkId = async (linkId: string, page: number = 1, limit: number = 10) => {
  const referralLink = await prisma.referralLink.findUnique({
    where: { id: linkId },
  });

  if (!referralLink) {
    throw new Error('Referral link not found');
  }

  // Calculate pagination
  const skip = (page - 1) * limit;

  // Get total count
  const totalCount = await prisma.referral.count({
    where: { referralLinkId: linkId },
  });

  // Get paginated referrals
  const referrals = await prisma.referral.findMany({
    where: { referralLinkId: linkId },
    include: {
      earnings: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
  });

  // Get detailed user information for each referred user
  const enrichedReferrals = await Promise.all(
    referrals.map(async (referral) => {
      let firstName = '';
      let lastName = '';
      let email = referral.referredUserEmail;

      try {
        if (referral.referredUserType === 'STUDENT') {
          const student = await prisma.student.findUnique({
            where: { id: referral.referredUserId },
            select: { firstName: true, lastName: true, email: true },
          });
          if (student) {
            firstName = student.firstName;
            lastName = student.lastName;
            email = student.email;
          }
        } else if (referral.referredUserType === 'CLASS') {
          const classUser = await prisma.classes.findUnique({
            where: { id: referral.referredUserId },
            select: { firstName: true, lastName: true, email: true },
          });
          if (classUser) {
            firstName = classUser.firstName;
            lastName = classUser.lastName;
            email = classUser.email;
          }
        }
      } catch (error) {
        console.log('Error fetching referred user details:', error);
        // Fallback to stored name if available
        const nameParts = referral.referredUserName.split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      }

      return {
        id: referral.id,
        firstName,
        lastName,
        email,
        userType: referral.referredUserType,
        createdAt: referral.createdAt,
        earnings: referral.earnings.map(earning => ({
          id: earning.id,
          earningType: earning.earningType,
          amount: earning.amount,
          paymentStatus: earning.paymentStatus,
          examId: earning.examId,
        })),
      };
    })
  );

  const totalPages = Math.ceil(totalCount / limit);

  return {
    referralLink: {
      id: referralLink.id,
      code: referralLink.code,
      userType: referralLink.userType,
    },
    referredUsers: enrichedReferrals,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};

// Get all referral earnings for admin management
export const getAllReferralEarnings = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    paymentStatus?: 'PAID' | 'UNPAID';
    earningType?: 'REGISTRATION' | 'UWHIZ_APPLICATION';
    referralCode?: string;
  }
) => {
  const skip = (page - 1) * limit;

  const whereClause: any = {};

  if (filters?.paymentStatus) {
    whereClause.paymentStatus = filters.paymentStatus;
  }

  if (filters?.earningType) {
    whereClause.earningType = filters.earningType;
  }

  if (filters?.referralCode) {
    whereClause.referral = {
      referralLink: {
        code: filters.referralCode,
      },
    };
  }

  const totalCount = await prisma.referralEarning.count({
    where: whereClause,
  });

  const earnings = await prisma.referralEarning.findMany({
    where: whereClause,
    include: {
      referral: {
        include: {
          referralLink: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
  });

  const totalPages = Math.ceil(totalCount / limit);

  return {
    earnings,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};

// Update payment status for multiple earnings
export const updateEarningsPaymentStatus = async (
  earningIds: string[],
  paymentStatus: 'PAID' | 'UNPAID'
) => {
  return prisma.referralEarning.updateMany({
    where: {
      id: {
        in: earningIds,
      },
    },
    data: {
      paymentStatus,
    },
  });
};

// Get earnings summary for a specific referral link
export const getReferralLinkEarnings = async (referralLinkId: string) => {
  const earnings = await prisma.referralEarning.findMany({
    where: {
      referral: {
        referralLinkId,
      },
    },
    include: {
      referral: true,
    },
  });

  const totalEarnings = earnings.reduce((sum, e) => sum + e.amount, 0);
  const paidEarnings = earnings
    .filter(e => e.paymentStatus === 'PAID')
    .reduce((sum, e) => sum + e.amount, 0);
  const unpaidEarnings = earnings
    .filter(e => e.paymentStatus === 'UNPAID')
    .reduce((sum, e) => sum + e.amount, 0);
  const registrationEarnings = earnings
    .filter(e => e.earningType === 'REGISTRATION')
    .reduce((sum, e) => sum + e.amount, 0);
  const uwhizEarnings = earnings
    .filter(e => e.earningType === 'UWHIZ_APPLICATION')
    .reduce((sum, e) => sum + e.amount, 0);

  return {
    totalEarnings,
    paidEarnings,
    unpaidEarnings,
    registrationEarnings,
    uwhizEarnings,
    earningsCount: earnings.length,
    earnings,
  };
};

// Get overall earnings summary for admin dashboard
export const getOverallEarningsSummary = async () => {
  const allEarnings = await prisma.referralEarning.findMany({
    select: {
      amount: true,
      paymentStatus: true,
      earningType: true,
    },
  });

  const totalEarnings = allEarnings.reduce((sum, e) => sum + e.amount, 0);
  const paidEarnings = allEarnings
    .filter(e => e.paymentStatus === 'PAID')
    .reduce((sum, e) => sum + e.amount, 0);
  const unpaidEarnings = allEarnings
    .filter(e => e.paymentStatus === 'UNPAID')
    .reduce((sum, e) => sum + e.amount, 0);
  const registrationEarnings = allEarnings
    .filter(e => e.earningType === 'REGISTRATION')
    .reduce((sum, e) => sum + e.amount, 0);
  const uwhizEarnings = allEarnings
    .filter(e => e.earningType === 'UWHIZ_APPLICATION')
    .reduce((sum, e) => sum + e.amount, 0);

  return {
    totalEarnings,
    paidEarnings,
    unpaidEarnings,
    registrationEarnings,
    uwhizEarnings,
    totalEarningsCount: allEarnings.length,
  };
};
