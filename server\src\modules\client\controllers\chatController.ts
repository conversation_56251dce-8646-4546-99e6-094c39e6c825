import { Request, Response } from "express";
import * as chatQueryService from "../services/chatQueryService";

export const getPrivateMessagesController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId1, userId2 } = req.query;

    if (!userId1 || !userId2) {
      res.status(400).json({ error: "Both user IDs are required" });
      return;
    }

    const messages = await chatQueryService.getPrivateMessages(userId1 as string, userId2 as string);
    res.json(messages);
  } catch (error) {
    console.error("Error fetching private messages:", error);
    res.status(500).json({ error: "Failed to fetch private messages" });
  }
};

export const getMessageUsersController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, userType } = req.query;

    if (!userId || !userType) {
      res.status(400).json({ error: "User ID and user type are required" });
      return;
    }

    const users = await chatQueryService.getMessageUsers(userId as string, userType as 'student' | 'class');
    res.json(users);
  } catch (error) {
    console.error("Error fetching message users:", error);
    res.status(500).json({ error: "Failed to fetch message users" });
  }
};

export const getOnlineUsersController = (onlineUsers: Map<string, { socketId: string; userType: string; userId: string }>) => {
  return (_req: Request, res: Response) => {
    console.log('Online users:', onlineUsers);
    const users = Array.from(onlineUsers.entries()).map(([username, data]) => ({
      username,
      userType: data.userType,
      userId: data.userId
    }));
    res.json(users);
  };
};

export const getUnreadMessageUsersController = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, userType } = req.query;

    if (!userId || !userType) {
      res.status(400).json({ error: "User ID and user type are required" });
      return;
    }

    const unreadUsers = await chatQueryService.getUnreadMessageUsers(userId as string, userType as 'student' | 'class');
    res.json(unreadUsers);
  } catch (error) {
    console.error("Error fetching unread message users:", error);
    res.status(500).json({ error: "Failed to fetch unread message users" });
  }
};
