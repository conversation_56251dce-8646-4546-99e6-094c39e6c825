import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload } from 'jsonwebtoken';
import { sendError } from '@/utils/response';

const JWT_SECRET = process.env.JWT_SECRET || 'secret123';

export async function authClientMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<any> {
  const token = req.cookies.client_jwt;

  if (!token) {
    return sendError(res, 'Unauthorized: No token provided', 401);
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as JwtPayload & { id: string };
    req.class = decoded;
    next();
  } catch (err) {
    console.error(err);
    return sendError(res, 'Unauthorized: Invalid token', 401);
  }
}
