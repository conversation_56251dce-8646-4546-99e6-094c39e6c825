import { axiosInstance } from '@/lib/axios';

export const uwhizMockQuestionForStudent = async (
  studentId: string,
  medium: string,
  standard: string,
): Promise<any[]> => {
  try {
    const response = await axiosInstance.get(
      `mock-exam?studentId=${studentId}&medium=${medium}&standard=${standard}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(
      `Failed To Get Student Question Detail: ${error.response?.data?.message || error.message}`
    );
}
};