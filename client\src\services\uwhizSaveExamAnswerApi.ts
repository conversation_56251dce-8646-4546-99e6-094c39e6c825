import { axiosInstance } from '@/lib/axios';

export const saveUwhizAnswer = async (data: any) => {
  try {
    const response = await axiosInstance.post('/saveExamAnswer', data,{
       headers: {
        "Server-Select": "uwhizServer",
      },
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to store Answer: ${error.response?.data?.message || error.message}`,
    };
  }
};