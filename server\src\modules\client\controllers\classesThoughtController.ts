import { Request, Response } from 'express';
import * as classesThoughtService from '../services/classesThoughtServices';

export const createThought = async (req: Request, res: Response):Promise<void> => {
  try {
    const { classId, thoughts } = req.body;
    if (!classId || !thoughts) {
      res.status(400).json({ error: 'classId and thoughts are required' });
      return;
    }
    const thought = await classesThoughtService.createThought({ classId, thoughts });
    res.status(201).json(thought);
  } catch {
    res.status(500).json({ error: 'Failed to create thought' });
  }
};

export const getAllThoughts = async (req: Request, res: Response): Promise<void> => {
  try {
    const status = req.query.status as 'PENDING' | 'APPROVED' | 'REJECTED' | undefined;
    const classId = req.query.classId as string | undefined;
    const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
    const page = req.query.page ? parseInt(req.query.page as string, 10) : 1;

    if (status && !['PENDING', 'APPROVED', 'REJECTED'].includes(status)) {
      res.status(400).json({ error: 'Invalid status' });
      return;
    }

    if (classId && typeof classId !== 'string') {
      res.status(400).json({ error: 'classId must be a string' });
      return;
    }

    // Validate limit
    if (isNaN(limit)) {
      res.status(400).json({ error: 'limit must be a valid number' });
      return;
    }
    if (limit <= 0) {
      res.status(400).json({ error: 'limit must be a positive number' });
      return;
    }

    if(isNaN(page) || page <= 0){
      res.status(400).json({ error: 'page must be a positive number' });
      return;
    }

    // Fetch thoughts using the service with pagination
    const result = await classesThoughtService.getThought(status, classId, page, limit);
    res.status(200).json(result);
  } catch (error: any) {
    console.error('Error fetching thoughts:', error);
    res.status(500).json({ error: 'Failed to fetch thoughts' });
  }
};


export const getThoughtById = async (req: Request, res: Response):Promise<any> => {
  try {
    const { id } = req.params;
    const thought = await classesThoughtService.getThoughtById(id);
    if (!thought) {
      return res.status(404).json({ error: 'Thought not found' });
    }
    res.status(200).json(thought);
  } catch {
    res.status(500).json({ error: 'Failed to fetch thought' });
  }
};

export const updateThought = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { thoughts } = req.body;
    const thought = await classesThoughtService.updateThought(id, { thoughts });
    res.status(200).json(thought);
  } catch {
    res.status(500).json({ error: 'Failed to update thought' });
  }
};

export const deleteThought = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const thought = await classesThoughtService.deleteThought(id);
    res.status(200).json({ message: 'Thought deleted', thought });
  } catch {
    res.status(500).json({ error: 'Failed to delete thought' });
  }
};

export const updateThoughtStatus = async (req: Request, res: Response):Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    if (!['PENDING', 'APPROVED', 'REJECTED'].includes(status)) {
       res.status(400).json({ error: 'Invalid status' });
       return;
    }
    const thought = await classesThoughtService.updateThoughtStatus(id, status);
    res.status(200).json(thought);
  } catch {
    res.status(500).json({ error: 'Failed to update thought status' });
  }
};