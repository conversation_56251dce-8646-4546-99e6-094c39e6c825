'use client';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { IoIosArrowDown } from 'react-icons/io';

const FAQSection = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: 'What is a Super Class?',
      answer:
        'A class with more reviews and mostly liked by students. These classes have consistently high ratings and positive feedback from our learning community.',
    },
    {
      question: 'How to request a tutor replacement?',
      answer:
        'Simply apply for a tutor you like, and they will reach out to you as soon as possible. You can browse through our verified tutors and select based on their expertise and ratings.',
    },
    {
      question: 'How is the evaluation of classes done?',
      answer:
        'Classes are evaluated based on student feedback, attendance rates, learning outcomes, and overall engagement. We maintain strict quality standards to ensure the best learning experience.',
    },
    {
      question: 'How to upgrade or downgrade my subscription?',
      answer:
        "You can easily modify your subscription plan from your account dashboard. Navigate to 'Subscription Settings' and choose the plan that best suits your needs.",
    },
    {
      question: 'How does subscription payment work?',
      answer:
        'We offer flexible payment options including monthly and annual plans. Payments can be made securely through various methods including credit cards, debit cards, and digital wallets.',
    },
    {
      question: 'How do I register to teach on Uest?',
      answer:
        "Visit our 'Become a Teacher' section, complete the registration form, submit required documentation, and pass our quality assessment. We'll review your application within 48 hours.",
    },
    {
      question: 'How to choose a good profile photo?',
      answer:
        'Upload a clear, professional headshot with good lighting. Ensure your face is clearly visible and the photo is recent. Avoid using filters or heavily edited images.',
    },
    {
      question: 'How to withdraw your earnings?',
      answer:
        "Teachers can withdraw their earnings through the 'Earnings' section in their dashboard. Withdrawals are processed within 3-5 business days to your registered bank account.",
    },
    {
      question: 'How can we get our leads?',
      answer:
        'Leads are generated through our platform based on your subject expertise and availability. Complete your profile and maintain good ratings to increase your visibility to potential students.',
    },
    {
      question: 'What is the registration fee?',
      answer:
        'Registration fees vary based on the course type and duration. Check our pricing page for detailed information about current rates and any ongoing promotional offers.',
    },
  ];

  return (
    <section className="py-16 px-4 bg-white dark:bg-black">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <motion.span
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-orange-50 dark:bg-orange-900/30 text-customOrange px-4 py-2 rounded-full text-sm font-medium inline-block mb-4"
          >
            FAQs
          </motion.span>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl md:text-4xl font-bold"
          >
            Frequently Asked Questions
          </motion.h2>
        </motion.div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
            >
              <motion.button
                onClick={() => setActiveIndex(activeIndex === index ? null : index)}
                className="w-full px-6 py-4 text-left flex cursor-pointer justify-between items-center bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                whileHover={{ backgroundColor: 'rgba(253, 144, 75, 0.05)' }}
              >
                <span className="font-medium text-gray-900 dark:text-gray-100">{faq.question}</span>
                <motion.div
                  animate={{ rotate: activeIndex === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <IoIosArrowDown className="text-customOrange text-xl" />
                </motion.div>
              </motion.button>

              <AnimatePresence>
                {activeIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border-t border-gray-200 dark:border-gray-700"
                  >
                    <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50">
                      <p className="text-gray-600 dark:text-gray-300">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
