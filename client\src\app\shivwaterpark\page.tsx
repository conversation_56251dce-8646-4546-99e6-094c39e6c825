'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const ShivWaterparkPage = () => {
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('shiv_admin_token');
    
    if (token) {
      router.push('/shivwaterpark/dashboard');
    } else {
      router.push('/shivwaterpark/login');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading...</p>
      </div>
    </div>
  );
};

export default ShivWaterparkPage;