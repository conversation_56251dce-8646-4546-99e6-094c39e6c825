import prisma from '@/config/prismaClient';
import { UwhizPriceRank } from '@prisma/client';

export const createPriceRank = async (examId:number, rank:number, price:number) => {
  try {
    const existingRank = await prisma.uwhizPriceRank.findFirst({
      where: { examId, rank }
    });
    
    if (existingRank) {
      throw new Error('Rank already exists for this exam');
    }

    return await prisma.uwhizPriceRank.create({
      data: {
        examId,
        rank,
        price
      }
    });
  } catch (error:any) {
    throw new Error(`Failed to create price rank: ${error.message}`);
  }
};

export const getPriceRanksByExamId = async (examId: number) => {
  try {
    return await prisma.uwhizPriceRank.findMany({
      where: { examId },
      select: {
        id: true,
        rank: true,
        price: true,
        exam: {
          select: {
            exam_name: true
          }
        }
      },
      orderBy: { rank: 'asc' }
    });
  } catch (error: any) {
    throw new Error(`Failed to fetch price ranks: ${error.message}`);
  }
};

export const updatePriceRank = async (id: string, data: Partial<UwhizPriceRank>) => {
  try {
    const existingRank = await prisma.uwhizPriceRank.findUnique({
      where: { id }
    });

    if (!existingRank) {
      throw new Error('Price rank not found');
    }

    return await prisma.uwhizPriceRank.update({
      where: { id },
      data
    });
  } catch (error: any) {
    throw new Error(`Failed to update price rank: ${error.message}`);
  }
};

export const deletePriceRank = async (id: string) => {
  try {

    const existingRank = await prisma.uwhizPriceRank.findUnique({
      where: { id },
    });

    if (!existingRank) {
      throw new Error('Price rank not found');
    }

    const deletedExamId = existingRank.examId;

    await prisma.uwhizPriceRank.delete({
      where: { id },
    });

    const remainingRanks = await prisma.uwhizPriceRank.findMany({
      where: { examId: deletedExamId },
      orderBy: { rank: 'asc' },
    });

    for (let i = 0; i < remainingRanks.length; i++) {
      const newRank = i + 1; 
      if (remainingRanks[i].rank !== newRank) {
        await prisma.uwhizPriceRank.update({
          where: { id: remainingRanks[i].id },
          data: { rank: newRank },
        });
      }
    }

    return { message: 'Price rank deleted and ranks renumbered successfully' };
  } catch (error: any) {
    throw new Error(`Failed to delete price rank: ${error.message}`);
  }
};