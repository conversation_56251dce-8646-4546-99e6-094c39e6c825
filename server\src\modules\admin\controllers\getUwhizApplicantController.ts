import { Request, Response } from "express";
import {getUserDetails}  from "../services/getUwhizApplicantServices";

export const getUserDetailsController = async (req: Request, res: Response) => {
  try {
    const { examType, applicantIds } = req.body;

    const data = await getUserDetails(examType, applicantIds);
    res.json(data);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error fetching user details" });
  }
};
