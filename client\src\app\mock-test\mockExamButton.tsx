"use client";

import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { isAttemptAllowed } from "./restictExamAttempt";

interface MockExamButtonProps {
  hasApplied: boolean;
}

export default function MockExamButton({ hasApplied }: MockExamButtonProps) {
  const router = useRouter();
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [remainingHours, setRemainingHours] = useState<number | null>(null);
  const [studentId, setStudentId] = useState<string | null>(null);

  // Fetch studentId from localStorage
  useEffect(() => {
    try {
      const data = localStorage.getItem("student_data");
      const fetchedStudentId = data ? JSON.parse(data).id : null;
      setStudentId(fetchedStudentId);
    } catch (error) {
      console.error("Error retrieving studentId:", error);
      setStudentId(null);
    }
  }, []);

  // Check exam attempt eligibility
  useEffect(() => {
    const checkAttempt = () => {
      if (studentId) {
        const { allowed, remainingHours } = isAttemptAllowed(studentId);
        setIsButtonDisabled(!allowed);
        setRemainingHours(remainingHours);
      } else {
        setIsButtonDisabled(true);
      }
    };

    checkAttempt();
    const intervalId = setInterval(checkAttempt, 60 * 1000);

    return () => clearInterval(intervalId);
  }, [studentId]);

  const handleMockExam = () => {
    if (!studentId) {
      toast.error("Please log in to attempt the exam.");
      router.push("/login");
      return;
    }

    const { allowed, remainingHours } = isAttemptAllowed(studentId);
    if (!allowed && remainingHours) {
      toast.error(`You can attempt the exam again after ${remainingHours} hours.`);
      return;
    }
    router.push("/mock-test");
  };

  return (
    <div className="flex flex-col">
      {hasApplied ? (
        <Button
          className="w-1/2 mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          onClick={handleMockExam}
          disabled={isButtonDisabled || !studentId}
        >
          Try Mock Exam
        </Button>
      ) : (
        <Button
          className="w-1/2 mx-auto bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
          onClick={handleMockExam}
          disabled
        >
          Try Mock Exam
        </Button>
      )}
      {isButtonDisabled && remainingHours && studentId && (
        <p className="text-red-500 text-sm mt-2 mx-auto">
          You can attempt the exam again in {remainingHours} hours.
        </p>
      )}
    </div>
  );
}