import prisma from '@/config/prismaClient';
import { Request, Response } from 'express';

export const getTransactions = async (req: Request, res: Response): Promise<any> => {
  const classId = req.class?.id;
  const studentId = req.student?.id;

  try {
    const transactions = await prisma.uestCoinTransaction.findMany({
      where: {
        modelId: classId ? classId : studentId,
        modelType: classId ? 'CLASS' : 'STUDENT',
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return res.status(200).json({
      transactions,
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return res.status(500).json({
      message: 'Internal server error',
    });
  }
};
