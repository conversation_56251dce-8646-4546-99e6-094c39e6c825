'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { X } from 'lucide-react';

interface CustomModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

const CustomModal = ({ isOpen, onClose, children, className }: CustomModalProps) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-[100] flex items-center justify-center bg-black/50 overflow-y-auto"
      onClick={onClose}
    >
      <div
        className={cn(
          'bg-white text-zinc-900 dark:bg-zinc-900 rounded-2xl p-6 sm:p-8 max-w-md sm:max-w-lg w-[95%] mx-auto my-4 relative',
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute top-4 right-4 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
          onClick={onClose}
          aria-label="Close modal"
        >
          <X className="h-6 w-6 text-gray-500 dark:text-gray-400" />
        </button>
        {children}
      </div>
    </div>
  );
};

export default CustomModal;
