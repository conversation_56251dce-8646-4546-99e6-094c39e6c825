import { Request, Response } from 'express';
import * as studentProfileService from '../services/uwhizStudentDetailServices';

export const getStudentProfileById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { studentId } = req.params;
    const profile = await studentProfileService.getStudentProfileByStudentId(studentId);
    res.status(200).json({
      success: true,
      data: profile,
    });
  } catch (error: any) {
    res.status(404).json({
      success: false,
      message: error.message || 'Error retrieving student profile',
    });
  }
};