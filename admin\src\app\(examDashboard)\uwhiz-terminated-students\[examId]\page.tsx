'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getTerminatedStudents  } from '@/services/uwhizQuizTerminationLogApi';
import { createColumnHelper, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import {  terminatedStudents,ApiResponse } from '@/lib/types';
import { toast } from 'sonner';


const columnHelper = createColumnHelper<terminatedStudents>();

const columns = [
    columnHelper.accessor(row => `${row.firstName} ${row.lastName}`, {
        header: 'Name',
        cell: info => info.getValue(),
    }),
    columnHelper.accessor('email', {
        header: 'Email',
        cell: info => info.getValue(),
    }),
    columnHelper.accessor('reason', {
        header: 'Reason',
        cell: info => info.getValue(),
    }),

    columnHelper.accessor('createdAt', {
        header: 'Terminated At',
        cell: info => new Date(info.getValue()).toLocaleString('en-IN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }),
    }),
];

export default function ExamApplicantsTable() {
    const { examId } = useParams();
    const [data, setData] = useState<terminatedStudents[]>([]);
    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(1);
    const limit = 10;

    useEffect(() => {
        const fetchData = async () => {
            if (typeof examId !== 'string') return;
            const response: ApiResponse = await getTerminatedStudents(parseInt(examId), page, limit);
            if (response.success === false) {
                return;
            }
            if (Array.isArray(response.data) && response.data.every(item => 'reason' in item)) {
                setData(response.data as terminatedStudents[]);
            } else {
                setData([]);
                toast.error('Invalid data received for terminated students.');
            }
            setTotal(response.total);
        };
        fetchData();
    }, [examId, page]);


    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
    });

    const totalPages = Math.ceil(total / limit);
    const isDataEmpty = data.length === 0;


    return (
        <div className="p-4">
            <div className="overflow-x-auto rounded-lg shadow-sm">
                <div className="flex justify-between items-center pb-5">
                    <h1 className="text-2xl font-bold">Terminated Students</h1>
                </div>
                <table className="min-w-full border border-gray-300 rounded-lg">
                    <thead>
                        {table.getHeaderGroups().map(headerGroup => (
                            <tr key={headerGroup.id}>
                                {headerGroup.headers.map(header => (
                                    <th key={header.id} className="border-b border-gray-300 px-4 py-2 text-left text-sm font-medium bg-gray-50">
                                        {flexRender(header.column.columnDef.header, header.getContext())}
                                    </th>
                                ))}
                            </tr>
                        ))}
                    </thead>
                    <tbody>
                        {isDataEmpty ? (
                            <tr>
                                <td colSpan={columns.length} className="px-4 py-2 text-sm text-center text-gray-600">
                                    No Applicant Data
                                </td>
                            </tr>
                        ) : (
                            table.getRowModel().rows.map(row => (
                                <tr key={row.id} className="border-b border-gray-300">
                                    {row.getVisibleCells().map(cell => (
                                        <td key={cell.id} className="px-4 py-2 text-sm">
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </td>
                                    ))}
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
            <div className="flex justify-between items-center mt-4">
                <span className="text-sm text-gray-600">
                    {total} entries
                </span>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => setPage(1)}
                        disabled={page === 1 || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'<<'}
                    </button>
                    <button
                        onClick={() => setPage(page - 1)}
                        disabled={page === 1 || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'<'}
                    </button>
                    <span className="text-sm">
                        Page {page} of {totalPages}
                    </span>
                    <button
                        onClick={() => setPage(page + 1)}
                        disabled={page === totalPages || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'>'}
                    </button>
                    <button
                        onClick={() => setPage(totalPages)}
                        disabled={page === totalPages || isDataEmpty}
                        className="px-3 py-1 border rounded disabled:opacity-50"
                    >
                        {'>>'}
                    </button>
                </div>
            </div>
        </div>
    );
}