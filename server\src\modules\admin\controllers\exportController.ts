import { Request, Response } from 'express';
import * as XLSX from 'xlsx';
import { fetchClassesForExport, fetchExamApplicationsForExport, fetchStudentsForExport } from '../services/exportService';

const generateExcelResponse = (
  data: any[],
  worksheetName: string,
  fileName: string,
  res: Response
) => {
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(data);
  XLSX.utils.book_append_sheet(workbook, worksheet, worksheetName);
  const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

  res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  );
  res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);

  res.send(excelBuffer);
};

export const exportClassesToExcel = async (req: Request, res: Response): Promise<void> => {
  try {
    const status = (req.query.status as string) || '';
    const search = (req.query.search as string) || '';
    const filterType = (req.query.filterType as string) || 'all';

    const classes = await fetchClassesForExport(status, search, filterType);

    const formattedData = classes.map((cls: any) => ({
      'User Name': cls.username || 'N/A',
      'First Name': cls.firstName || 'N/A',
      'Last Name': cls.lastName || 'N/A',
      'Contact No': cls.contactNo || 'N/A',
      Email: cls.email || 'N/A',
      'Class Name': cls.className || 'N/A',
      Status: cls.status?.status?.toLowerCase() || 'PROFILE NOT COMPLETED',
      'Created At': cls.createdAt ? new Date(cls.createdAt).toLocaleDateString() : 'N/A',
    }));

    generateExcelResponse(formattedData, 'Classes', 'classes.xlsx', res);
  } catch (error: any) {
    console.error('Error exporting classes to Excel:', error);
    res.status(500).json({
      success: false,
      message: 'Error exporting classes to Excel',
      error: error.message,
    });
  }
};

export const exportExamApplicationsToExcel = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 1000;
    const search = (req.query.search as string) || '';
    const filterType = (req.query.filterType as string) || 'all';

    const applications = await fetchExamApplicationsForExport(page, limit, search, filterType);

    const formattedData = applications.map((app: any) => ({
      'Exam Name': app.exam.exam_name || 'N/A',
      'Class Name': app.class.className || 'N/A',
      'Contact No': app.class.contactNo || 'N/A',
      Email: app.class.email || 'N/A',
      Name: `${app.class.firstName || ''} ${app.class.lastName || ''}`.trim() || 'N/A',
      'Applied At': app.createdAt ? new Date(app.createdAt).toLocaleDateString() : 'N/A',
    }));

    generateExcelResponse(formattedData, 'Exam Applications', 'exam-applications.xlsx', res);
  } catch (error: any) {
    console.error('Error exporting exam applications to Excel:', error);
    res.status(500).json({
      success: false,
      message: 'Error exporting exam applications to Excel',
      error: error.message,
    });
  }
};

export const exportStudentsToExcel = async (req: Request, res: Response): Promise<void> => {
  try {
    const name = (req.query.name as string) || '';
    const email = (req.query.email as string) || '';
    const contact = (req.query.contact as string) || '';
    const status = (req.query.status as string) || '';

    const students = await fetchStudentsForExport(name, email, contact, status);

    const formattedData = students.map((student: any) => ({
      'First Name': student.firstName || 'N/A',
      'Last Name': student.lastName || 'N/A',
      'Email': student.email || 'N/A',
      'Contact Number': student.contact || 'N/A',
      'Uest Coins': student.coins || 0,
      'Verification Status': student.isVerified ? 'Verified' : 'Not Verified',
      'Profile Status': student.profile?.status || 'PROFILE NOT COMPLETED',
      'Medium': student.profile?.medium || 'N/A',
      'Classroom': student.profile?.classroom || 'N/A',
      'Birthday': student.profile?.birthday ? new Date(student.profile.birthday).toLocaleDateString() : 'N/A',
      'School': student.profile?.school || 'N/A',
      'Address': student.profile?.address || 'N/A',
      'Created At': student.createdAt ? new Date(student.createdAt).toLocaleDateString() : 'N/A',
    }));

    generateExcelResponse(formattedData, 'Students', 'students.xlsx', res);
  } catch (error: any) {
    console.error('Error exporting students to Excel:', error);
    res.status(500).json({
      success: false,
      message: 'Error exporting students to Excel',
      error: error.message,
    });
  }
};