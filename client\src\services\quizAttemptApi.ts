// services/quizAttemptApi.ts
import { axiosInstance } from '@/lib/axios';

export const hasAttemptedExam = async (examId: number, userId: string): Promise<boolean> => {
  try {
    const response = await axiosInstance.get(`/api/quiz-attempt/has-attempted/${examId}/${userId}`);
    return response.data.hasAttempted;
  } catch (error: any) {
    console.error('Error checking exam attempt:', error);
    return false;
  }
};
