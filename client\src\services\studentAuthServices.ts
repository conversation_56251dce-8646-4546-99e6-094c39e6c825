import {
    StudentRegisterData,
    StudentLoginData,
    AuthResponse,
    GoogleAuthData,
} from '@/lib/types';
import { axiosInstance } from '@/lib/axios';

export const registerStudent = async (StudentRegisterData: StudentRegisterData) => {
    const response = await axiosInstance.post(`/student/register`, StudentRegisterData);
    return response.data
}

export const loginStudent = async (StudentLoginData: StudentLoginData) => {
    const response = await axiosInstance.post(`/student/login`, StudentLoginData);
    return response.data;
}

export const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {
    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);
    return response.data;
};

export const logoutStudent = async (): Promise<AuthResponse> => {
    // Clear student token from localStorage
    localStorage.removeItem('studentToken');
    localStorage.removeItem('student_data');

    return {
        success: true,
        message: 'Logged out successfully',
    };
};

export const forgotPassword = async (email: string) => {
    const response = await axiosInstance.post(`/student/forgot-password`, { email });
    return response.data;
}

export const studentverifyEmail = async (token: string) => {
    const response = await axiosInstance.post(`/student/verify-email`, { token });
    return response.data;
};

export const studentresendVerificationEmail = async (email: string) => {
    const response = await axiosInstance.post(`/student/resend-verification`, { email });
    return response.data;
};

export const resetStudentPassword = async (email: string, token: string, newPassword: string) => {
    const response = await axiosInstance.post(`/student/reset-password`, {
        email,
        token,
        newPassword,
    });
    return response.data;
};