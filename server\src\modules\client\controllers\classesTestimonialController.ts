import { Request, Response } from 'express';
import { createTestimonial, getTestimonialsByClassId, updateTestimonialStatus, getAllTestimonials, getApprovedTestimonials, getClassDetails, deletetestimonials } from '../services/classesTestimonialServices';
import { Status } from '@prisma/client';

export const createTestimonialHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { classId, message, rating } = req.body;

    if (!classId || !message || !rating) {
      res.status(400).json({ message: 'Missing required fields' });
      return;
    }

    if (rating < 1 || rating > 5) {
      res.status(400).json({ message: 'Rating must be between 1 and 5' });
      return;
    }


    const classDetails = await getClassDetails(classId);
    if (!classDetails) {
      res.status(404).json({ message: 'Class not found' });
      return;
    }

    const testimonial = await createTestimonial({
      classId,
      message,
      rating
    });

    res.status(201).json(testimonial);
  } catch (error) {
    console.error('Error creating testimonial:', error);
    res.status(500).json({ message: 'Failed to create testimonial' });
  }
};

export const getClassTestimonialsHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { classId } = req.params;

    const classDetails = await getClassDetails(classId);
    if (!classDetails) {
      res.status(404).json({ message: 'Class not found' });
      return;
    }

    const testimonials = await getTestimonialsByClassId(classId);

    const formattedTestimonials = testimonials.map(testimonial => ({
      ...testimonial,
      class: {
        ...testimonial.class,
        id: testimonial.class.id, // Explicitly include the class ID
        className: testimonial.class.className || `${classDetails.firstName} ${classDetails.lastName}'s Class`,
        classesLogo: testimonial.class.ClassAbout?.classesLogo || null,
        profilePhoto: testimonial.class.ClassAbout?.profilePhoto || null,
        fullName: `${testimonial.class.firstName} ${testimonial.class.lastName}`
      }
    }));

    res.status(200).json(formattedTestimonials);
  } catch (error) {
    console.error('Error getting class testimonials:', error);
    res.status(500).json({ message: 'Failed to get testimonials' });
  }
};

export const updateTestimonialStatusHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!Object.values(Status).includes(status)) {
      res.status(400).json({ message: 'Invalid status' });
      return;
    }

    const testimonial = await updateTestimonialStatus(id, status);
    res.status(200).json(testimonial);
  } catch (error) {
    console.error('Error updating testimonial status:', error);
    res.status(500).json({ message: 'Failed to update testimonial status' });
  }
};

export const getAllTestimonialsHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = Number(req.query.page) || 1;
    const limit = Number(req.query.limit) || 10;
    const status = req.query.status as Status | undefined;

    const result = await getAllTestimonials(page, limit, status);
    res.status(200).json(result);
  } catch (error) {
    console.error('Error getting all testimonials:', error);
    res.status(500).json({ message: 'Failed to get testimonials' });
  }
};

export const getApprovedTestimonialsHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const limit = Number(req.query.limit) || 10;

    const testimonials = await getApprovedTestimonials(limit);
    res.status(200).json(testimonials);
  } catch (error) {
    console.error('Error getting approved testimonials:', error);
    res.status(500).json({ message: 'Failed to get approved testimonials' });
  }
};

export const deleteTestimonialHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    await deletetestimonials(id);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting testimonial:', error);
    res.status(500).json({ message: 'Failed to delete testimonial' });
  }
};