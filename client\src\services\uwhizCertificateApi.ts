import { axiosInstance } from '@/lib/axios';
interface Participant {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface ApiResponse {
  success: boolean;
  data?: Participant[];
  error?: string;
}

export const getExamParticipants = async (
  examId: number,
  classId: string
): Promise<ApiResponse> => {
  try {
    const response = await axiosInstance.get(
      `/certificate/exam-participants?examId=${examId}&classId=${classId}`
    );
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to get participants: ${error.response?.data?.message || error.message}`,
    };
  }
};

//Rank Certificate

export async function getUserRankings(
  examId: number,
  userId: string
): Promise<any | { success: false; error: string }> {
  try {
    const response = await axiosInstance.get(`/ranking/${examId}/${userId}`);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch rankings: ${error.response?.data?.message || error.message}`,
    };
  }
}
