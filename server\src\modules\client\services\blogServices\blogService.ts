import prisma from "@/config/prismaClient";
import { Status } from "@prisma/client";
import { CreateBlogInput, UpdateBlogInput, UpdateBlogStatusInput } from "../../requests/blogRequests/blogRequest";

interface BlogFilterOptions {
  page?: number;
  limit?: number;
  status?: Status;
}

export const createBlogService = async (data: CreateBlogInput & { classId?: string }) => {
  try {
    const blogData = {
      blogTitle: data.blogTitle,
      blogDescription: data.blogDescription,
      blogImage: data.blogImage || "",
      status: Status.PENDING,
    };

    if (data.classId) {
      const classExists = await prisma.classes.findUnique({
        where: { id: data.classId }
      });

      if (!classExists) {
        throw new Error(`Class with ID ${data.classId} not found`);
      }

      try {
        return await prisma.blog.create({
          data: {
            ...blogData,
            class: {
              connect: {
                id: data.classId
              }
            }
          },
        });
      } catch (error) {
        console.log("error in create a blog with ID",error)
        try {
          return await prisma.blog.create({
            data: {
              ...blogData,
              classId: data.classId
            } as any,
          });
        } catch (error) {
          console.log("error in create blog",error)
          return await prisma.blog.create({
            data: blogData as any
          });
        }
      }
    } else {
      return await prisma.blog.create({
        data: blogData as any
      });
    }
  } catch (error) {
    throw error;
  }
};

export const getAllBlogsService = async (options: BlogFilterOptions = {}) => {
  const { page = 1, limit = 10, status } = options;
  const skip = (page - 1) * limit;

  const where = status ? { status } : {};

  const [blogs, total] = await Promise.all([
    prisma.blog.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true
          }
        }
      }
    }),
    prisma.blog.count({ where }),
  ]);

  return {
    blogs,
    total,
    totalPages: Math.ceil(total / limit),
    currentPage: page,
  };
};

export const getBlogById = async (id: string) => {
  return await prisma.blog.findUnique({
    where: { id },
    include: {
      class: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          className: true
        }
      }
    }
  });
};

export const updateBlogService = async (id: string, data: UpdateBlogInput) => {
  const updateData: any = {};

  if (data.blogTitle) updateData.blogTitle = data.blogTitle;
  if (data.blogDescription) updateData.blogDescription = data.blogDescription;
  if (data.blogImage) updateData.blogImage = data.blogImage;
  if (data.status) updateData.status = data.status;

  return await prisma.blog.update({
    where: { id },
    data: updateData,
  });
};

export const updateBlogStatusService = async (id: string, data: UpdateBlogStatusInput) => {
  return await prisma.blog.update({
    where: { id },
    data,
  });
};

export const deleteBlogService = async (id: string) => {
  return await prisma.blog.delete({
    where: { id },
  });
};

export const getApprovedBlogsService = async (options: BlogFilterOptions = {}) => {
  const { page = 1, limit = 10 } = options;
  const skip = (page - 1) * limit;

  const [blogs, total] = await Promise.all([
    prisma.blog.findMany({
      where: {
        status: Status.APPROVED,
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true
          }
        }
      }
    }),
    prisma.blog.count({
      where: {
        status: Status.APPROVED,
      },
    }),
  ]);

  return {
    blogs,
    total,
    totalPages: Math.ceil(total / limit),
    currentPage: page,
  };
};

export const getBlogsByClassId = async (classId: string, options: BlogFilterOptions = {}) => {
  const { page = 1, limit = 10, status } = options;
  const skip = (page - 1) * limit;

  const where: any = { classId };
  if (status) {
    where.status = status;
  }

  const [blogs, total] = await Promise.all([
    prisma.blog.findMany({
      where,
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true
          }
        }
      }
    }),
    prisma.blog.count({ where }),
  ]);

  return {
    blogs,
    total,
    totalPages: Math.ceil(total / limit),
    currentPage: page,
  };
};
