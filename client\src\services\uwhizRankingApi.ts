import { axiosInstance } from '@/lib/axios';
import { Ranking } from '@/lib/types';

export async function getExamRankings(
  examId: number
): Promise<Ranking[] | { success: false; error: string }> {
  try {
    const response = await axiosInstance.get(`/ranking/${examId}`);
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: `Failed to fetch rankings: ${error.response?.data?.message || error.message}`,
    };
  }
}
