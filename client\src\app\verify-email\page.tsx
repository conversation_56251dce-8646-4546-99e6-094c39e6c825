'use client';

import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import { Dialog } from '@/components/ui/dialog';
import { verifyEmail } from '@/services/AuthService';
import { studentverifyEmail } from '@/services/studentAuthServices';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState, Suspense } from 'react';

const EmailVerificationContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const userType = searchParams.get('userType') || 'teacher';
  const [verifyStatus, setVerifyStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('');

  console.log("Token that is", token);
  console.log("User type:", userType);

  useEffect(() => {
    const verifyUserEmail = async () => {
      if (!token) {
        setVerifyStatus('error');
        setMessage('No verification token');
        return;
      }

      try {
        let response;
        if (userType === 'student') {
          response = await studentverifyEmail(token);
        } else {
          response = await verifyEmail(token);
        }

        console.log("Verification response:", response);
        setVerifyStatus('success');
        setMessage(response.message || 'Email verified successfully!');

        setTimeout(() => {
          router.push("/");
        }, 2000);
      } catch (error) {
        console.error("Verification error:", error);
        setVerifyStatus('error');
        setMessage('Failed to verify email. The token is invalid or expired.');
      }
    };

    verifyUserEmail();
  }, [token, userType, router]);

  return (
    <div className="flex justify-center items-center min-h-screen">
      <Dialog>
        {verifyStatus === 'loading' && (
          <h1 className="text-3xl font-bold">
            Verifying your email...
          </h1>
        )}

        {verifyStatus === 'success' && (
          <h1 className="text-3xl font-bold">
            Your Email is
            <span className="text-orange-500 italic"> Verified</span>
            <p className="text-sm mt-2 text-gray-600">{message}</p>
          </h1>
        )}

        {verifyStatus === 'error' && (
          <h1 className="text-3xl font-bold">
            Verification
            <span className="text-red-500 italic"> Failed</span>
            <p className="text-sm mt-2 text-gray-600">{message}</p>
          </h1>
        )}
      </Dialog>
    </div>
  );
};

// Main Page component with Suspense boundary
const Page = () => {
  return (
    <>
      <Header />
      <Suspense fallback={<div>Loading verification...</div>}>
        <EmailVerificationContent />
      </Suspense>
      <Footer />
    </>
  );
};

export default Page;