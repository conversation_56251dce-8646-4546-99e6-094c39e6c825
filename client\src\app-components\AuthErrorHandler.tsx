'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { useDispatch } from 'react-redux';
import { clearUser } from '@/store/slices/userSlice';

const AuthErrorHandler = () => {
  const searchParams = useSearchParams();
  const authError = searchParams.get('authError');
  const dispatch = useDispatch();

  useEffect(() => {
    if (authError === '1') {
      toast.error('Login Expired, Please login to continue');
      dispatch(clearUser());
    }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authError]);

  return null;
};

export default AuthErrorHandler;
