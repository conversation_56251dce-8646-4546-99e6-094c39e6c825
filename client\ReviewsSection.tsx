"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>, Trash2, Filter } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Testimonial } from '@/lib/types';


import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { getReviewsByClassId, createReview, deleteReview } from "@/services/reviewsApi";
import { isStudentAuthenticated } from "@/lib/utils";

const reviewFormSchema = z.object({
  message: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(500, "Message cannot exceed 500 characters"),
  rating: z.number().min(1, "Please select a rating"),
  classId: z.string().min(1, "Class ID is required"),
});

type ReviewFormValues = z.infer<typeof reviewFormSchema>;

interface ReviewsSectionProps {
  classId: string;
  userData: any;
  onReviewSubmit?: () => void;
}

const ReviewsSection: React.FC<ReviewsSectionProps> = ({ classId, userData, onReviewSubmit }) => {
  const [rating, setRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reviews, setReviews] = useState<Testimonial[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<'ALL' | 'ME'>('ALL');
  const [filteredReviews, setFilteredReviews] = useState<Testimonial[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);
  useEffect(() => {
    setIsStudentLoggedIn(isStudentAuthenticated());

    const handleStorageChange = () => {
      setIsStudentLoggedIn(isStudentAuthenticated());
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  const form = useForm<ReviewFormValues>({
    resolver: zodResolver(reviewFormSchema),
    defaultValues: {
      message: "",
      rating: 0,
      classId: classId,
    },
  });

  const fetchReviews = async (page = 1, filterType: 'ALL' | 'ME' = 'ALL') => {
    try {
      const response = await getReviewsByClassId(classId, page, 5);

      let userFullName = '';
      const studentDataStr = localStorage.getItem('student_data');

      if (studentDataStr) {
        try {
          const studentData = JSON.parse(studentDataStr);
          if (studentData.firstName && studentData.lastName) {
            userFullName = `${studentData.firstName} ${studentData.lastName}`;
          }
        } catch (e) {
          console.error('Error parsing student data:', e);
        }
      }

      if (!userFullName && userData && userData.firstName && userData.lastName) {
        userFullName = `${userData.firstName} ${userData.lastName}`;
      }

      const processedReviews = response.reviews.map((review: any) => {
        if (review.studentName && !review.message.startsWith(review.studentName)) {
          return {
            ...review,
            message: `${review.studentName}: ${review.message}`
          };
        }
        return review;
      });

      if (page === 1) {
        if (filterType === 'ALL') {
          setReviews(processedReviews);
          setFilteredReviews(processedReviews);
        } else if (filterType === 'ME' && userFullName) {
          const filtered = processedReviews.filter((review: Testimonial) => {
            if (review.studentId) {
              const currentStudentData = JSON.parse(studentDataStr || '{}');
              return review.studentId === currentStudentData.id;
            } else {
              const author = review.message.split(':')[0];
              return author === userFullName;
            }
          });
          setFilteredReviews(filtered);
        }
      } else {
        if (filterType === 'ALL') {
          setReviews(prev => [...prev, ...processedReviews]);
          setFilteredReviews(prev => [...prev, ...processedReviews]);
        } else if (filterType === 'ME' && userFullName) {
          const filtered = processedReviews.filter((review: Testimonial) => {
            if (review.studentId) {
              const currentStudentData = JSON.parse(studentDataStr || '{}');
              return review.studentId === currentStudentData.id;
            } else {
              const author = review.message.split(':')[0];
              return author === userFullName;
            }
          });
          setFilteredReviews(prev => [...prev, ...filtered]);
        }
      }

      setHasMore(response.hasMore);
      setCurrentPage(response.currentPage);

      return response;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to fetch reviews');
      return null;
    }
  };

  const handleLoadMore = async () => {
    setIsLoadingMore(true);
    try {
      await fetchReviews(currentPage + 1, filterType);
    } finally {
      setIsLoadingMore(false);
    }
  };

  useEffect(() => {
    setCurrentPage(1);
    fetchReviews(1, filterType);
  }, [filterType, classId]);

  const openDeleteDialog = (reviewId: string) => {
    setReviewToDelete(reviewId);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteTestimonial = async () => {
    if (!reviewToDelete) {
      console.error('No review ID to delete');
      return;
    }

    try {
      await deleteReview(reviewToDelete);
      toast.success('Review deleted successfully!');

      await fetchReviews(1, filterType);

      if (onReviewSubmit) {
        onReviewSubmit();
      }
    } catch (error: any) {
      console.error('Error deleting review:', error);
      toast.error(error.message || 'Failed to delete review');
    } finally {
      setIsDeleteDialogOpen(false);
      setReviewToDelete(null);
    }
  };

  const handleStarClick = (selectedRating: number) => {
    setRating(selectedRating);
    form.setValue("rating", selectedRating);
  };

  const onSubmit = async (values: ReviewFormValues) => {
    setIsSubmitting(true);

    try {
      const studentDataStr = localStorage.getItem('student_data');
      let studentName = '';
      let studentId = '';

      if (studentDataStr) {
        try {
          const studentData = JSON.parse(studentDataStr);
          if (studentData.firstName && studentData.lastName) {
            studentName = `${studentData.firstName} ${studentData.lastName}`;
            studentId = studentData.id;
          }
        } catch (e) {
          console.error('Error parsing student data:', e);
        }
      }

      if (!studentName && userData && userData.firstName && userData.lastName) {
        studentName = `${userData.firstName} ${userData.lastName}`;
      }

      if (!isStudentAuthenticated()) {
        toast.error('Only students can submit reviews');
        return;
      }

      const reviewData = {
        classId: values.classId,
        rating: values.rating,
        message: values.message,
        studentName: studentName,
        studentId: studentId
      };

      await createReview(reviewData);

      toast.success('Review submitted successfully!');

      // Reset form
      form.reset({
        message: "",
        rating: 0,
        classId: classId
      });
      setRating(0);

      await fetchReviews(1, filterType);

      if (onReviewSubmit) {
        onReviewSubmit();
      }
    } catch (error: any) {
      console.error('Error submitting review:', error);
      toast.error(error.message || 'Failed to submit review');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent className="dark:bg-siderbar">
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete your testimonial.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteTestimonial}
              className="bg-red-500 text-white hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <div className="w-full max-w-9xl mx-auto">
        {isStudentLoggedIn ? (
          <div className="dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-6">Write a Review</h2>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-2">Your Rating</label>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => handleStarClick(star)}
                        className="focus:outline-none"
                      >
                        <Star
                          className={`w-8 h-8 ${star <= rating
                            ? 'fill-[#FD904B] text-[#FD904B]'
                            : 'text-gray-300'
                            }`}
                        />
                      </button>
                    ))}
                  </div>
                  {form.formState.errors.rating && (
                    <p className="text-red-500 text-sm mt-1">{form.formState.errors.rating.message}</p>
                  )}
                </div>

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem className="mb-6">
                      <FormLabel className="block text-sm font-medium mb-2">Your Message</FormLabel>
                      <FormControl>
                        <textarea
                          {...field}
                          className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FD904B] focus:border-transparent"
                          rows={4}
                          placeholder="Share your experience (10-500 characters)..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[#FD904B] text-white py-2 px-4 rounded-lg hover:bg-[#FD904B]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Review'}
                </button>
              </form>
            </Form>
          </div>
        ) : (
          <div className="dark:bg-siderbar border rounded-lg shadow-md p-6 mb-8 text-center">
            <p className="text-lg mb-4">Please log in as a student to write a review</p>
          </div>
        )}

        <div>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-semibold">Class Reviews</h2>
            {isStudentLoggedIn && (
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={filterType} onValueChange={(value: 'ALL' | 'ME') => setFilterType(value)}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Reviews</SelectItem>
                    <SelectItem value="ME">My Reviews</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
          {filteredReviews.length > 0 ? (
            <div className="space-y-4">
              {filteredReviews.map((review: Testimonial) => (
                <div key={review.id} className="dark:bg-slidebar border border-gray-400 rounded-lg shadow-sm p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="relative w-12 h-12 rounded-full overflow-hidden border-2 border-[#FD904B]/20">
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium text-gray-600 dark:text-white">
                          {review.studentName || review.message.split(':')[0]}
                        </h4>
                        {isStudentLoggedIn && (() => {
                          let userFullName = '';
                          let userId = '';
                          try {
                            const studentDataStr = localStorage.getItem('student_data');
                            if (studentDataStr) {
                              const studentData = JSON.parse(studentDataStr);
                              if (studentData.firstName && studentData.lastName) {
                                userFullName = `${studentData.firstName} ${studentData.lastName}`;
                                userId = studentData.id;
                              }
                            }
                          } catch (e) {
                            console.error('Error parsing student data:', e);
                          }

                          const isAuthor =
                            (review.studentId && userId && review.studentId === userId) ||
                            (userFullName && (
                              (review.studentName && review.studentName === userFullName) ||
                              review.message.split(':')[0] === userFullName
                            ));

                          return isAuthor ? (
                            <button
                              onClick={() => openDeleteDialog(review.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          ) : null;
                        })()}
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        {[1, 2, 3, 4, 5].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${i < review.rating ? 'fill-[#FD904B] text-[#FD904B]' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-700 dark:text-gray-300 break-words mb-3">
                    {review.message.includes(':') ?
                      <>
                        <span>{review.message.split(':').slice(1).join(':').trim()}</span>
                      </>
                      : review.message
                    }
                  </p>
                  <div className="flex justify-between items-center text-sm text-gray-500">
                    <span>Posted on {new Date(review.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}

              {hasMore && (
                <div className="flex justify-center mt-6">
                  <Button
                    onClick={handleLoadMore}
                    variant="outline"
                    className="px-6 py-2 border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10"
                    disabled={isLoadingMore}
                  >
                    {isLoadingMore ? 'Loading...' : 'Load More'}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground">
              {filterType === 'ALL'
                ? "No reviews yet."
                : "You haven't written any reviews yet."}
            </p>
          )}
        </div>
      </div>
    </>
  );
};

export default ReviewsSection;
