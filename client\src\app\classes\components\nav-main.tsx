"use client";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function NavMain({ items }: { items: any[] }) {
  const { state, setOpenMobile } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <SidebarMenu className="p-2">
      {items.map((item: any, index: number) =>
        item.children ? (
          isCollapsed ? (
            <SidebarMenuItem key={index}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton tooltip={item.title}>
                    {item.icon && <item.icon className="h-5 w-5" />}
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  side="right"
                  align="start"
                  sideOffset={4}
                  className="z-50 w-60"
                >
                  <DropdownMenuLabel>{item.title}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {item.children.map((child: any, childIndex: number) => (
                    <DropdownMenuItem asChild key={childIndex}>
                      <Link
                        href={"/classes/" + child.url}
                        className="flex gap-2 items-center text-sm"
                        onClick={() => setOpenMobile(false)}
                      >
                        {child.icon && <child.icon className="h-4 w-4" />}
                        <div className="flex items-center justify-between w-full">
                          <span>{child.title}</span>
                          {child.badge && (
                            <span className="ml-2 text-xs px-1.5 py-0.5 rounded bg-orange-100 text-orange-700">
                              {child.badge}
                            </span>
                          )}
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          ) : (
            <Collapsible key={index} className="group/collapsible">
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton>
                    {item.icon && <item.icon className="h-5 w-5" />}
                    <span>{item.title}</span>
                    <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.children.map((child: any, childIndex: number) => (
                      <SidebarMenuSubItem key={childIndex}>
                        <SidebarMenuSubButton asChild>
                          <Link
                            href={"/classes/" + child.url}
                            onClick={() => setOpenMobile(false)}
                          >
                            {child.icon && <child.icon className="h-4 w-4" />}
                            <div className="flex items-center justify-between w-full">
                              <span>{child.title}</span>
                              {child.badge && (
                                <span className="ml-2 text-xs px-1.5 py-0.5 rounded bg-orange-100 text-orange-700">
                                  {child.badge}
                                </span>
                              )}
                            </div>
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          )
        ) : (
          // --- Regular menu item ---
          <SidebarMenuItem key={index}>
            <SidebarMenuButton asChild tooltip={item.title}>
              <Link
                href={"/classes/" + item.url}
                onClick={() => setOpenMobile(false)}
                className="flex items-center gap-2"
              >
                {item.icon && <item.icon className="h-5 w-5" />}
                <div className="flex items-center justify-between w-full">
                  <span>{item.title}</span>
                  {item.badge && (
                    <span className="ml-2 text-xs px-1.5 py-0.5 rounded bg-orange-100 text-orange-700">
                      {item.badge}
                    </span>
                  )}
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        )
      )}
    </SidebarMenu>
  );
}
