"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { FaGoogleScholar, FaRegHeart, FaHeart } from "react-icons/fa6";
import { MdOutlineElectricBolt } from "react-icons/md";
import { RiMessage2Line } from "react-icons/ri";
import { IoShieldCheckmark } from "react-icons/io5";
import { Button } from "@/components/ui/button";
import Header from "@/app-components/Header";
import Footer from "@/app-components/Footer";
import { axiosInstance } from "@/lib/axios";
import { parseAndJoinArray } from "@/lib/helper";
import { useSelector } from "react-redux";
import ReviewsSection from "@/app-components/ReviewsSection";
import { getAverageRating, getReviewsByClassId } from "@/services/reviewsApi";
import { isStudentAuthenticated } from "@/lib/utils";
import { addToWishlist, removeFromWishlist, checkWishlistStatus } from "@/services/studentWishlistServices";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

const TABS = [
  { key: "education", label: "Education", icon: <FaGoogleScholar /> },
  { key: "work", label: "Work Experience", icon: <MdOutlineElectricBolt /> },
  { key: "certifications", label: "Certifications", icon: <IoShieldCheckmark /> },
  { key: "tuition", label: "Tuition Classes", icon: <RiMessage2Line /> },
];

const InnerPage = () => {
  const [activeTab, setActiveTab] = useState("education");
  const [data, setData] = useState<any>(null);
  const [averageRating, setAverageRating] = useState(0);
  const [reviewCount, setReviewCount] = useState(0);
  const [isInWishlist, setIsInWishlist] = useState(false);
  const [wishlistItemId, setWishlistItemId] = useState<string | null>(null);
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const { id } = useParams();
  const router = useRouter();

  useEffect(() => {
    const fetchTeacher = async () => {
      try {
        const res = await axiosInstance.get(`classes/details/${id}`);
        setData(res.data);
      } catch (err) {
        console.error("Failed to fetch teacher data", err);
      }
    };

    const fetchAverageRating = async () => {
      try {
        const averageRating = await getAverageRating(id as string);
        setAverageRating(averageRating);
      } catch (err) {
        console.error("Failed to fetch average rating", err);
      }
    };

    const fetchReviewCount = async () => {
      try {
        const response = await getReviewsByClassId(id as string, 1, 1);
        setReviewCount(response.total);
      } catch (err) {
        console.error("Failed to fetch review count", err);
      }
    };
    setIsStudentLoggedIn(isStudentAuthenticated());

    fetchTeacher();
    fetchAverageRating();
    fetchReviewCount();
  }, [id]);

  useEffect(() => {
    const checkIfInWishlist = async () => {
      if (!isStudentLoggedIn || !id) return;

      try {
        const response = await checkWishlistStatus(id as string);
        setIsInWishlist(response.inWishlist);
        if (response.wishlistItem) {
          setWishlistItemId(response.wishlistItem.id);
        }
      } catch (error) {
        console.error("Error checking wishlist status:", error);
      }
    };

    checkIfInWishlist();
  }, [isStudentLoggedIn, id]);

  const userData = useSelector((state: any) => state.user.user);

  const handleReviewUpdate = async () => {
    try {
      const averageRating = await getAverageRating(id as string);
      setAverageRating(averageRating);

      const response = await getReviewsByClassId(id as string, 1, 1);
      setReviewCount(response.total);
    } catch (err) {
      console.error("Failed to update review stats", err);
    }
  };

  const toggleWishlist = async () => {
    if (!isStudentLoggedIn) {
      setShowLoginModal(true);
      return;
    }

    try {
      if (isInWishlist && wishlistItemId) {
        await removeFromWishlist(wishlistItemId);
        setIsInWishlist(false);
        setWishlistItemId(null);
        toast.success("Removed from wishlist");
      } else {
        const response = await addToWishlist(id as string);
        setIsInWishlist(true);
        if (response.data?.id) {
          setWishlistItemId(response.data.id);
        }
        toast.success("Added to wishlist");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to update wishlist");
    }
  };

  if (!data) {
    return (
       <div className="flex justify-center items-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-orange-500" />
      </div>
    );
  }

  const {
    firstName,
    lastName,
    education = [],
    experience = [],
    certificates = [],
    ClassAbout = {},
    tuitionClasses = [],
    status = {},
  } = data;

  const fullName = `${firstName} ${lastName}`;
  const profileImg = ClassAbout?.profilePhoto
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.profilePhoto}`
    : "/teacher-profile.jpg";

  const logoImg = ClassAbout?.classesLogo
    ? `${process.env.NEXT_PUBLIC_API_BASE_URL}${ClassAbout.classesLogo}`
    : "/teacher-profile.jpg";

  return (
    <>
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 space-y-12">
        <section className="grid md:grid-cols-4 gap-8">
          <div className="md:col-span-3 space-y-8">
            <div className="flex flex-col sm:flex-row gap-6 p-6 rounded-2xl shadow-sm border">
              <div className="relative w-full sm:w-64 h-64 rounded-xl overflow-hidden shadow-lg">
                <Image
                  src={logoImg}
                  alt="Teacher"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="flex-1 space-y-4">
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  {fullName}
                  {status?.status === "APPROVED" && (
                    <IoShieldCheckmark className="text-green-500" />
                  )}
                </h1>
                <p className="text-lg text-muted-foreground font-medium">
                  {ClassAbout?.catchyHeadline || "Professional Educator"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {ClassAbout?.tutorBio || "No bio available."}
                </p>
              </div>
            </div>

            <div className="space-y-6">
              <h2 className="text-2xl font-semibold">Resume</h2>
              <div className="flex flex-wrap gap-4 border-b pb-2">
                {TABS.map(({ key, label, icon }) => (
                  <button
                    key={key}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 ${activeTab === key
                      ? "bg-orange-100 text-orange-600 font-semibold"
                      : "text-muted-foreground hover:bg-gray-100"
                      }`}
                    onClick={() => setActiveTab(key)}
                  >
                    {icon}
                    {label}
                  </button>
                ))}
              </div>

              <div className="space-y-4">
                {activeTab === "education" && (
                  <div className="grid gap-4">
                    {education.length > 0 && education[0].isDegree ? (
                      education.map((edu: any, idx: number) => (
                        <div
                          key={idx}
                          className="p-4 rounded-lg shadow-sm border"
                        >
                          <p className="font-semibold text-foreground">
                            {edu.university}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {edu.degree} — {edu.degreeType}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Passout Year: {edu.passoutYear}
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">
                        No education details available.
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "work" && (
                  <div className="grid gap-4">
                    {experience.length && experience[0].isExperience ? (
                      experience.map((exp: any, idx: number) => (
                        <div
                          key={idx}
                          className="p-4 rounded-lg shadow-sm border"
                        >
                          <p className="font-semibold text-foreground">
                            {exp.title}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            From: {new Date(exp.from).toLocaleDateString()} — To:{" "}
                            {new Date(exp.to).toLocaleDateString()}
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">
                        No work experience available.
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "certifications" && (
                  <div className="grid gap-4">
                    {certificates.length > 0 && certificates[0].isCertificate ? (
                      certificates.map((cert: any, idx: number) => (
                        <div
                          key={idx}
                          className="p-4 rounded-lg shadow-sm border"
                        >
                          <p className="font-semibold text-foreground">
                            {cert.title}
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">
                        No certifications available.
                      </p>
                    )}
                  </div>
                )}

                {activeTab === "tuition" && (
                  <div className="grid gap-4">
                    {tuitionClasses.length > 0 ? (
                      tuitionClasses.map((tuition: any, idx: number) => (
                        <div
                          key={idx}
                          className="p-6 rounded-lg shadow-sm border"
                        >
                          <p className="text-lg font-semibold text-foreground">
                            Tuition #{idx + 1}
                          </p>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4 text-sm text-muted-foreground">
                            <div>
                              <strong>Category:</strong> {tuition.education || "N/A"}
                            </div>
                            <div>
                              <strong>Coaching Type:</strong>{" "}
                              {parseAndJoinArray(tuition.coachingType)}
                            </div>
                            {tuition.education === "Education" ? (
                              <>
                                <div>
                                  <strong>Board:</strong>{" "}
                                  {parseAndJoinArray(tuition.boardType)}
                                </div>
                                <div>
                                  <strong>Medium:</strong>{" "}
                                  {parseAndJoinArray(tuition.medium)}
                                </div>
                                <div>
                                  <strong>Section:</strong>{" "}
                                  {parseAndJoinArray(tuition.section)}
                                </div>
                                <div>
                                  <strong>Subject:</strong>{" "}
                                  {parseAndJoinArray(tuition.subject)}
                                </div>
                              </>
                            ) : (
                              <div>
                                <strong>Details:</strong>{" "}
                                {parseAndJoinArray(tuition.details)}
                              </div>
                            )}
                          </div>
                          {tuition.timeSlots?.length > 0 && (
                            <div className="mt-4">
                              <p className="font-medium">Time Slots:</p>
                              <ul className="list-disc ml-6 mt-1 text-sm text-muted-foreground">
                                {tuition.timeSlots.map((slot: any, i: number) => (
                                  <li key={i}>
                                    {slot.from} — {slot.to}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground">
                        No tuition classes listed yet.
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          <aside className="sticky top-24 rounded-2xl p-6 shadow-sm border space-y-6">
            <div className="relative w-full h-48 rounded-xl overflow-hidden">
              <Image
                src={profileImg}
                alt="Profile"
                fill
                className="object-cover"
              />
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-500">★ {averageRating.toFixed(1)}</p>
              <p className="text-sm text-muted-foreground">{reviewCount} reviews</p>
            </div>
            <div className="space-y-3">
              <Button
                variant="default"
                className="w-full flex gap-2 bg-orange-500 hover:bg-orange-600 transition-colors"
                 onClick={() => {
                  if (!isStudentLoggedIn) {
                    setShowLoginModal(true);
                    return;
                  }
                  const userName = `${data.firstName} ${data.lastName}`;
                  router.push(`/student/chat?userId=${data.id}&userName=${encodeURIComponent(userName)}`);
                }}
              >
                <RiMessage2Line /> Send Message
              </Button>
              <Button
                variant="outline"
                className={`w-full flex gap-2 hover:bg-orange-50 transition-colors ${isInWishlist ? 'bg-orange-50 text-orange-600' : ''}`}
                onClick={toggleWishlist}
              >
                {isInWishlist ? <FaHeart className="text-orange-500" /> : <FaRegHeart />}
                {isInWishlist ? 'Saved to Wishlist' : 'Save to My List'}
              </Button>
            </div>
          </aside>
        </section>

        <ReviewsSection
          classId={id as string}
          userData={userData}
          onReviewSubmit={handleReviewUpdate}
        />
      </main>
      <Footer />

      <Dialog open={showLoginModal} onOpenChange={setShowLoginModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Login Required</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <p className="text-center text-muted-foreground">
              Please login as a student to add this class to your wishlist or send a message.
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default InnerPage;