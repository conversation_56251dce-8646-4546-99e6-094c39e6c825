"use client";

import Link from "next/link";
import Image from "next/image";
import { Menu, X, User, ShoppingBag, Briefcase, Share2, UserCircle, ChevronRight, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useAppDispatch } from "@/store/hooks";
import { useEffect, useState, useRef } from "react";
import { GraduationCap, Flame } from "lucide-react";
import { isStudentAuthenticated, clearStudentAuthToken } from "@/lib/utils";
import ProfileCompletionIndicator from "./ProfileCompletionIndicator";
import {
  Avatar,
  AvatarFallback,
} from "@/components/ui/avatar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { toast } from "sonner";
import { logoutStudent } from "@/services/studentAuthServices";
import { clearUser } from "@/store/slices/userSlice";
import { clearStudentProfileData } from "@/store/slices/studentProfileSlice";
import { fetchStudentProfile } from "@/store/thunks/studentProfileThunks";
import { useRouter } from "next/navigation";
import { axiosInstance } from "@/lib/axios";
import { motion, useMotionValue, useAnimationFrame } from "framer-motion";

const Header = () => {
  const { isAuthenticated, user } = useSelector((state: RootState) => state.user);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);
  const [studentData, setStudentData] = useState<any>(null);
  const dispatch = useAppDispatch();
  const router = useRouter();

  const contentRef = useRef<HTMLDivElement>(null);
  const [contentWidth, setContentWidth] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const x = useMotionValue(0); // Motion value to control the x position

  const speed = (contentWidth / 20);

  useEffect(() => {
    const isLoggedIn = isStudentAuthenticated();
    setIsStudentLoggedIn(isLoggedIn);

    if (isLoggedIn) {
      const storedData = localStorage.getItem('student_data');
      if (storedData) {
        setStudentData(JSON.parse(storedData));
      }

      dispatch(fetchStudentProfile());
    }

    const handleStorageChange = () => {
      const newLoginStatus = isStudentAuthenticated();
      setIsStudentLoggedIn(newLoginStatus);

      if (newLoginStatus) {
        const storedData = localStorage.getItem('student_data');
        if (storedData) {
          setStudentData(JSON.parse(storedData));
        }

        dispatch(fetchStudentProfile());
      } else {
        setStudentData(null);
      }
    };

    window.addEventListener('storage', handleStorageChange);

    if (contentRef.current) {
      const width = contentRef.current.getBoundingClientRect().width;
      setContentWidth(width);
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [dispatch]);

  // Custom animation loop using useAnimationFrame
  useAnimationFrame((time, delta) => {
    if (isHovering || contentWidth === 0) return;
    const currentX = x.get();
    const deltaX = (speed * delta) / 1000;
    let newX = currentX - deltaX;
    if (newX <= -contentWidth) {
      newX = 0;
    }
    x.set(newX);
  });

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleStudentLogout = async () => {
    try {
      const response = await logoutStudent();

      if (response.success !== false) {
        // Clear local state
        clearStudentAuthToken();
        setIsStudentLoggedIn(false);
        setStudentData(null);
        localStorage.removeItem('student_data');

        // Clear Redux state
        dispatch(clearStudentProfileData());

        toast.success("Logged out successfully");
        window.dispatchEvent(new Event('storage'));
      } else {
        toast.error(response.message || "Failed to logout");
        clearStudentAuthToken();
        setIsStudentLoggedIn(false);
        setStudentData(null);
        localStorage.removeItem('student_data');
        dispatch(clearStudentProfileData());
      }
    } catch (error) {
      console.log("Failed to logout", error);
      toast.error("Failed to logout");
      localStorage.removeItem('student_data');
      clearStudentAuthToken();
      setIsStudentLoggedIn(false);
      setStudentData(null);
      dispatch(clearStudentProfileData());
    }
  };


  const navLinks = [
    { href: "/verified-classes", label: "Find Tutor", icon: <GraduationCap className="w-4 h-4" /> },
    { href: "/uwhiz", label: "U - Whiz", icon: <Flame className="w-4 h-4" />, isNew: true },
    { href: "/careers", label: "Career", icon: <Briefcase className="w-4 h-4" />},
  ];

  const bannerContent = (
    <div className="inline-flex items-center space-x-4 whitespace-nowrap">
      <span className="text-sm md:text-xl font-semibold text-black">
        U Whiz – Super Kids Exam is live! Win ₹1,00,000 – So hurry up, Apply now and be a champion
      </span>
      <button
        className="inline-flex items-center justify-center rounded-md font-bold bg-white text-black px-3 py-1 text-sm hover:bg-[#FD904B] hover:text-black transition"
        style={{ border: '2px solid black' }}
        onClick={() => router.push(`/uwhiz-info/${1}`)}
      >
        Apply Now <ChevronRight className="ml-2 h-4 w-4" />
      </button>
    </div>
  );

  return (
    <>
      <header className="sticky top-0 z-50 w-full bg-black overflow-x-hidden">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-20 items-center justify-between">
            <Link
              href="/"
              className="flex items-center space-x-2 transition-transform hover:scale-105"
            >
              <Image
                src="/logo_black.png"
                alt="Preply Logo"
                width={200}
                height={50}
                className="rounded-sm"
              />
            </Link>

            <nav className="hidden md:flex items-center space-x-4">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400"
                >
                  {link.icon}
                  {link.label}
                  {link.label === "Find School" && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black">
                      Coming Soon
                    </span>
                  )}
                  {link.isNew && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse">
                      Trending
                    </span>
                  )}
                </Link>
              ))}
            </nav>
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-orange-400 hover:bg-orange-500/10"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>

            {/* Desktop Actions */}
            <div className="hidden md:flex items-center space-x-4">
              {isAuthenticated && (
                <Link href="/coins" passHref>
                  <Button
                    variant="outline"
                    size="icon"
                    className="relative rounded-full group bg-black"
                  >
                    <div className="absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity" />
                    <div className="relative z-10 ">
                      <Image
                        src="/uest_coin.png"
                        alt="Coin Icon"
                        width={60}
                        height={60}
                        className="object-contain"
                      />
                    </div>
                  </Button>
                </Link>
              )}

              <div className="h-8 border-l border-orange-500/20" />

              {isAuthenticated && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Avatar className="cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors">
                      <AvatarFallback className="bg-white text-black">
                        {user?.firstName && user?.lastName
                        ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                          : "CT"}
                      </AvatarFallback>
                    </Avatar>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 bg-white">
                    <div className="flex items-center gap-3 mb-3 pb-2 border-b">
                      <Avatar className="h-12 w-12 border-2 border-[#ff914d]">
                        <AvatarFallback className="bg-white text-black">
                          {user?.firstName && user?.lastName
                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
                            : "CT"}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-black">
                          {user?.firstName && user?.lastName
                            ? `${user.firstName} ${user.lastName}`
                            : user?.className || "Class Account"}
                        </p>
                        <p className="text-xs text-gray-600">{user?.email || "<EMAIL>"}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Button asChild className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white">
                        <Link href="/classes/profile" className="flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          <span>My Dashboard</span>
                        </Link>
                      </Button>
                      <Button asChild className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white">
                        <Link href="/classes/referral-dashboard" className="flex items-center">
                          <Share2 className="mr-2 h-4 w-4" />
                          <span>Referral Dashboard</span>
                        </Link>
                      </Button>

                      <Button
                        variant="outline"
                        className="w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10"
                        onClick={async () => {
                          try {
                            const response = await axiosInstance.post("/auth-client/logout", {});
                            if (response.data.success) {
                              router.push("/");
                              dispatch(clearUser());
                              localStorage.removeItem("token");
                              toast.success("Logged out successfully");
                            }
                          } catch (error) {
                            console.error("Logout error:", error);
                            toast.error("Failed to logout");
                          }
                        }}
                      >
                        Logout
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              )}

              {!isAuthenticated && !isStudentLoggedIn && (
                <Button
                  className="bg-customOrange hover:bg-[#E88143] text-white mr-4"
                  asChild
                >
                  <Link href="/class/login">Join as a Tutor/Class</Link>
                </Button>
              )}

              {!isAuthenticated && !isStudentLoggedIn && (
                <Button
                  variant="outline"
                  className="bg-black border-orange-500 hover:bg-orange-900/50 text-white hover:text-white"
                  asChild
                >
                  <Link href="/student/login">Student Login</Link>
                </Button>
              )}

              {isStudentLoggedIn && (
                <Link href="/coins" passHref>
                  <Button
                    variant="outline"
                    size="icon"
                    className="relative rounded-full group bg-black"
                  >
                    <div className="absolute rounded-full inset-0 opacity-0 group-hover:opacity-20 transition-opacity" />
                    <div className="relative z-10 ">
                      <Image
                        src="/uest_coin.png"
                        alt="Coin Icon"
                        width={60}
                        height={60}
                        className="object-contain"
                      />
                    </div>
                  </Button>
                </Link>
              )}

              {isStudentLoggedIn && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Avatar className="cursor-pointer border-2 border-[#ff914d] hover:border-[#ff914d]/80 transition-colors">
                      <AvatarFallback className="bg-white text-black">
                        {studentData?.firstName && studentData?.lastName
                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                          : "ST"}
                      </AvatarFallback>
                    </Avatar>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 bg-white">
                    <div className="flex items-center gap-3 mb-3 pb-2 border-b">
                      <Avatar className="h-12 w-12 border-2 border-[#ff914d]">
                        <AvatarFallback className="bg-white text-black">
                          {studentData?.firstName && studentData?.lastName
                          ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()
                            : "ST"}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-black">
                          {studentData?.firstName && studentData?.lastName
                            ? `${studentData.firstName} ${studentData.lastName}`
                            : "Student Account"}
                        </p>
                        <p className="text-xs text-gray-600">{studentData?.email || "<EMAIL>"}</p>
                      </div>
                    </div>

                  <div className="space-y-2">
                    <Button asChild className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white">
                      <Link href="/student/profile" className="flex items-center">
                        <UserCircle className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                      </Link>
                    </Button>
                    <Button asChild className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white">
                      <Link href="/student/wishlist" className="flex items-center">
                        <ShoppingBag className="mr-2 h-4 w-4" />
                        <span>My Wishlist</span>
                      </Link>
                    </Button>
                    <Button asChild className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white">
                      <Link href="/student/chat" className="flex items-center">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        <span>Chat</span>
                      </Link>
                    </Button>
                    <Button asChild className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white">
                      <Link href="/student/referral-dashboard" className="flex items-center">
                        <Share2 className="mr-2 h-4 w-4" />
                        <span>Referral Dashboard</span>
                      </Link>
                    </Button>
                    {/* Referral dashboard removed - only available for Classes */}

                      <Button variant="outline" className="w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10" onClick={handleStudentLogout}>
                        Logout
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </div>
        </div>
        <div className="w-screen bg-[#FD904B] border-y border-black relative mt-1">
            <div className="absolute top-0 right-0 h-full w-[20vw] bg-[#FD904B] block md:hidden z-0"></div>
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 relative z-10 overflow-hidden">
              <motion.div
                className="inline-flex py-2 px-4"
                style={{ x }} // Bind the x position to the motion value
                onMouseEnter={() => setIsHovering(true)}
                onMouseLeave={() => setIsHovering(false)}
              >
                <div ref={contentRef} className="inline-flex items-center space-x-4 whitespace-nowrap pr-8">
                  {bannerContent}
                </div>
                <div className="inline-flex items-center space-x-4 whitespace-nowrap pr-8">
                  {bannerContent}
                </div>
              </motion.div>
            </div>
          </div>
      </header>

      <div>
      {/* Mobile Sidebar Menu */}
      <div
        className={`fixed inset-y-0 right-0 z-50 w-72 bg-black/95 shadow-2xl transform transition-all duration-300 ease-in-out md:hidden border-l border-orange-500/20 ${isMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
      >
        <div className="flex flex-col h-full p-6">
          {/* Close Button */}
          <div className="flex justify-end">
            <Button
              variant="ghost"
              size="icon"
              className="text-orange-400 hover:bg-orange-500/10 rounded-full"
              onClick={toggleMenu}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>



          {/* Mobile Navigation */}
            <nav className="flex flex-col space-y-2 mt-8">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="px-4 py-3 border text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-orange-500/10 rounded-lg transition-colors flex justify-between items-center"
                  onClick={toggleMenu}
                >
                  <div className="flex items-center gap-3">
                    {link.icon}
                    <span>{link.label}</span>
                  </div>
                  {link.label === "Find School" && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-yellow-500 text-black animate-pulse">
                      Coming Soon
                    </span>
                  )}
                  {link.isNew && (
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white">
                      New
                    </span>
                  )}
                </Link>
              ))}
            </nav>

          {/* Mobile Actions */}
            <div className="mt-auto space-y-4">
              {isAuthenticated && (
                <>
                  <Link href="/classes/profile" passHref>
                    <Button
                      variant="outline"
                      className="w-full group relative border-orange-500 hover:border-orange-400 bg-black"
                      onClick={toggleMenu}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity" />
                      <div className="relative z-10 flex items-center justify-center gap-3 py-2">
                        <div className="p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500">
                          <User className="h-5 w-5 text-white" />
                        </div>
                        <span className="font-medium text-gray-300">Dashboard</span>
                      </div>
                    </Button>
                  </Link>

                  <Link href="/classes/referral-dashboard" passHref>
                    <Button
                      variant="outline"
                      className="w-full group relative border-orange-500 hover:border-orange-400 mt-3 bg-black"
                      onClick={toggleMenu}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity" />
                      <div className="relative z-10 flex items-center justify-center gap-3 py-2">
                        <div className="p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500">
                          <Share2 className="h-5 w-5 text-white" />
                        </div>
                        <span className="font-medium text-gray-300">Referral Dashboard</span>
                      </div>
                    </Button>
                  </Link>

                <Link href="/coins" passHref>
                  <Button
                    variant="outline"
                    className="w-full group relative border-orange-500 hover:border-orange-400 bg-black mt-3"
                    onClick={toggleMenu}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity" />
                    <div className="relative z-10 flex items-center justify-center gap-3 py-2">
                      <div className="p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500">
                        <Image
                          src="/uest_coin.png"
                          alt="Coin Icon"
                          width={20}
                          height={20}
                          className="object-contain"
                        />
                      </div>
                      <span className="font-medium text-gray-300">My Coins</span>
                    </div>
                  </Button>
                </Link>

                <Button
                  variant="outline"
                  className="w-full border-orange-500 text-orange-500 hover:bg-orange-500/10 mt-3"
                  onClick={async () => {
                    try {
                      const response = await axiosInstance.post("/auth-client/logout", {});
                      if (response.data.success) {
                        router.push("/");
                        dispatch(clearUser());
                        localStorage.removeItem("token");
                        toast.success("Logged out successfully");
                      }
                    } catch (error) {
                      console.error("Logout error:", error);
                      toast.error("Failed to logout");
                    }
                    toggleMenu();
                  }}
                >
                  <div className="flex items-center justify-center gap-3">
                    <User className="h-5 w-5" />
                    <span>Logout</span>
                  </div>
                </Button>
              </>
            )}

              {isStudentLoggedIn && (
                <>
                  {studentData?.firstName && studentData?.lastName && (
                    <div className="p-3 border border-[#ff914d]/20 rounded-lg bg-white">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12 border-2 border-[#ff914d]">
                          <AvatarFallback className="bg-white text-black">
                            {(`${studentData.firstName[0]}${studentData.lastName[0]}`).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-black">{`${studentData.firstName} ${studentData.lastName}`}</p>
                          <p className="text-xs text-gray-600">{studentData.email}</p>
                        </div>
                      </div>
                    </div>
                  )}

                <Button
                  asChild
                  className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                  onClick={toggleMenu}
                >
                  <Link href="/student/profile" className="flex items-center justify-center gap-3">
                    <UserCircle className="h-5 w-5" />
                    <span>Profile</span>
                  </Link>
                </Button>

                <Button
                  asChild
                  className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                  onClick={toggleMenu}
                >
                  <Link href="/student/chat" className="flex items-center justify-center gap-3">
                    <MessageSquare className="h-5 w-5" />
                    <span>Chat</span>
                  </Link>
                </Button>

                <Button
                  asChild
                  className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                  onClick={toggleMenu}
                >
                  <Link href="/student/wishlist" className="flex items-center justify-center gap-3">
                    <ShoppingBag className="h-5 w-5" />
                    <span>My Wishlist</span>
                  </Link>
                </Button>

                <Button
                  asChild
                  className="w-full bg-[#ff914d] hover:bg-[#ff914d]/90 text-white"
                  onClick={toggleMenu}
                >
                  <Link href="/student/referral-dashboard" className="flex items-center justify-center gap-3">
                    <Share2 className="h-5 w-5" />
                    <span>Referral Dashboard</span>
                  </Link>
                </Button>

                  <Link href="/coins" passHref>
                    <Button
                      variant="outline"
                      className="w-full group relative border-orange-500 hover:border-orange-400 bg-black mb-3"
                      onClick={toggleMenu}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-yellow-500 opacity-0 group-hover:opacity-20 transition-opacity" />
                      <div className="relative z-10 flex items-center justify-center gap-3">
                        <div className="p-1.5 rounded-full bg-gradient-to-br from-orange-500 to-yellow-500">
                          <Image
                            src="/uest_coin.png"
                            alt="Coin Icon"
                            width={20}
                            height={20}
                            className="object-contain"
                          />
                        </div>
                        <span className="font-medium text-gray-300">My Coins</span>
                      </div>
                    </Button>
                  </Link>

                  <Button
                    variant="outline"
                    className="w-full border-[#ff914d] text-[#ff914d] hover:bg-[#ff914d]/10"
                    onClick={() => {
                      handleStudentLogout();
                      toggleMenu();
                    }}
                  >
                    <div className="flex items-center justify-center gap-3">
                      <User className="h-5 w-5" />
                      <span>Logout</span>
                    </div>
                  </Button>
                </>
              )}

              {!isAuthenticated && !isStudentLoggedIn && (
                <div className="space-y-3 pt-3">
                  <Button
                    variant="default"
                    className="w-full bg-orange-500 hover:bg-orange-600"
                    asChild
                  >
                    <Link href="/class/login" onClick={toggleMenu}>
                      Tutor/Classes Login
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full border-customOrange text-orange-500 hover:bg-orange"
                    asChild
                  >
                    <Link href="/student/login" onClick={toggleMenu}>
                      Student Login
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {isStudentLoggedIn && (
          <div className="mt-4 mx-10 sm:px-4">
            <ProfileCompletionIndicator />
          </div>
        )}
      </div>
    </>
  );
};

export default Header;
