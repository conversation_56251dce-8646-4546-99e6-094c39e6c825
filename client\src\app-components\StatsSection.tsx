import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Users, BookCheck, GraduationCap } from "lucide-react";

interface StatsSectionProps {
  totalTutors: number;
  totalStudent: number;
}

const useCounter = (end: number, duration = 1500) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const startTime = performance.now();

    const step = (currentTime: number) => {
      const progress = Math.min((currentTime - startTime) / duration, 1);
      const currentCount = Math.floor(progress * end);
      setCount(currentCount);

      if (progress < 1) {
        requestAnimationFrame(step);
      } else {
        setCount(end);
      }
    };

    requestAnimationFrame(step);
  }, [end, duration]);

  return count;
};

export default function StatsSection({ totalTutors, totalStudent }: StatsSectionProps) {
  const tutorsCount = useCounter(totalTutors);
  const categoriesCount = useCounter(16);
  const studentsCount = useCounter(totalStudent);

  const stats = [
    {
      icon: <Users className="w-8 h-8" />,
      count: tutorsCount,
      suffix: "+",
      label: "Verified Classes",
    },
    {
      icon: <BookCheck className="w-8 h-8" />,
      count: categoriesCount,
      suffix: "+",
      label: "Categories",
    },
    {
      icon: <GraduationCap className="w-8 h-8" />,
      count: studentsCount,
      suffix: "+",
      label: "Students",
    },
  ];

  return (
    <section className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"></div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="relative group"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.15 }}
              viewport={{ once: true }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-[#FD904B]/20 to-transparent rounded-2xl blur-2xl group-hover:blur-3xl transition-all duration-300 opacity-0 group-hover:opacity-100"></div>
              <div className="relative p-8 rounded-2xl bg-card/50 dark:bg-card/30 backdrop-blur-sm border border-border shadow-sm hover:shadow-sm transition-all duration-300">
                <div className="text-[#FD904B] mb-4 transform group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <h3 className="text-4xl font-bold mb-2 bg-gradient-to-br from-foreground to-foreground/80 bg-clip-text text-transparent">
                  {stat.count}
                  {stat.suffix}
                </h3>
                <p className="text-muted-foreground">{stat.label}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}