"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Blog } from '@/lib/types';
import { getApprovedBlogs } from '@/services/blogApi';
import BlogCard from './BlogCard';

const RecentBlogs = () => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRecentBlogs = async () => {
      try {
        setIsLoading(true);
        const response = await getApprovedBlogs(1, 3);
        setBlogs(response.blogs);
      } catch (error) {
        console.error("Failed to fetch recent blogs:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentBlogs();
  }, []);

  if (blogs.length === 0 && !isLoading) {
    return null;
  }

  return (
    <section className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-background via-secondary/5 to-background dark:from-background dark:via-secondary/10 dark:to-background"></div>
      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <span className="text-[#FD904B] text-sm font-semibold tracking-wider uppercase mb-4 block">
            Latest Blogs
          </span>
          <h2 className="text-4xl font-bold bg-clip-text mb-4">
            Our Latest Articles
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Stay updated with our latest news, tips, and insights
          </p>
        </motion.div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
              {blogs.map((blog) => (
                <motion.div
                  key={blog.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <BlogCard blog={blog} />
                </motion.div>
              ))}
            </div>
            
            <div className="text-center">
              <Link href="/blogs" passHref>
                <Button 
                  variant="outline" 
                  className="px-8 py-6 text-lg rounded-full border-[#FD904B] text-[#FD904B] hover:bg-[#FD904B]/10 transition-all duration-300"
                >
                  Visit More Blogs
                </Button>
              </Link>
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default RecentBlogs;
