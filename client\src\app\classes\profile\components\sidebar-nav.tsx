import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { CheckCircle } from 'lucide-react';

interface SidebarNavProps {
  items: { title: string; href: string }[];
}

export function SidebarNav({ items }: SidebarNavProps) {
  const pathname = usePathname();
  const { completedForms } = useSelector((state: RootState) => state.formProgress);

  const normalize = (title: string) => title.toLowerCase().replace(/ & /g, '_').replace(/\s+/g, '_');

  return (
    <nav className="space-y-1">
      {items.map((item, index) => {
        const formKey = normalize(item.title);
        const isActive = pathname === item.href;

        // Disable if previous form is not completed
        const isDisabled =
          index > 0 &&
          !completedForms[normalize(items[index - 1].title)];

        return (
          <Link
            key={item.href}
            href={isDisabled ? "#" : item.href}
            className={`flex items-center w-[200px] justify-between rounded-md px-3 py-2 text-sm font-medium transition-colors ${isActive ? 'bg-muted text-primary' : isDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-muted-foreground hover:text-primary'
              }`}
            onClick={(e) => {
              if (isDisabled) e.preventDefault();
            }}
          >
            <span>{item.title}</span>
            {completedForms[formKey] && <CheckCircle size={16} className="text-green-500" />}
          </Link>
        );
      })}
    </nav>
  );
}