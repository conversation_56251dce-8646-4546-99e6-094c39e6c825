'use client';

import React, { useState, useEffect } from 'react';
import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import { motion } from 'framer-motion';
import { FaShieldAlt, FaUserShield, FaExchangeAlt, FaLock, FaGlobe } from 'react-icons/fa';
import {
  MdSecurity,
  MdPrivacyTip,
  MdCookie,
  MdOutlinePolicy,
  MdContactSupport,
} from 'react-icons/md';
import { RiInformationLine, RiDatabase2Line } from 'react-icons/ri';
import { BiData } from 'react-icons/bi';

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

function PrivacyPolicyPage() {
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  return (
    <>
      <Header />
      <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-16 bg-background min-h-screen">
        <motion.header
          className="text-center mb-12"
          initial="hidden"
          animate="visible"
          variants={fadeIn}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="flex justify-center mb-6"
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ type: 'spring', stiffness: 260, damping: 20, delay: 0.3 }}
          >
            <FaShieldAlt className="text-6xl text-customOrange" />
          </motion.div>
          <h1 className="text-4xl sm:text-5xl font-extrabold text-foreground tracking-tight">
            <span className="inline-block pb-2">Privacy Policy</span>
          </h1>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            We value your privacy. Please read our policy carefully to understand how we handle your
            information.
          </p>
        </motion.header>

        <motion.div
          className="space-y-12"
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
        >
          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <RiInformationLine className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Introduction
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              Your access to and use of our Service is conditioned on your acceptance of and
              compliance with this Privacy Policy. This policy applies to all visitors, users, and
              others who access or use the Service.
            </p>
            <p className="text-muted-foreground leading-relaxed mt-3">
              By accessing or using the Service, you agree to the collection and use of your
              information in accordance with this Privacy Policy. If you disagree with any part of
              this policy, you may not access the Service.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <BiData className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Information We Collect
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>
                  <strong className="text-card-foreground">Personal Data:</strong> Name, email,
                  phone number, address, etc.
                </div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>
                  <strong className="text-card-foreground">Usage Data:</strong> IP address, browser
                  type, device information, etc.
                </div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>
                  <strong className="text-card-foreground">Cookies:</strong> Session, preference,
                  security, and advertising cookies.
                </div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>
                  <strong className="text-card-foreground">Other Data:</strong> Age, gender,
                  educational details (if applicable).
                </div>
              </motion.li>
            </ul>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <MdPrivacyTip className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                How We Use Your Data
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>To provide and maintain our Service.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>To notify you of changes or updates.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>For customer support and troubleshooting.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>To analyze usage and improve functionality.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>For marketing and promotional purposes (with your consent).</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>To comply with legal obligations.</div>
              </motion.li>
            </ul>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <RiDatabase2Line className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Data Retention
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              We retain your Personal Data only as long as necessary for the purposes outlined in
              this Privacy Policy. Usage Data may be retained for analysis or legal compliance.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <FaExchangeAlt className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Data Transfer
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              Your information, including Personal Data, may be transferred to and maintained on
              computers located outside your jurisdiction. We ensure data security during such
              transfers.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <FaGlobe className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Data Disclosure
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>To comply with legal requests or law enforcement.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>During business transactions (e.g., mergers, acquisitions).</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>To trusted third-party service providers.</div>
              </motion.li>
            </ul>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <FaLock className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Data Security
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              We use industry-standard measures to protect your data. However, no method of
              transmission over the internet is 100% secure.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <FaUserShield className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Your Data Protection Rights
              </h2>
            </div>
            <ul className="list-none space-y-3 text-muted-foreground">
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>Access, update, or delete your data.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>Rectify inaccurate or incomplete information.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>Object to data processing.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>Request a copy of your data in a machine-readable format.</div>
              </motion.li>
              <motion.li
                className="flex items-start gap-2"
                whileHover={{ x: 5, transition: { duration: 0.2 } }}
              >
                <span className="text-customOrange mt-1">•</span>
                <div>Withdraw consent for data processing at any time.</div>
              </motion.li>
            </ul>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <MdCookie className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Cookies & Tracking
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              We use cookies for functionality, preferences, security, and advertising. You can
              disable cookies, but some Service features may not work.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <MdSecurity className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Third-Party Services
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              We use third-party providers for analytics, payments, and remarketing. These providers
              have their own privacy policies.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <MdOutlinePolicy className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Changes to This Policy
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              We may update this Privacy Policy periodically. Changes will be notified via email or
              a notice on our Service.
            </p>
          </motion.section>

          <motion.section
            className="bg-card rounded-lg shadow-md p-6 sm:p-8 border-l-4 border-customOrange hover:shadow-lg transition-shadow duration-300"
            variants={fadeIn}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
          >
            <div className="flex items-center gap-3 mb-4">
              <MdContactSupport className="text-3xl text-customOrange" />
              <h2 className="text-2xl font-semibold text-card-foreground">
                Contact Us
              </h2>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              For questions or requests, contact us at{' '}
              <motion.a
                href="mailto:<EMAIL>"
                className="text-customOrange hover:text-foreground underline transition-colors"
                aria-label="Email <NAME_EMAIL>"
                whileHover={{ scale: 1.05 }}
              >
                <EMAIL>
              </motion.a>
              .
            </p>
          </motion.section>
        </motion.div>
      </main>
      <Footer />

      {/* Scroll to top button */}
      {showScrollTop && (
        <motion.button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 p-3 rounded-full bg-customOrange text-white shadow-lg z-50"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.5 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 10l7-7m0 0l7 7m-7-7v18"
            />
          </svg>
        </motion.button>
      )}
    </>
  );
}

export default PrivacyPolicyPage;
