import prisma from '@/config/prismaClient';
import { ClassApprovalStatus } from '@prisma/client';

export const fetchClassesForExport = async (
  status: string = '',
  search: string = '',
  filterType: string = 'all'
) => {
  const whereClause: any = {};

  if (status && status !== 'all') {
    if (status === 'NOT_COMPLETED') {
      whereClause.status = { is: null };
    } else {
      whereClause.status = {
        status: status as ClassApprovalStatus,
      };
    }
  }

  if (search) {
    if (filterType === 'userName') {
      whereClause.username = { contains: search, mode: 'insensitive' };
    } else if (filterType === 'firstName') {
      whereClause.firstName = { contains: search, mode: 'insensitive' };
    } else if (filterType === 'lastName') {
      whereClause.lastName = { contains: search, mode: 'insensitive' };
    } else if (filterType === 'contactNo') {
      whereClause.contactNo = { contains: search, mode: 'insensitive' };
    } else if (filterType === 'email') {
      whereClause.email = { contains: search, mode: 'insensitive' };
    } else if (filterType === 'className') {
      whereClause.className = { contains: search, mode: 'insensitive' };
    } else if (filterType === 'status') {
      whereClause.status = {
        status: { contains: search, mode: 'insensitive' },
      };
    } else {
      whereClause.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { contactNo: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } },
        { className: { contains: search, mode: 'insensitive' } },
      ];
    }
  }

  const classes = await prisma.classes.findMany({
    where: whereClause,
    include: {
      ClassAbout: true,
      status: {
        select: { status: true },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return classes;
};

export const fetchExamApplicationsForExport = async (
  page: number = 1,
  limit: number = 1000,
  search: string = '',
  filterType: string = 'all'
) => {
  const skip = (page - 1) * limit;
  const whereClause: any = {};

  if (search) {
    if (filterType === 'examName') {
      whereClause.exam = {
        exam_name: { contains: search, mode: 'insensitive' },
      };
    } else {
      whereClause.OR = [
        { class: { firstName: { contains: search, mode: 'insensitive' } } },
        { class: { lastName: { contains: search, mode: 'insensitive' } } },
        { class: { email: { contains: search, mode: 'insensitive' } } },
        { class: { contactNo: { contains: search, mode: 'insensitive' } } },
        { class: { className: { contains: search, mode: 'insensitive' } } },
        { exam: { exam_name: { contains: search, mode: 'insensitive' } } },
      ];
    }
  }

  const applications = await prisma.examApplication.findMany({
    skip,
    take: limit,
    where: whereClause,
    orderBy: { createdAt: 'desc' },
    include: {
      exam: {
        select: {
          exam_name: true,
          total_questions: true,
          marks: true,
          level: true,
          duration: true,
          start_date: true,
          coins_required: true,
        },
      },
      class: {
        select: {
          firstName: true,
          lastName: true,
          className: true,
          email: true,
          contactNo: true,
        },
      },
    },
  });

  return applications;
};

export const fetchStudentsForExport = async (
  name: string = '',
  email: string = '',
  contact: string = '',
  status: string = ''
) => {
  const whereClause: any = {};

  // Apply filters similar to the existing student filtering logic
  if (name) {
    whereClause.OR = [
      { firstName: { contains: name, mode: 'insensitive' } },
      { lastName: { contains: name, mode: 'insensitive' } },
    ];
  }

  if (email) {
    whereClause.email = { contains: email, mode: 'insensitive' };
  }

  if (contact) {
    whereClause.contact = { contains: contact, mode: 'insensitive' };
  }

  // Handle profile status filter
  if (status && status !== 'all') {
    whereClause.profile = {
      status: status,
    };
  }

  const students = await prisma.student.findMany({
    where: whereClause,
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      contact: true,
      isVerified: true,
      createdAt: true,
      updatedAt: true,
      profile: {
        select: {
          id: true,
          medium: true,
          classroom: true,
          birthday: true,
          school: true,
          address: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Get coins for all students
  const studentIds = students.map(student => student.id);
  const coins = await prisma.uestCoins.findMany({
    where: {
      modelType: 'STUDENT',
      modelId: { in: studentIds },
    },
    select: {
      modelId: true,
      coins: true,
    },
  });

  const coinsMap = new Map(coins.map((coin) => [coin.modelId, coin.coins]));

  // Enrich students with coins data
  const enrichedStudents = students.map((student) => ({
    ...student,
    coins: coinsMap.get(student.id) ?? 0,
  }));

  return enrichedStudents;
};
