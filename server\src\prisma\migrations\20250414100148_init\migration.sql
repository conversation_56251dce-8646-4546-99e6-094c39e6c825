-- Create<PERSON>num
CREATE TYPE "ClassApprovalStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'IN_PROCESS');

-- CreateEnum
CREATE TYPE "ModelType" AS ENUM ('CLASS', 'STUDENT', 'SCHOOL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "TransactionType" AS ENUM ('CREDIT', 'DEBIT');

-- CreateTable
CREATE TABLE "Classes" (
    "id" TEXT NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "className" TEXT,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "contactNo" TEXT,
    "username" TEXT NOT NULL,
    "resetToken" TEXT,
    "resetTokenExpiry" TIMESTAMP(3),

    CONSTRAINT "Classes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClassesAbout" (
    "id" TEXT NOT NULL,
    "birthDate" TIMESTAMP(3) NOT NULL,
    "catchyHeadline" TEXT,
    "tutorBio" TEXT,
    "profilePhoto" TEXT,
    "classesLogo" TEXT,
    "videoUrl" TEXT,
    "classId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClassesAbout_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminUser" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AdminUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClassesExpereince" (
    "id" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "certificateUrl" TEXT NOT NULL,
    "from" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClassesExpereince_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClassesEducation" (
    "id" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "university" TEXT NOT NULL,
    "degree" TEXT NOT NULL,
    "degreeType" TEXT NOT NULL,
    "passoutYear" TEXT NOT NULL,
    "certificate" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ClassesEducation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClassesCertificates" (
    "id" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "certificateUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ClassesCertificates_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClassesStatus" (
    "id" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "status" "ClassApprovalStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ClassesStatus_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UestCoins" (
    "id" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "modelType" TEXT NOT NULL,
    "coins" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UestCoins_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UestCoinTransaction" (
    "id" TEXT NOT NULL,
    "modelId" TEXT NOT NULL,
    "modelType" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "type" "TransactionType" NOT NULL,
    "reason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UestCoinTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TuitionClass" (
    "id" TEXT NOT NULL,
    "classId" TEXT NOT NULL,
    "education" TEXT NOT NULL,
    "boardType" TEXT,
    "subject" TEXT,
    "medium" TEXT,
    "section" TEXT,
    "coachingType" TEXT NOT NULL,
    "pricingPerMonth" DOUBLE PRECISION NOT NULL,
    "pricingPerCourse" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TuitionClass_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ClassesTimeSlot" (
    "id" TEXT NOT NULL,
    "tuitionClassId" TEXT NOT NULL,
    "from" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClassesTimeSlot_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ConstantCategory" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "ConstantCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ConstantDetail" (
    "id" SERIAL NOT NULL,
    "value" TEXT NOT NULL,
    "categoryId" INTEGER NOT NULL,

    CONSTRAINT "ConstantDetail_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Classes_email_key" ON "Classes"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Classes_username_key" ON "Classes"("username");

-- CreateIndex
CREATE UNIQUE INDEX "ClassesAbout_classId_key" ON "ClassesAbout"("classId");

-- CreateIndex
CREATE UNIQUE INDEX "AdminUser_email_key" ON "AdminUser"("email");

-- CreateIndex
CREATE UNIQUE INDEX "ClassesStatus_classId_key" ON "ClassesStatus"("classId");

-- CreateIndex
CREATE UNIQUE INDEX "UestCoins_modelId_modelType_key" ON "UestCoins"("modelId", "modelType");

-- AddForeignKey
ALTER TABLE "ClassesAbout" ADD CONSTRAINT "ClassesAbout_classId_fkey" FOREIGN KEY ("classId") REFERENCES "Classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClassesExpereince" ADD CONSTRAINT "ClassesExpereince_classId_fkey" FOREIGN KEY ("classId") REFERENCES "Classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClassesEducation" ADD CONSTRAINT "ClassesEducation_classId_fkey" FOREIGN KEY ("classId") REFERENCES "Classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClassesCertificates" ADD CONSTRAINT "ClassesCertificates_classId_fkey" FOREIGN KEY ("classId") REFERENCES "Classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClassesStatus" ADD CONSTRAINT "ClassesStatus_classId_fkey" FOREIGN KEY ("classId") REFERENCES "Classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TuitionClass" ADD CONSTRAINT "TuitionClass_classId_fkey" FOREIGN KEY ("classId") REFERENCES "Classes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClassesTimeSlot" ADD CONSTRAINT "ClassesTimeSlot_tuitionClassId_fkey" FOREIGN KEY ("tuitionClassId") REFERENCES "TuitionClass"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ConstantDetail" ADD CONSTRAINT "ConstantDetail_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "ConstantCategory"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
