import { Router } from 'express';
import {
  addToWishlist,
  removeFromWishlist,
  checkWishlistStatus,
  getWishlist
} from '../controllers/studentWishlistController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';

const studentWishlistRouter = Router();
studentWishlistRouter.use(studentAuthMiddleware);

studentWishlistRouter.post('/', addToWishlist);
studentWishlistRouter.delete('/:id', removeFromWishlist);
studentWishlistRouter.get('/check/:classId', checkWishlistStatus);
studentWishlistRouter.get('/', getWishlist);

export default studentWishlistRouter;
