import { axiosInstance } from '@/lib/axios';

interface QuizState {
  questions: any[];
  currentQuestionIndex: number;
  userAnswers: any[];
}

export const uwhizQuestionForStudent = async (
  studentId: string,
  medium: string,
  standard: string,
  examId: string
): Promise<any[]> => {
  try {
    const response = await axiosInstance.get(
      `questionForStudentRouter?studentId=${studentId}&medium=${medium}&standard=${standard}&examId=${examId}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(
      `Failed To Get Student Question Detail: ${error.response?.data?.message || error.message}`
    );
  }
};

export const getQuizState = async (studentId: string, examId: string): Promise<QuizState | null> => {
  try {
    const response = await axiosInstance.get(
      `questionForStudentRouter/state?studentId=${studentId}&examId=${examId}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(
      `Failed To Get Quiz State: ${error.response?.data?.message || error.message}`
    );
  }
};

export const updateQuizState = async (
  studentId: string,
  examId: string,
  state: Partial<QuizState>
): Promise<void> => {
  try {
    await axiosInstance.post(
      `questionForStudentRouter/state?studentId=${studentId}&examId=${examId}`,
      state,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
  } catch (error: any) {
    throw new Error(
      `Failed To Update Quiz State: ${error.response?.data?.message || error.message}`
    );
  }
};

export const clearQuizState = async (studentId: string, examId: string): Promise<void> => {
  try {
    await axiosInstance.delete(
      `questionForStudentRouter/state?studentId=${studentId}&examId=${examId}`,
      {
        headers: {
          "Server-Select": "uwhizServer",
        },
      }
    );
  } catch (error: any) {
    throw new Error(
      `Failed To Clear Quiz State: ${error.response?.data?.message || error.message}`
    );
  }
};