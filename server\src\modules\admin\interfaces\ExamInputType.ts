import { Exam } from '@prisma/client';

// Define the type for creating/updating an exam
export interface ExamInputType {
  exam_name: string;
  start_date: Date | string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: string;
  coins_required: number;
  max_classes_can_join: number;
  max_questions_class_can_apply: number;
  exam_type: 'CLASSES' | 'STUDENTS';
  start_registration_date: Date | string;
}

// Define pagination response type
export interface PaginationResponse {
  exams: Exam[];
  total: number;
  currentPage: number;
  totalPages: number;
}
