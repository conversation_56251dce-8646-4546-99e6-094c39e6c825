'use client';

import * as React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  addMonths,
  subMonths,
  isSameMonth,
  isSameDay,
  isToday
} from 'date-fns';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface CalendarProps {
  className?: string;
  selected?: Date;
  onSelect?: (date: Date) => void;
  disabled?: (date: Date) => boolean;
  mode?: 'single' | 'range';
  month?: Date;
  onMonthChange?: (date: Date) => void;
  fromYear?: number;
  toYear?: number;
  captionLayout?: 'buttons' | 'dropdown';
  initialFocus?: boolean;
  classNames?: Record<string, string>;
}

function Calendar({
  className,
  selected,
  onSelect,
  disabled,
  month,
  onMonthChange,
  fromYear,
  toYear,
  captionLayout = 'buttons',
  classNames,
  ...props
}: CalendarProps) {
  const [currentMonth, setCurrentMonth] = React.useState(month || selected || new Date());

  React.useEffect(() => {
    if (month) {
      setCurrentMonth(month);
    }
  }, [month]);

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart);
  const endDate = endOfWeek(monthEnd);

  const dateFormat = 'MMMM yyyy';
  const rows = [];
  let days = [];
  let day = startDate;
  let formattedDate = '';

  // Generate calendar days
  while (day <= endDate) {
    for (let i = 0; i < 7; i++) {
      formattedDate = format(day, 'd');
      const cloneDay = day;

      days.push(
        <div
          key={day.toString()}
          className={cn(
            'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 cursor-pointer',
            'h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent hover:text-accent-foreground',
            {
              'text-muted-foreground': !isSameMonth(day, monthStart),
              'bg-primary text-primary-foreground': selected && isSameDay(day, selected),
              'bg-accent text-accent-foreground': isToday(day) && (!selected || !isSameDay(day, selected)),
              'opacity-50 cursor-not-allowed': disabled && disabled(day),
            }
          )}
          onClick={() => {
            if (!disabled || !disabled(cloneDay)) {
              onSelect?.(cloneDay);
            }
          }}
        >
          <span className="font-normal">{formattedDate}</span>
        </div>
      );
      day = addDays(day, 1);
    }
    rows.push(
      <div className="flex w-full mt-2" key={day.toString()}>
        {days}
      </div>
    );
    days = [];
  }

  const nextMonth = () => {
    const newMonth = addMonths(currentMonth, 1);
    setCurrentMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  const prevMonth = () => {
    const newMonth = subMonths(currentMonth, 1);
    setCurrentMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonth = new Date(currentMonth.getFullYear(), parseInt(e.target.value), 1);
    setCurrentMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonth = new Date(parseInt(e.target.value), currentMonth.getMonth(), 1);
    setCurrentMonth(newMonth);
    onMonthChange?.(newMonth);
  };

  return (
    <div className={cn('p-3', className)} {...props}>
      <div className="flex flex-col gap-4">
        {/* Header */}
        <div className={cn('flex justify-center pt-1 relative items-center w-full', classNames?.caption)}>
          {captionLayout === 'dropdown' ? (
            <div className="flex gap-2">
              <select
                value={currentMonth.getMonth()}
                onChange={handleMonthChange}
                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <option key={i} value={i}>
                    {format(new Date(2000, i, 1), 'MMMM')}
                  </option>
                ))}
              </select>
              <select
                value={currentMonth.getFullYear()}
                onChange={handleYearChange}
                className={cn('mx-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-2 py-1 text-sm text-black dark:text-white', classNames?.dropdown)}
              >
                {Array.from({ length: (toYear || new Date().getFullYear()) - (fromYear || 1950) + 1 }, (_, i) => {
                  const year = (fromYear || 1950) + i;
                  return (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  );
                })}
              </select>
            </div>
          ) : (
            <>
              <Button
                variant="outline"
                size="sm"
                className="absolute left-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100"
                onClick={prevMonth}
              >
                <ChevronLeft className="size-4" />
              </Button>
              <div className={cn('text-sm font-medium', classNames?.caption_label)}>
                {format(currentMonth, dateFormat)}
              </div>
              <Button
                variant="outline"
                size="sm"
                className="absolute right-1 size-7 bg-transparent p-0 opacity-50 hover:opacity-100"
                onClick={nextMonth}
              >
                <ChevronRight className="size-4" />
              </Button>
            </>
          )}
        </div>

        {/* Calendar Grid */}
        <div className="w-full border-collapse space-x-1">
          {/* Days of week header */}
          <div className="flex">
            {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
              <div
                key={day}
                className="text-muted-foreground rounded-md w-8 font-normal text-[0.8rem] text-center"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar rows */}
          {rows}
        </div>
      </div>
    </div>
  );
}

export { Calendar };
