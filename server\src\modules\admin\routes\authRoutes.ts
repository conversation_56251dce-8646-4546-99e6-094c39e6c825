import { Router } from 'express';
import { login, logout } from '../controllers/authController';
import { adminLoginSchema } from '../requests/authRequest';
import validateRequest from '@/middlewares/validateRequest';
import { sendMailHandler } from '../controllers/emailController';

const authAdminRouter = Router();

authAdminRouter.post('/login', validateRequest(adminLoginSchema), login);
authAdminRouter.post('/send-email', sendMailHandler);
authAdminRouter.post('/logout', logout);

export default authAdminRouter;
