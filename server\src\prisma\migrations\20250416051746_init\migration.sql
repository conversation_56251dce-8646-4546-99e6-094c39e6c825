/*
  Warnings:

  - You are about to drop the column `pricingPerCourse` on the `TuitionClass` table. All the data in the column will be lost.
  - You are about to drop the column `pricingPerMonth` on the `TuitionClass` table. All the data in the column will be lost.
  - You are about to drop the `ClassesTimeSlot` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ClassesTimeSlot" DROP CONSTRAINT "ClassesTimeSlot_tuitionClassId_fkey";

-- AlterTable
ALTER TABLE "ClassesCertificates" ADD COLUMN     "isCertificate" BOOLEAN NOT NULL DEFAULT true,
ALTER COLUMN "title" DROP NOT NULL,
ALTER COLUMN "certificateUrl" DROP NOT NULL;

-- AlterTable
ALTER TABLE "ClassesEducation" ADD COLUMN     "isDegree" BOOLEAN NOT NULL DEFAULT false,
ALTER COLUMN "university" DROP NOT NULL,
ALTER COLUMN "degree" DROP NOT NULL,
ALTER COLUMN "degreeType" DROP NOT NULL,
ALTER COLUMN "passoutYear" DROP NOT NULL,
ALTER COLUMN "certificate" DROP NOT NULL;

-- AlterTable
ALTER TABLE "ClassesExpereince" ADD COLUMN     "isExperience" BOOLEAN NOT NULL DEFAULT true,
ALTER COLUMN "title" DROP NOT NULL,
ALTER COLUMN "certificateUrl" DROP NOT NULL,
ALTER COLUMN "from" DROP NOT NULL,
ALTER COLUMN "to" DROP NOT NULL;

-- AlterTable
ALTER TABLE "TuitionClass" DROP COLUMN "pricingPerCourse",
DROP COLUMN "pricingPerMonth",
ALTER COLUMN "details" DROP NOT NULL;

-- DropTable
DROP TABLE "ClassesTimeSlot";
