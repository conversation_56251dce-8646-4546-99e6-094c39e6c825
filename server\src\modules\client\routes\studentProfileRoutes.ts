import express, { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import {
  createStudentProfileController,
  getStudentProfileController,
  updateStudentProfileController,
  deleteStudentProfileController,
  getClassroomOptionsController,
  updateStudentProfileStatusController,
  getStudentProfileByIdController,
  updateStudentProfileStatusByAdminController,
  getStudentAllDataController,
  updateStudentAndProfileController,
  getStudentAllDataForAdminController
} from '../controllers/studentProfileController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';
import { dynamicStorage } from '@/utils/upload';
import validateRequest from '@/middlewares/validateRequest';
import { sendError } from '@/utils/response';
import {
  createStudentProfileSchema,
  updateStudentProfileSchema,
  updateStudentProfileStatusSchema,
  updateStudentAndProfileSchema
} from '../requests/studentProfileRequest';

const studentProfileRoutes = express.Router();

// Handle multer errors
const handleMulterError = (err: any, _req: Request, res: Response, next: NextFunction) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return sendError(res, 'File size exceeds 5MB limit', 400);
    } else if (err.code === 'LIMIT_UNEXPECTED_FILE') {
      return sendError(res, 'Unexpected file field. Only photo and document are allowed', 400);
    }
    return sendError(res, `Upload error: ${err.message}`, 400);
  } else if (err) {
    return sendError(res, err.message || 'File upload error', 400);
  }
  next(err);
};

// File type validation
const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.fieldname === 'photo') {
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Profile photo must be an image file (JPEG, PNG, etc.)'));
    }
  } else if (file.fieldname === 'document') {
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Document must be PDF or image file (PDF, JPEG, PNG)'));
    }
  }
  cb(null, true);
};

// Configure multer for file uploads
const upload = multer({
  ...dynamicStorage({ folder: '', studentIdKey: 'id' }),
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter
});

// File upload middleware
const handleFileUpload = (req: Request, res: Response, next: NextFunction) => {
  upload.fields([
    { name: 'photo', maxCount: 1 },
    { name: 'document', maxCount: 1 }
  ])(req, res, (err) => {
    if (err) {
      return handleMulterError(err, req, res, next);
    }
    next();
  });
};

// Student profile routes
studentProfileRoutes.post(
  '/',
  studentAuthMiddleware,
  handleFileUpload,
  validateRequest(createStudentProfileSchema),
  createStudentProfileController
);

studentProfileRoutes.get('/', studentAuthMiddleware, getStudentProfileController);

studentProfileRoutes.put(
  '/',
  studentAuthMiddleware,
  handleFileUpload,
  validateRequest(updateStudentProfileSchema),
  updateStudentProfileController
);

studentProfileRoutes.delete('/', studentAuthMiddleware, deleteStudentProfileController);

studentProfileRoutes.patch(
  '/status',
  studentAuthMiddleware,
  validateRequest(updateStudentProfileStatusSchema),
  updateStudentProfileStatusController
);

studentProfileRoutes.get('/classroom-options', studentAuthMiddleware, getClassroomOptionsController);

// Optimized endpoints
studentProfileRoutes.get('/all-data', studentAuthMiddleware, getStudentAllDataController);
studentProfileRoutes.put(
  '/combined',
  studentAuthMiddleware,
  validateRequest(updateStudentAndProfileSchema),
  updateStudentAndProfileController
);

// Admin routes
studentProfileRoutes.get('/admin/:studentId', authMiddleware, getStudentProfileByIdController);
studentProfileRoutes.get('/admin/:studentId/all-data', authMiddleware, getStudentAllDataForAdminController);
studentProfileRoutes.patch(
  '/admin/:studentId/status',
  authMiddleware,
  validateRequest(updateStudentProfileStatusSchema),
  updateStudentProfileStatusByAdminController
);
studentProfileRoutes.put(
  '/admin/:studentId/combined',
  authMiddleware,
  validateRequest(updateStudentAndProfileSchema),
  updateStudentAndProfileController
);

export default studentProfileRoutes;
