import prisma from "@/config/prismaClient";
import { Request, Response } from "express";
import { createHmac } from "crypto";
import { initRazorpay } from "@/utils/helper";
import { transporter } from "@/utils/email";
import { createCoinPurchaseTemplate } from "@/utils/emailTemplates";


export const getCoins = async (req: Request, res: Response): Promise<any> => {
  const classId = req.class?.id;
  const studentId = req.student?.id;

  try {
    const totalCoins = await prisma.uestCoins.aggregate({
      where: {
        modelId: classId ? classId : studentId,
        modelType: classId ? "CLASS" : "STUDENT",
      },
      _sum: {
        coins: true,
      },
    });

    return res.status(200).json({
      coins: totalCoins._sum.coins || 0,
    });
  } catch (error) {
    console.error("Error fetching coins:", error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};

export const createOrder = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { amount } = req.body;
    if (!amount || amount > 1000000)
      return res.status(400).json({ error: "Invalid amount" });

    const razorpay = initRazorpay();

    const order = await razorpay.orders.create({
      amount,
      currency: "INR",
      receipt: `receipt_${Date.now()}`,
    });

    res.json({ order });
  } catch (error) {
    console.error("Error fetching coins:", error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};

export const verify = async (req: Request, res: Response): Promise<any> => {
  const { razorpay_order_id, razorpay_payment_id, razorpay_signature, amount } =
    req.body;

  const generatedSignature = createHmac(
    "sha256",
    process.env.RAZORPAY_KEY_SECRET!
  )
    .update(`${razorpay_order_id}|${razorpay_payment_id}`)
    .digest("hex");

  if (generatedSignature !== razorpay_signature) {
    return res.status(400).json({ error: "Invalid signature" });
  }

  const classId = req.class?.id;
  const studentId = req.student?.id;
  const userId = classId || studentId || "";
  const modelType = classId ? "CLASS" : "STUDENT";

  try {
    // Add coins to user
    const updatedCoins = await prisma.uestCoins.upsert({
      where: { modelId_modelType: { modelId: userId, modelType } },
      update: { coins: { increment: amount / 100 } },
      create: {
        modelId: userId,
        modelType,
        coins: amount / 100,
      },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId: userId,
        modelType,
        type: "CREDIT",
        amount: amount / 100,
        reason: "Coins added via Razorpay " + razorpay_order_id,
      },
    });

    // Get user details for email
    let userName = "";
    let userEmail = "";

    if (modelType === "STUDENT") {
      const studentDetails = await prisma.student.findUnique({
        where: { id: userId },
        select: {
          firstName: true,
          lastName: true,
          email: true,
        },
      });

      if (studentDetails) {
        userName = `${studentDetails.firstName} ${studentDetails.lastName}`;
        userEmail = studentDetails.email;
      }
    } else {
      const classDetails = await prisma.classes.findUnique({
        where: { id: userId },
        select: {
          firstName: true,
          lastName: true,
          email: true,
          className: true,
        },
      });

      if (classDetails) {
        userName = classDetails.className || `${classDetails.firstName} ${classDetails.lastName}`;
        userEmail = classDetails.email;
      }
    }

    if (userEmail) {
      // Create and send email
      const emailHtml = createCoinPurchaseTemplate({
        name: userName,
        email: userEmail,
        userType: modelType,
        amountPaid: amount / 100,
        coinsAdded: amount / 100,
        totalCoins: updatedCoins.coins,
        paymentId: razorpay_payment_id,
        orderId: razorpay_order_id,
      });

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: userEmail,
        subject: "Uest Coin Purchase Confirmation",
        html: emailHtml,
      });
    }

    res.json({ success: true });
  } catch (error) {
    console.error("Error processing coin purchase:", error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};

export const verifyMobile = async (req: Request, res: Response): Promise<any> => {
  const { razorpay_order_id, razorpay_payment_id, amount } =
    req.body;

  const classId = req.class?.id;
  const studentId = req.student?.id;
  const userId = classId || studentId || "";
  const modelType = classId ? "CLASS" : "STUDENT";

  try {
    // Add coins to user
    const updatedCoins = await prisma.uestCoins.upsert({
      where: { modelId_modelType: { modelId: userId, modelType } },
      update: { coins: { increment: amount / 100 } },
      create: {
        modelId: userId,
        modelType,
        coins: amount / 100,
      },
    });

    await prisma.uestCoinTransaction.create({
      data: {
        modelId: userId,
        modelType,
        type: "CREDIT",
        amount: amount / 100,
        reason: "Coins added via Razorpay " + razorpay_order_id,
      },
    });

    // Get user details for email
    let userName = "";
    let userEmail = "";

    if (modelType === "STUDENT") {
      const studentDetails = await prisma.student.findUnique({
        where: { id: userId },
        select: {
          firstName: true,
          lastName: true,
          email: true,
        },
      });

      if (studentDetails) {
        userName = `${studentDetails.firstName} ${studentDetails.lastName}`;
        userEmail = studentDetails.email;
      }
    } else {
      const classDetails = await prisma.classes.findUnique({
        where: { id: userId },
        select: {
          firstName: true,
          lastName: true,
          email: true,
          className: true,
        },
      });

      if (classDetails) {
        userName = classDetails.className || `${classDetails.firstName} ${classDetails.lastName}`;
        userEmail = classDetails.email;
      }
    }

    if (userEmail) {
      // Create and send email
      const emailHtml = createCoinPurchaseTemplate({
        name: userName,
        email: userEmail,
        userType: modelType,
        amountPaid: amount / 100,
        coinsAdded: amount / 100,
        totalCoins: updatedCoins.coins,
        paymentId: razorpay_payment_id,
        orderId: razorpay_order_id,
      });

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: userEmail,
        subject: "Uest Coin Purchase Confirmation",
        html: emailHtml,
      });
    }

    res.json({ success: true });
  } catch (error) {
    console.error("Error processing coin purchase:", error);
    return res.status(500).json({
      message: "Internal server error",
    });
  }
};